var tempthem = "them";
var tempthemCapPhoi = "them";
var tempTrangThai = "32";
var ChuaCoThongTin = "";
///////// PHÍM TẮT /////////
var hotKey = 0; // 1 thêm\
var currentAvatarPath = "";
const today = new Date();
const dd = String(today.getDate()).padStart(2, '0');
const mm = String(today.getMonth() + 1).padStart(2, '0');
const yyyy = today.getFullYear();
const defaultDate = `${dd}/${mm}/${yyyy}`;
var selectedId;

$(function () {
    $(document).on("keydown", function (e) {
        switch (e.keyCode) {
            case 113:
                if (hotKey == 0) $("#btnThemMoi").trigger("click");
                e.preventDefault();
                break;
            case 114:
                if (hotKey == 0) $(".nav-search-input").focus();
                e.preventDefault();
                break;
            case 115:
                if (hotKey == 1) $("#mdThemMoi").modal("hide");
                e.preventDefault();
                break;
            case 120:
                if (hotKey == 1) $("#btnLuuVaDong").trigger("click");
                e.preventDefault();
                break;
        }
    });

    LoadDataComBo();
    LoadDataComBo_Loc();
    LoadDataTable();
    LoadDataComBo_GuiDonXinCapPhoiBang();
    LoadDataComBo_CapPhoi();
});

let commonComboConfig = {
    columns: 2,
    indexValue: 0,
    indexText: 1, // assuming your result rows are [id, code, name]
    indexText1: 2, // assuming your result rows are [id, code, name]
    textShowTatCa: "-Tất cả-",
    showTatCa: true,
};

function LoadDataComBo() {
    NTS.loadDataComboAsync({
        name: "#NguoiLapID",
        type: "GET",
        ajaxUrl: window.Laravel.local.comboNhanVien,
        ajaxParam: {},
        ...commonComboConfig
    });
    NTS.loadDataComboAsync({
        name: "#CapHocID",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListCapHoc,
        ajaxParam: {},
        ...commonComboConfig
    });
    NTS.loadDataComboAsync({
        name: "#ChucVuNguoiLapID",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListChucVu,
        ajaxParam: {},
        ...commonComboConfig
    });
    NTS.loadDataComboAsync({
        name: "#LoaiPhoiID",
        type: "POST",
        ajaxUrl: window.Laravel.local.getloaiphoivanbangchungchi,
        ajaxParam: {},
        columns: 2,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: "-Tất cả-",
        showTatCa: true,
    });
    NTS.loadDataComboAsync({
        name: "#DonViNhanID",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListDonVi,
        ajaxParam: {},
        ...commonComboConfig
    });

}

function LoadDataComBo_Loc() {


    NTS.loadDataComboAsync({
        name: "#LoaiPhoiID_Loc",
        type: "POST",
        ajaxUrl: window.Laravel.local.getloaiphoivanbangchungchi,
        ajaxParam: {},
        columns: 2,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: "-Tất cả-",
        showTatCa: true,
    });

    NTS.loadDataComboAsync({
        name: "#TrangThaiXuLyID_Loc",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListTrangThaiCapBang,
        ajaxParam: {},
        ...commonComboConfig,
        indexValue: 1,

    });
}

function LoadDataComBo_GuiDonXinCapPhoiBang() {
    NTS.loadDataComboAsync({
        name: "#NguoiTiepNhanXuLyID",
        type: "GET",
        ajaxUrl: window.Laravel.local.comboNhanVien,
        ajaxParam: {},
        ...commonComboConfig,
        textShowTatCa: "-Chọn-",
    });
    NTS.loadDataComboAsync({
        name: "#ChucVuNguoiTiepNhanXuLyID",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListChucVu,
        ajaxParam: {},
        ...commonComboConfig,
        textShowTatCa: "-Chọn-",
    });
}

function LoadDataComBo_CapPhoi() {
    NTS.loadDataComboAsync({
        name: "#CapHocID_Loc",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListCapHoc,
        ajaxParam: {},
        ...commonComboConfig
    });

    NTS.loadDataComboAsync({
        name: "#LoaiPhoiVanBangChungChiID",
        type: "POST",
        ajaxUrl: window.Laravel.local.getloaiphoivanbangchungchi,
        ajaxParam: {},
        columns: 2,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: "-Chọn-",
        showTatCa: true,
    });

    NTS.loadDataComboAsync({
        name: "#DonViID_Cap, #DonViID_Nhap",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListDonVi,
        ajaxParam: {},
        ...commonComboConfig
    });
    NTS.loadDataComboAsync({
        name: "#ChucVuID",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListChucVu,
        ajaxParam: {},
        ...commonComboConfig
    });
    NTS.loadDataComboAsync({
        name: "#NhanVienID_Nhap",
        type: "GET",
        ajaxUrl: window.Laravel.local.comboNhanVien,
        ajaxParam: {},
        columns: 1,
        indexValue: 0,
        indexText: 2,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });

}

$(document).on("click", "#TimKiemNangCao", function () {
    if ($("#KhungTimKiem").css("display") == "block") {
        $("#KhungTimKiem").slideUp(200);
    } else {
        $("#KhungTimKiem").slideDown(200);
    }
    return false;
});
$(document).on("click", "#DongTimKiem", function () {
    $("#KhungTimKiem").slideUp(200);
    return false;
});

$(document).on("click", "#TimKiem", async function () {
    $("#KhungTimKiem").slideUp(200);
    await LoadDataTable();
    await LoadDataTable2();
    return false;
});

$(document).on("keyup", "#SearchKey", async function (e) {
    if (e.keyCode == "13") {
        await LoadDataTable();
        $("#KhungTimKiem").slideUp(200);
        return false;
    }
});

$(document).on("click", "#btnAnHienTQ", function () {
    var divThongKe = document.getElementById("DivThongKe");
    var textAnHien = document.getElementById("textAnHien");
    var iconAnHien = document.getElementById("iconAnHien");
    if (iconAnHien.className == "fa fa-eye-slash") {
        divThongKe.classList.add("d-none");
        iconAnHien.className = "fa fa-eye";
        textAnHien.textContent = " Hiện trang tổng quan";
    } else {
        divThongKe.classList.remove("d-none");
        iconAnHien.className = "fa fa-eye-slash";
        textAnHien.textContent = " Ẩn trang tổng quan";
    }
});

function XemChiTietNhatKy(id) {
    $("#DonYeuCauID").val(id);
    selectedId = id;
    $.ajax({
        url: window.location.pathname + '/loadDuLieuSua',
        method: 'GET',
        data: { id: id },
        success: function (res) {
            const configByTrangThai = {
                40: {
                    labelNgay: 'Ngày đề nghị    ',
                    labelNguoi: 'Người đề nghị',
                    labelChucVu: 'Chức vụ người đề nghị',
                    labelNoiDung: 'Nội dung đề nghị',

                },
                41: {
                    labelNgay: 'Ngày gửi',
                    labelNguoi: 'Người gửi',
                    labelChucVu: 'Chức vụ người gửi',
                    labelNoiDung: 'Nội dung gửi',

                },
                42: {
                    labelNgay: 'Ngày từ chối',
                    labelNguoi: 'Người từ chối',
                    labelChucVu: 'Chức vụ người từ chối',
                    labelNoiDung: 'Nội dung từ chối',

                },

                32: {
                    labelNgay: 'Ngày phê duyệt',
                    labelNguoi: 'Người phê duyệt',
                    labelChucVu: 'Chức vụ',
                    labelNoiDung: 'Nội dung phê duyệt',

                },
                default: {
                    labelNgay: 'Ngày xử lý',
                    labelNguoi: 'Người xử lý',
                    labelChucVu: 'Chức vụ',
                    labelNoiDung: 'Nội dung',

                }
            };
            if (!res.Err && res.Result) {
                var data = res.Result;
                if (window.Laravel && window.Laravel.local && window.Laravel.local.linkAnhDonXin) {
                    $('#dxcpb_imgDonXin').attr('src', window.Laravel.local.linkAnhDonXin);
                }
                $('#dxcpb_SoPhieu_ct').text(data.SoPhieu || '');
                $('#dxcpb_NgayLap_ct').text(data.txtNgayLap || '');
                $('#dxcpb_NguoiLap_ct').text(data.TenNguoiLap || '');
                $('#dxcpb_ChucVuNguoiLap_ct').text(data.TenChucVuNguoiLap || '');
                $('#dxcpb_DonViGui_ct').text(data.TenDonViGui || '');
                $('#dxcpb_NguoiLienHe_ct').text(data.NguoiLienHe || '');
                $('#dxcpb_SoDienThoai_ct').text(data.SoDienThoai || '');
                $('#dxcpb_Email_ct').text(data.Email || '');
                $('#dxcpb_DiaChi_ct').text(data.DiaChi || '');
                $('#dxcpb_HinhThucNhanPhoi_ct').text(data.TenHinhThucNhanPhoi || '');
                $('#dxcpb_DonViNhan_ct').text((data.TenDonViNhan || '') + (data.MaDonViNhan ? ` (${data.MaDonViNhan})` : ''));
                $('#dxcpb_LyDoXinCap_ct').text(data.LyDoXinCap || '');
                $('#dxcpb_TrangThai_ct')
                    .text(data.TenTrangThaiXuLy || 'Chưa rõ')
                    .css('background-color', data.MauSacTrangThaiXuLy || '#ccc');

                $('#dxcpb_CacMinhChung_ct').text(data.MinhChungKemTheo || '');
                $('#txtDinhKem').html(`<p class="fs-big my-1">
                            Đính kèm:
                            <a href="#" data="" onclick="XemDinhKem_us('`+ data.DinhKem + `')">
                                <i class="fa fa-paperclip me-1"></i> Xem đính kèm
                            </a>
                            </p> `)
                $('#dxcpb_GhiChu_ct').text(data.GhiChu || '');
                var labelNgay = 'Ngày xử lý', labelNguoi = 'Người xử lý', labelChucVu = 'Chức vụ', labelNoiDung = 'Nội dung';
                if (data.TrangThaiXuLyID == 40) {
                    labelNgay = 'Ngày đề nghị';
                    labelNguoi = 'Người đề nghị';
                    labelChucVu = 'Chức vụ người đề nghị';
                    labelNoiDung = 'Nội dung đề nghị';
                    labelDonViTiepNhan = 'Đơn vị tiếp nhận';
                } else if (data.TrangThaiXuLyID == 41) {
                    labelNgay = 'Ngày gửi';
                    labelNguoi = 'Người gửi';
                    labelChucVu = 'Chức vụ người gửi';
                    labelNoiDung = 'Nội dung gửi';
                } else if (data.TrangThaiXuLyID == 42) {
                    labelNgay = 'Ngày từ chối';
                    labelNguoi = 'Người từ chối';
                    labelChucVu = 'Chức vụ người từ chối';
                    labelNoiDung = 'Lý do từ chối';
                }

                const tbody = document.querySelector('#phoiVBCCTable_ct tbody');
                tbody.innerHTML = '';
                let count = 1;
                data.PhoiVBCC.forEach(item => {
                    const tr = document.createElement('tr');

                    const tenPhoi = item.TenLoaiPhoiVanBangChungChi;
                    const donViTinh = item.TenDonViTinh;
                    const soLuong = item.SoLuongPhoi;

                    tr.innerHTML = `
                    <td>${count}</td>
                    <td>${tenPhoi}</td>
                    <td>${donViTinh}</td>
                    <td>${soLuong}</td>
                `;

                    tbody.appendChild(tr);
                    count++;
                });

                const config = configByTrangThai[parseInt(data.TrangThaiXuLyID)] || configByTrangThai.default;

                $('#dxcpb_LabelNgay_ct').html('<b>' + (data.txtNgayXuLy || '') + '</b>');
                $('#dxcpb_LabelNguoi_ct').html('<b>' + (data.TenNguoiXuLy || '') + '</b>');
                $('#dxcpb_LabelChucVu_ct').html('<b>' + (data.TenChucVuNguoiXuLy || '') + '</b>');
                $('#dxcpb_LabelNoiDung_ct').html('<b>' + (data.NoiDungXuLy || '') + '</b>');

                $('#dxcpb_LabelNgayTiepNhanXuLy_ct').text(config.labelNgay + ': ');
                $('#dxcpb_NgayTiepNhanXuLy_ct').text(data.txtNgayTiepNhanXuLy || '');

                $('#dxcpb_LabelNguoiTiepNhanXuLy_ct').text(config.labelNguoi + ': ');
                $('#dxcpb_NguoiTiepNhanXuLy_ct').text(data.TenNguoiTiepNhanXuLy || '');

                $('#dxcpb_LabelChucVuNguoiTiepNhanXuLy_ct').text(config.labelChucVu + ': ');
                $('#dxcpb_ChucVuNguoiTiepNhanXuLy_ct').text(data.TenChucVuNguoiTiepNhanXuLy || '');

                $('#dxcpb_LabelNoiDungTiepNhan_ct').text(config.labelNoiDung + ': ');
                $('#dxcpb_NoiDungTiepNhanXuLy_ct').text(data.NoiDungTiepNhanXuLy || '');

                $('#dxcpb_DonViTiepNhanXuLyID_ct').text(data.TenDonViTiepNhanXuLy || '');

            } else {
                $('#dxcpb_imgDonXin').attr('src', window.Laravel && window.Laravel.local ? window.Laravel.local.linkAnhDonXin : '');
                $('#dxcpb_TrangThaiLabel').text('').css('background-color', '#ccc');
                $('#dxcpb_NgayLap_ct, #dxcpb_NguoiLap_ct, #dxcpb_ChucVuNguoiLap_ct, #dxcpb_DonViGui_ct, #dxcpb_LoaiPhoi_ct, #dxcpb_SoLuong_ct, #dxcpb_DonViNhan_ct, #dxcpb_LyDoXinCap_ct, #dxcpb_DinhKem_ct, #dxcpb_GhiChu_ct, #dxcpb_LabelNgay_ct, #dxcpb_LabelNguoi_ct, #dxcpb_LabelChucVu_ct, #dxcpb_LabelNoiDung_ct').text('');
            }
            $('#mdChiTietQD').modal('show');
        },
        error: function () {
            $('#dxcpb_imgDonXin').attr('src', window.Laravel && window.Laravel.local ? window.Laravel.local.linkAnhDonXin : '');
            $('#dxcpb_TrangThaiLabel').text('').css('background-color', '#ccc');
            $('#dxcpb_NgayLap_ct, #dxcpb_NguoiLap_ct, #dxcpb_ChucVuNguoiLap_ct, #dxcpb_DonViGui_ct, #dxcpb_LoaiPhoi_ct, #dxcpb_SoLuong_ct, #dxcpb_DonViNhan_ct, #dxcpb_LyDoXinCap_ct, #dxcpb_DinhKem_ct, #dxcpb_GhiChu_ct, #dxcpb_LabelNgay_ct, #dxcpb_LabelNguoi_ct, #dxcpb_LabelChucVu_ct, #dxcpb_LabelNoiDung_ct').text('');
            $('#mdChiTietQD').modal('show');
        }
    });
}

function LoadDataComBo_Loc() {
    NTS.loadDataComboAsync({
        name: "#CapHocID_Loc",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListCapHoc,
        ajaxParam: {},
        ...commonComboConfig

    });

    NTS.loadDataComboAsync({
        name: "#LoaiPhoiID_Loc",
        type: "POST",
        ajaxUrl: window.Laravel.local.getloaiphoivanbangchungchi,
        ajaxParam: {},
        columns: 2,
        indexValue: 2,
        indexText: 0,
        indexText1: 1,
        textShowTatCa: "-Tất cả-",
        showTatCa: true,
    });

    NTS.loadDataComboAsync({
        name: "#TrangThaiXuLyID_Loc",
        type: "GET",
        ajaxUrl: window.Laravel.local.getListTrangThaiCapBang,
        ajaxParam: {},
        ...commonComboConfig,
        indexValue: 1,

    });
}

function htmlDuLieu(cell) {
    const data = cell.getData();

    const fileName = data.TenTepDinhKem || "Không rõ";
    const fileLink = data.LinkDinhKem || "#";
    const dinhKemHTML = data.LinkDinhKem
        ? `<a href="${fileLink}" target="_blank">${fileName}</a>`
        : "Không có";
    let labelNgay, labelNguoi, labelChucVu, labelNoiDung;
    let txtNgay = data.txtNgayTiepNhanXuLy || "";
    let tenNguoi = data.TenNguoiTiepNhanXuLy || "";
    let chucVu = data.TenChucVuNguoiTiepNhanXuLy || "";
    let noiDung = data.NoiDungTiepNhanXuLy || "";
    switch (parseInt(data.TrangThaiXuLyID)) {
        case 40:
            labelNgay = "Ngày gửi";
            labelNguoi = "Người gửi";
            labelChucVu = "Chức vụ";
            labelNoiDung = "Nội dung gửi";

            txtNgay = data.txtNgayXuLy || "";
            tenNguoi = data.TenNguoiXuLy || "";
            chucVu = data.TenChucVuNguoiXuLy || "";
            noiDung = data.NoiDungXuLy || "";
            break;
        case 41:
            labelNgay = "Ngày đề nghị";
            labelNguoi = "Người đề nghị";
            labelChucVu = "Chức vụ";
            labelNoiDung = "Nội dung đề nghị";

            txtNgay = data.txtNgayTiepNhanXuLy || "";
            tenNguoi = data.TenNguoiTiepNhanXuLy || "";
            chucVu = data.TenChucVuNguoiTiepNhanXuLy || "";
            noiDung = data.NoiDungTiepNhanXuLy || "";
            break;
        case 42:
            labelNgay = "Ngày từ chối";
            labelNguoi = "Người từ chối";
            labelChucVu = "Chức vụ";
            labelNoiDung = "Nội dung từ chối";


            txtNgay = data.txtNgayTiepNhanXuLy || "";
            tenNguoi = data.TenNguoiTiepNhanXuLy || "";
            chucVu = data.TenChucVuNguoiTiepNhanXuLy || "";
            noiDung = data.NoiDungTiepNhanXuLy || "";
            break;
        case 32:
            labelNgay = "Ngày duyệt";
            labelNguoi = "Người duyệt";
            labelChucVu = "Chức vụ";
            labelNoiDung = "Nội dung duyệt";


            txtNgay = data.txtNgayTiepNhanXuLy || "";
            tenNguoi = data.TenNguoiTiepNhanXuLy || "";
            chucVu = data.TenChucVuNguoiTiepNhanXuLy || "";
            noiDung = data.NoiDungTiepNhanXuLy || "";
            break;
        default:
            labelNgay = "Ngày đề nghị";
            labelNguoi = "Người đề nghị";
            labelChucVu = "Chức vụ";
            labelNoiDung = "Nội dung đề nghị";

            txtNgay = data.txtNgayTiepNhanXuLy || "";
            tenNguoi = data.TenNguoiTiepNhanXuLy || "";
            chucVu = data.TenChucVuNguoiTiepNhanXuLy || "";
            noiDung = data.NoiDungTiepNhanXuLy || "";
            break;

    }
    return `<div class="list-item col-md-12" style="padding: 0px;">
        <div class="card card-luoi shadow-sm mb-2">
            <div id="card_${data.id}" class="card-body profile-user-box">
                <div class="row">
                    <div class="col-sm-2 text-center" style="width:12%;">
                        <div class="profile-picture" style=" height: 109px;">
                            <img src="${window.Laravel.local.linkAnhDonXin}" alt="ảnh đơn xin" class="img-thumbnail rounded lazy mb-2" style="background: white;
                            border: none;
                            box-shadow: unset;" >
                        </div>
                        <div style="display: flex; justify-content: center;">
                            <span style="
                                display: block;
                                margin-top: 10px;
                                font-weight: bold;
                                font-size: 1rem;
                                text-align: center;
                                padding: 6px 0;
                                background-color: ${data.MauSacTrangThaiXuLy};
                                color: white;
                                border-radius: 6px;
                                width: 95%;
                                ">
                                ${data.TenTrangThaiXuLy}
                            </span>
                        </div>
                    </div>
                    <div class="col-md-10" style="width:88%;">
                        <div class="row">
                            <div class="col-md-11">
                                <div class="row mb-2">
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">Số phiếu: <b>${data.SoPhieu || ""}</b></p>
                                    </div>
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">Ngày lập: <b>${data.txtNgayLap || ""}</b></p>
                                    </div>
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">Người lập: <b>${data.TenNguoiLap || ""}</b></p>
                                    </div>
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">Chức vụ: <b>${data.TenChucVuNguoiLap || ""}</b></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-1">
                                <div class="dropdown text-end" style="position: absolute; top: 10px; right: 10px;">
                                    <button class="btn btn-sm btn-white dropdown-toggle-hide-arrow" type="button" style="font-size:18px" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        <i class="blue fa fa-ellipsis-h" style="color: #696cff"></i>
                                    </button>
                                    <div class="dropdown-menu dropdown-menu-end w-auto">
                                        <!-- Luôn hiển thị -->
                                        <a style="padding: 5px 12px" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="XemChiTietNhatKy('${data.id}'); return false;">
                                            <i class="fa fa-eye" aria-hidden="true" style="color: #4265B6"></i>&ensp; Xem phiếu đề nghị
                                        </a>

                                        <!-- TrangThaiXuLyID = 40 (Đã gửi, chờ duyệt) -->
                                        ${data.TrangThaiXuLyID == '40' ? `
                                            <a style="padding: 5px 12px" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="GuiDonXinCapPhoiBang('${data.id}', '${data.SoPhieu}', '${data.txtNgayLap}', '${data.TenDonViGui}'); return false;">
                                                <i class="fa fa-check-circle" aria-hidden="true" style="color: #4265B6"></i>&ensp; Duyệt phiếu đề nghị
                                            </a>
                                            <a style="padding: 5px 12px" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="TuChoiDonXinCapPhoiBang('${data.id}', '${data.SoPhieu}', '${data.txtNgayLap}', '${data.TenDonViGui}'); return false;">
                                                <i class="fas fa-times-circle" aria-hidden="true" style="color: #EA1818"></i>&ensp; Từ chối duyệt phiếu xin đề nghị
                                            </a>

                                        ` : ''}

                                        <!-- TrangThaiXuLyID = 32 (Đã duyệt) -->
                                        ${data.TrangThaiXuLyID == '32' ? `
                                            <a style="padding: 5px 12px" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="ThuHoiDuyetDonXinCapPhoiBang('${data.id}', '${data.SoPhieu}', '${data.txtNgayLap}', '${data.TenDonViGui}'); return false;">
                                                <i class="fa fa-refresh" aria-hidden="true" style="color: #EA1818"></i>&ensp; Thu hồi duyệt phiếu đề nghị
                                            </a>
                                            <a style="padding: 5px 12px" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="CapPhatPhoiBang('${data.id}', '${data.SoPhieu}', '${data.TenDonViGui}'); return false;">
                                                <i class="fa fa-arrow-right text-primary" aria-hidden="true" style="color: #146654"></i>&ensp; Cấp phát phôi bằng
                                            </a>
                                        ` : ''}

                                        <!-- TrangThaiXuLyID = 42 (Đã từ chối) -->
                                        ${data.TrangThaiXuLyID == '42' ? `
                                            <a style="padding: 5px 12px" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="ThuHoiTuChoiDonXinCapPhoiBang('${data.id}', '${data.SoPhieu}', '${data.txtNgayLap}', '${data.TenDonViGui}'); return false;">
                                                <i class="fa fa-refresh" aria-hidden="true" style="color: #EA1818"></i>&ensp; Thu hồi từ chối phiếu đề nghị
                                            </a>
                                        ` : ''}

                                        <!-- Luôn hiển thị -->
                                        <a style="padding: 5px 12px" class="dropdown-item textsize-item" href="javascript:void(0);" onclick="InPhieuDeNghi('${data.id}')">
                                            <i class="fa fa-print iconsize-item" style="color: #2A79FF"></i>&ensp; In phiếu đề nghị
                                        </a>
                                    </div>

                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <hr class="nts-hr" />
                            <div class="col-md-11">
                                <div class="row mb-2">
                                    <div class="col-md-6">
                                        <p class="fs-big my-1">Đơn vị xin cấp phôi: <b>${data.TenDonViGui || ""}</b></p>
                                    </div>
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">Người liên hệ: <b>${data.NguoiLienHe || ""}</b></p>
                                    </div>
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">Số điện thoại: <b>${data.SoDienThoai || ""}</b></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-1"></div>
                            <div class="col-md-11">
                                <div class="row mb-2">
                                    <div class="col-md-6">
                                        <p class="fs-big my-1">Địa chỉ: <b>${data.DiaChi || ""}</b></p>
                                    </div>
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">Email: <b>${data.Email || ""}</b></p>
                                    </div>
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">Hình thức nhận phôi: <b>${data.TenHinhThucNhanPhoi || ""}</b></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-1"></div>
                        </div>
                        <div class="row">
                            <hr class="nts-hr" />
                            <div class="col-md-11">
                                <div class="row mb-12">
                                    <div class="col-md-6">
                                        <p class="fs-big my-1">Đơn vị tiếp nhận: <b>${data.TenDonViGui || ""}</b></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-1"></div>
                        </div>
                        <div class="row">
                            <div class="col-md-11">
                                <div class="row mb-2">
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">${labelNgay}: <b>${txtNgay}</b></p>
                                    </div>
                                    <div class="col-md-3">
                                        <p class="fs-big my-1">${labelNguoi}: <b>${tenNguoi}</b></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p class="fs-big my-1">${labelChucVu}: <b>${chucVu}</b></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-1"></div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>`;
}

var table = new Tabulator("#Grid1", {
    layout: "fitColumns",
    pagination: true,
    paginationSize: 50,
    paginationSizeSelector: [50, 100, 150, 200, 500, true],
    height: "600",
    HeaderVertAlign: "center",
    headerVisible: false,
    columns: [
        {
            title: "Thông tin",
            field: "ThongTinHoGiaDinh",
            formatter: htmlDuLieu,
            visible: true,
            minWidth: 250,
        },
    ],
    locale: true,
    paginationCounter: "rows",
    langs: TabulatorLangsVi,
    placeholder: "Không có dữ liệu",
});

async function LoadDataTable() {
    table.clearData();
    let result = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/getAll",
        {
            SearchKey: $("#SearchKey").value(),
            LoaiPhoiID_Loc: $("#LoaiPhoiID_Loc").value(),
            TrangThaiXuLyID_Loc: $("#TrangThaiXuLyID_Loc").value(),
        }
    );
    if (!result.Err) {
        VeChart();
        table.setData(result.result);
    } else {
        table.setData(null);
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
}

$(document).on("click", "#btn-layout-1", async function () {
    $("#grid-layout").fadeIn(200);
    $("#list-layout").hide();
    $("#list-layout").removeClass("show");
    $("#grid-layout").addClass("show");
    await LoadDataTable();
});

$(document).on("click", "#btn-layout-2", async function () {
    $("#grid-layout").hide();
    $("#list-layout").fadeIn(200);
    $("#list-layout").addClass("show");
    $("#grid-layout").removeClass("show");
    $("#DonYeuCauID").value("");
    $(".divThaoTacNhanh").hide();
    $("#txtTenTrangThaiXuLy_View").html("Trạng thái xử lý: <b>---</b>");
    $("#txtTenDonViGui_View").html("Đơn vị gửi: <b>---</b>");
    await LoadDataTable2();
});

function actionDropdownFormatter(cell) {
    const data = cell.getData();
    const ID = data.id;
    const button = document.createElement("button");
    button.className = "btn btn-sm btn-white dropdown-toggle-hide-arrow";
    button.innerHTML = `<i class="fa fa-ellipsis-h" style="color: #696cff;"></i>`;
    button.style.boxShadow = "none";

    button.onclick = function (e) {
        e.stopPropagation();
        document.querySelectorAll('.custom-dropdown-menu').forEach(el => el.remove());

        const dropdown = document.createElement("div");
        dropdown.className = "custom-dropdown-menu dropdown-menu dropdown-menu-end show";
        dropdown.style.position = "absolute";
        dropdown.style.zIndex = 9999;
        dropdown.style.minWidth = "250px";

        const TrangThai = data.TrangThaiXuLyID;
        let html = `
            <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                href="javascript:void(0);" onclick="XemChiTietNhatKy('${ID}'); return false;">
                ${icons["xem"]}&ensp; Xem phiếu đề nghị
            </a>
        `;

        if (TrangThai == '41') {
            html += `
                <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                    href="javascript:void(0);" onclick="SuaDuLieu('${ID}'); return false;">
                    ${icons["chinh"]}&ensp; Chỉnh sửa phiếu đề nghị
                </a>
                <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                    href="javascript:void(0);" onclick="GuiDonXinCapPhoiBang('${ID}', '${data.SoPhieu}', '${data.txtNgayLap}', '${data.TenDonViGui}'); return false;">
                    ${icons["guiduyet"]}&ensp; Gửi phiếu đề nghị
                </a>
                <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                    href="javascript:void(0);" onclick="XoaDuLieu('${ID}')">
                    ${icons["xoa"]}&ensp; Xóa phiếu đề nghị
                </a>
            `;
        }

        if (TrangThai == '40') {
            html += `
                <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                    href="javascript:void(0);" onclick="GuiDonXinCapPhoiBang('${ID}', '${data.SoPhieu}', '${data.txtNgayLap}', '${data.TenDonViGui}'); return false;">
                    ${icons["duyet"]}&ensp; Duyệt phiếu đề nghị
                </a>
                <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                    href="javascript:void(0);" onclick="TuChoiDonXinCapPhoiBang('${ID}', '${data.SoPhieu}', '${data.txtNgayLap}', '${data.TenDonViGui}'); return false;">
                    ${icons["tuchoi"]}&ensp; Từ chối duyệt phiếu xin đề nghị
                </a>
                <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                    href="javascript:void(0);" onclick="CapPhatPhoiBang('${ID}', '${data.SoPhieu}', '${data.TenDonViGui}'); return false;">
                    ${icons["capphat"]}&ensp; Cấp phát phôi bằng
                </a>
            `;
        }

        if (TrangThai == '32') {
            html += `
                <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                    href="javascript:void(0);" onclick="ThuHoiDuyetDonXinCapPhoiBang('${ID}', '${data.SoPhieu}', '${data.txtNgayLap}', '${data.TenDonViGui}'); return false;">
                    ${icons["thuhoiBH"]}&ensp; Thu hồi duyệt phiếu đề nghị
                </a>
            `;
        }

        if (TrangThai == '42') {
            html += `
                <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                    href="javascript:void(0);" onclick="ThuHoiTuChoiDonXinCapPhoiBang('${ID}', '${data.SoPhieu}', '${data.txtNgayLap}', '${data.TenDonViGui}'); return false;">
                    ${icons["thuhoiBH"]}&ensp; Thu hồi từ chối phiếu đề nghị
                </a>
            `;
        }

        html += `
            <a style="padding: 5px 12px" class="dropdown-item textsize-item"
                href="javascript:void(0);" onclick="InPhieuDeNghi('${ID}')">
                ${icons["in"]}&ensp; In phiếu đề nghị
            </a>
        `;

        dropdown.innerHTML = html;

        const rect = button.getBoundingClientRect();
        dropdown.style.left = `${rect.left + window.scrollX}px`;
        dropdown.style.top = `${rect.bottom + window.scrollY}px`;

        document.body.appendChild(dropdown);

        const closeDropdown = (event) => {
            if (!dropdown.contains(event.target) && event.target !== button) {
                dropdown.remove();
                document.removeEventListener('click', closeDropdown);
            }
        };
        document.addEventListener('click', closeDropdown);
    };

    return button;
}



function trangThaiSpanFormatter(cell) {
    const data = cell.getData();

    const mauSac = data.MauSacTrangThaiXuLy || "#6c757d";
    const tenTrangThai = data.TenTrangThaiXuLy || "Không xác định";

    const container = document.createElement("div");
    container.style.display = "flex";
    container.style.justifyContent = "center";

    const span = document.createElement("span");
    span.style.display = "block";
    span.style.marginTop = "10px";
    span.style.fontWeight = "bold";
    span.style.fontSize = "1rem";
    span.style.textAlign = "center";
    span.style.padding = "6px 0";
    span.style.backgroundColor = mauSac;
    span.style.color = "white";
    span.style.borderRadius = "6px";
    span.style.width = "95%";
    span.innerText = tenTrangThai;

    container.appendChild(span);
    return container;
}


var GridMainLuoi = new Tabulator("#GridMainLuoi", {
    layout: "fitColumns",
    pagination: true,
    paginationSize: 50,
    paginationSizeSelector: [50, 100, 150, 200, 500, true],
    height: "550",
    HeaderVertAlign: "center",
    columns: [
        {
            title: '<i class="fa fa-ellipsis-h"></i>',
            headerHozAlign: "center",
            hozAlign: "center",
            formatter: actionDropdownFormatter,
            width: 60,
            headerSort: false,
            frozen: true,
            vertAlign: "middle",
            print: false
        },
        {
            title: "Số phiếu",
            field: "SoPhieu",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            visible: true,
            width: 100,
        },
        {
            title: "Ngày lập",
            field: "txtNgayLap",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "center",
            vertAlign: "middle",
            width: 100,
        },
        {
            title: "Người lập",
            field: "TenNguoiLap",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "center",
            vertAlign: "middle",
            width: 150,
        },
        {
            title: "Chức vụ người lập",
            field: "TenChucVuNguoiLap",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            width: 150,
        },
        {
            title: "Đơn vị xin cấp phôi",
            field: "TenDonViGui",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "center",
            vertAlign: "middle",
            width: 300,
        },
        {
            title: "Đơn vị tiếp nhận",
            field: "TenDonViNhan",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "center",
            vertAlign: "middle",
            width: 300,
        },
        {
            title: "Người liên hệ",
            field: "NguoiLienHe",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            width: 150,
        },
        {
            title: "Số điện thoại",
            field: "SoDienThoai",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            width: 100,
        },
        {
            title: "Địa chỉ",
            field: "DiaChi",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "left",
            vertAlign: "middle",
            width: 300,
        },
        {
            title: "Email",
            field: "Email",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "right",
            vertAlign: "middle",
            width: 150,
        },
        {
            title: "Hình thực nhận phôi",
            field: "TenHinhThucNhanPhoi",
            formatter: "textarea",
            headerHozAlign: "center",
            hozAlign: "right",
            vertAlign: "middle",
            width: 100,
        },
        {
            title: "Trạng thái",
            field: "TenTrangThaiXuLy",
            formatter: "textarea",
            hozAlign: "center",
            headerHozAlign: "center",
            hozAlign: "right",
            vertAlign: "middle",
            width: 120,
        },
        {
            title: "Trạng thái",
            field: "TenTrangThaiXuLy",
            formatter: "textarea",
            hozAlign: "center",
            headerHozAlign: "center",
            hozAlign: "right",
            vertAlign: "middle",
            width: 120,
        }
    ],
    locale: true,
    paginationCounter: "rows",
    langs: TabulatorLangsVi,
    placeholder: "Không có dữ liệu",
});

async function LoadDataTable2() {

    GridMainLuoi.clearData();
    let result = await NTS.getAjaxAPIAsync(
        "GET",
        window.location.pathname + "/getAll",
        {
            SearchKey: $("#SearchKey").value(),
            LoaiPhoiID_Loc: $("#LoaiPhoiID_Loc").value(),
            TrangThaiXuLyID_Loc: $("#TrangThaiXuLyID_Loc").value(),
            CbSapXep: $("#CbSapXep").value(),
        }
    );
    if (!result.Err) {
        GridMainLuoi.setData(result.result);
    } else {
        GridMainLuoi.setData(null);
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
    }
}

async function VeChart() {
    let result = await NTS.getAjaxAPIAsync(
        "GET",
        window.Laravel.local.getThongKe,
        {
            SearchKey: $("#SearchKey").value(),
            LoaiPhoiID_Loc: $("#LoaiPhoiID_Loc").value(),
            TrangThaiXuLyID_Loc: $("#TrangThaiXuLyID_Loc").value(),
        }
    );

    try {
        $("#txtTong").text(result.Result.TS);

        const trangThaiData = result.Result.ThongKeTrangThai || [];
        const container = $("#trangThaiContainer").empty();

        const iconClassMap = {
            "Đã phê duyệt": "fa-check-square",
            "Đã duyệt": "fa-check-square",
            "Chờ duyệt": "fa-clock-o",
            "Chờ gửi": "fa-clock-o",
            "Bị từ chối": "fa-ban",
        };

        const chartLabels = [];
        const chartSeries = [];
        const chartColors = [];

        trangThaiData.forEach(item => {
            const icon = iconClassMap[item.TenTrangThai] || "fa-info-circle";
            container.append(`
                <div style="color: ${item.MauSac}; margin-bottom: 7px;">
                    <i class="fa ${icon}" aria-hidden="true"></i>
                    ${item.TenTrangThai}:
                    <b><u><b id="txtTrangThai_${item.TrangThaiXuLyID}">${item.SoLuong}</b></u></b>
                </div>
            `);
            chartLabels.push(item.TenTrangThai);
            chartSeries.push(item.SoLuong);
            chartColors.push(item.MauSac || "#999999");
        });

        const $container = $("#phoiContainer").empty();
        const ThongKeLoaiPhoi = result.Result.ThongKeLoaiPhoi || [];

        ThongKeLoaiPhoi.forEach((item, index) => {
            const isEven = index % 2 === 0;
            const color = isEven ? "#7AA802" : "#F78B2D";
            const iconSvg = isEven ? `<svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17"
                                        fill="none">
                                        <path
                                            d="M2.73756 2.05261C1.97809 2.05261 1.36914 2.66156 1.36914 3.42103V10.2631C1.36914 10.6261 1.51331 10.9741 1.76994 11.2308C2.02657 11.4874 2.37463 11.6316 2.73756 11.6316H8.21125V15.0526L10.2639 13L12.3165 15.0526V11.6316H13.6849C14.0479 11.6316 14.3959 11.4874 14.6525 11.2308C14.9092 10.9741 15.0534 10.6261 15.0534 10.2631V3.42103C15.0534 3.05811 14.9092 2.71004 14.6525 2.45341C14.3959 2.19678 14.0479 2.05261 13.6849 2.05261H2.73756ZM8.21125 3.42103L10.2639 4.78945L12.3165 3.42103V5.81577L14.3691 6.84209L12.3165 7.8684V10.2631L10.2639 8.89472L8.21125 10.2631V7.8684L6.15861 6.84209L8.21125 5.81577V3.42103ZM2.73756 3.42103H6.15861V4.78945H2.73756V3.42103ZM2.73756 6.15788H4.79019V7.5263H2.73756V6.15788ZM2.73756 8.89472H6.15861V10.2631H2.73756V8.89472Z"
                                            fill="#7AA802" />
                                     </svg>` : `<svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17"
                                        fill="none">
                                        <path
                                            d="M8.21083 13C9.34447 13 10.2635 12.081 10.2635 10.9473C10.2635 9.81371 9.34447 8.89471 8.21083 8.89471C7.0772 8.89471 6.1582 9.81371 6.1582 10.9473C6.1582 12.081 7.0772 13 8.21083 13Z"
                                            stroke="#F78B2D" stroke-width="1.02632" />
                                        <path
                                            d="M6.23612 10.92L5.52454 11.6316L4.51328 12.6011C4.2916 12.8139 4.18076 12.9199 4.14244 13.0102C4.09936 13.1063 4.09372 13.215 4.12663 13.315C4.15954 13.415 4.22863 13.4991 4.32033 13.5508C4.40381 13.598 4.55502 13.6124 4.85607 13.6425C5.02576 13.6589 5.11128 13.6671 5.18244 13.6924C5.25976 13.7193 5.33037 13.7625 5.38941 13.8192C5.44846 13.8759 5.49456 13.9447 5.52454 14.0208C5.55123 14.0892 5.56012 14.1707 5.57723 14.3342C5.60802 14.6229 5.62376 14.7673 5.67302 14.8474C5.78523 15.03 6.02265 15.1019 6.23681 15.0184C6.33055 14.9808 6.44139 14.8747 6.66307 14.6626L8.21144 13.1779L9.75981 14.6626C9.98149 14.8747 10.0923 14.9808 10.1861 15.0184C10.4002 15.1019 10.6377 15.03 10.7499 14.8474C10.7991 14.7673 10.8149 14.6229 10.8457 14.3342C10.8628 14.1707 10.8717 14.0892 10.8983 14.0208C10.9283 13.9447 10.9744 13.8759 11.0335 13.8192C11.0925 13.7625 11.1631 13.7193 11.2404 13.6924C11.3123 13.6671 11.3971 13.6589 11.5668 13.6425C11.8679 13.613 12.0191 13.598 12.1025 13.5508C12.1943 13.4991 12.2633 13.415 12.2963 13.315C12.3292 13.215 12.3235 13.1063 12.2804 13.0102C12.2421 12.9199 12.1313 12.8139 11.9096 12.6011L10.8977 11.6316L10.2641 10.9973"
                                            stroke="#F78B2D" stroke-width="1.02632" />
                                        <path
                                            d="M11.8512 12.313C13.2005 12.2987 13.9531 12.2138 14.4519 11.7144C15.0534 11.1136 15.0534 10.1455 15.0534 8.21051V5.47367C15.0534 3.53872 15.0534 2.57057 14.4519 1.96983C13.8512 1.36841 12.883 1.36841 10.9481 1.36841H5.4744C3.53946 1.36841 2.5713 1.36841 1.97056 1.96983C1.36914 2.57057 1.36914 3.53872 1.36914 5.47367V8.21051C1.36914 10.1455 1.36914 11.1136 1.97056 11.7144C2.49604 12.2405 3.30204 12.3062 4.79019 12.3144"
                                            stroke="#F78B2D" stroke-width="1.02632" />
                                        <path d="M6.15944 4.10522H10.2647M4.79102 6.49996H11.6331" stroke="#F78B2D"
                                            stroke-width="1.02632" stroke-linecap="round" />
                                    </svg>`;

            $container.append(`
                <div style="color: ${color}; margin-bottom: 7px;">
                    ${iconSvg} <span>${item.TenLoaiPhoiVanBangChungChi}</span>: 
                    <b><u><b>${item.SoLuong}</b></u></b>
                </div>
            `);
        });

        document.querySelector("#XepLoaiChart").innerHTML = "";

        mixedXepLoaiChart = new ApexCharts(document.querySelector("#XepLoaiChart"), {
            labels: chartLabels,
            colors: chartColors,
            series: chartSeries,
            chart: {
                type: "donut",
                height: 150
            },
            dataLabels: {
                enabled: false,
            },
            responsive: [{
                breakpoint: 480,
                options: {
                    chart: {
                        width: 110,
                    },
                    legend: {
                        position: "bottom",
                    },
                },
            }],
            dataLabels: {
                enabled: true,
                formatter: function (val) {
                    return Math.round(val) + "%";
                },
                style: {
                    fontSize: '12px',
                    fontWeight: 'bold',
                    colors: ['#ffffff'],
                    fontFamily: 'Arial'
                }
            },
            plotOptions: {
                pie: {
                    donut: {
                        size: '55%',
                        labels: {
                            show: false
                        }
                    }
                }
            }
        });

        mixedXepLoaiChart.render();

    } catch (err) {
        console.error("Lỗi khi vẽ biểu đồ:", err);
    }
}

function GuiDonXinCapPhoiBang(id, SoPhieu, txtNgayLap, DonViGui) {
    $("#DonYeuCauID").val(id);
    $("#NgayTiepNhanXuLy").val(defaultDate);

    NTS.getAjaxAPIAsync("GET", window.Laravel.layouts.getCurrentUserInfo, {})
        .then((response) => {
            if (response.status && response.data) {
                const nhanVien = response.data.nhanVien;
                $("#NguoiTiepNhanXuLyID").value(nhanVien.id);
                if (nhanVien && nhanVien.chuc_vu) {
                    $("#ChucVuNguoiTiepNhanXuLyID").value(nhanVien.chuc_vu.id);
                }
            }
        })
        .catch((err) => {
            console.error("Lỗi khi lấy thông tin người dùng:", err);
        });

    const noiDungText = `Duyệt phiếu đề nghị cấp phôi văn bằng, chứng chỉ số: ${SoPhieu} ngày lập ${txtNgayLap} của ${DonViGui}.`;
    $('#NoiDungTiepNhanXuLy').val(noiDungText);

    $("#tieuDe_mdGuiDonXinCapPhoiBang").text("Duyệt phiếu đề nghị cấp phôi văn bằng, chứng chỉ");
    $("#alertMessage").html('Bạn đang thực hiện duyệt phiếu đề nghị cấp phôi văn bằng, chứng chỉ số: <b id="SoPhieu_lbl"></b> ngày lập <b id="txtNgayLap_lbl"></b> của <b id="TenDonViGui_lbl"></b>, Vui lòng điền đầy đủ các thông tin bên dưới và nhấn vào nút <b>"Phê duyệt"</b> để thực hiện thao tác duyệt phiếu đề nghị.');
    $("#btnGuiDon").html('<i class="fa fa-check nts-iconThaoTacs" aria-hidden="true"></i>&ensp;Phê duyệt (F9)');
    $('#SoPhieu_lbl').text(SoPhieu)
    $('#txtNgayLap_lbl').text(txtNgayLap)
    $('#TenDonViGui_lbl').text(DonViGui)

    $('label[for="NgayTiepNhanXuLy"]').text('Ngày duyệt');
    $('label[for="NguoiTiepNhanXuLyID"]').text('Người duyệt');
    $('label[for="ChucVuNguoiTiepNhanXuLyID"]').text('Chức vụ');
    $('label[for="NoiDungTiepNhanXuLy"]').text('Nội dung duyệt');

    tempTrangThai = "32";
    $('#mdGuiDonXinCapPhoiBang').modal('show');
}

function TuChoiDonXinCapPhoiBang(id, SoPhieu, txtNgayLap, DonViGui) {
    $("#DonYeuCauID").val(id);
    $("#NgayTiepNhanXuLy").val(defaultDate);

    NTS.getAjaxAPIAsync("GET", window.Laravel.layouts.getCurrentUserInfo, {})
        .then((response) => {
            if (response.status && response.data) {
                const nhanVien = response.data.nhanVien;
                $("#NguoiTiepNhanXuLyID").value(nhanVien.id);
                if (nhanVien && nhanVien.chuc_vu) {
                    $("#ChucVuNguoiTiepNhanXuLyID").value(nhanVien.chuc_vu.id);
                }
            }
        })
        .catch((err) => {
            console.error("Lỗi khi lấy thông tin người dùng:", err);
        });

    const noiDungText = `Từ chối phiếu đề nghị cấp phôi văn bằng, chứng chỉ số: ${SoPhieu} ngày lập ${txtNgayLap} của ${DonViGui}.`;
    $('#NoiDungTiepNhanXuLy').val(noiDungText);

    $("#tieuDe_mdGuiDonXinCapPhoiBang").text("Từ chối phiếu đề nghị cấp phôi văn bằng, chứng chỉ");
    $("#alertMessage").html('Bạn đang thực hiện từ chối phiếu đề nghị cấp phôi văn bằng, chứng chỉ số: <b id="SoPhieu_lbl"></b> ngày lập <b id="txtNgayLap_lbl"></b> của <b id="TenDonViGui_lbl"></b>, Vui lòng điền đầy đủ các thông tin bên dưới và nhấn vào nút <b>"Từ chối"</b> để thực hiện thao tác từ chối phiếu đề nghị.');
    $("#btnGuiDon").html('<i class="fa fa-times-circle nts-iconThaoTacs" aria-hidden="true"></i>&ensp; Từ chối (F9)');
    $('#SoPhieu_lbl').text(SoPhieu)
    $('#txtNgayLap_lbl').text(txtNgayLap)
    $('#TenDonViGui_lbl').text(DonViGui)

    $('label[for="NgayTiepNhanXuLy"]').text('Ngày từ chối');
    $('label[for="NguoiTiepNhanXuLyID"]').text('Người từ chối');
    $('label[for="ChucVuNguoiTiepNhanXuLyID"]').text('Chức vụ');
    $('label[for="NoiDungTiepNhanXuLy"]').text('Nội dung từ chối');
    tempTrangThai = "42";
    $('#mdGuiDonXinCapPhoiBang').modal('show');
}

function ThuHoiTuChoiDonXinCapPhoiBang(id, SoPhieu, txtNgayLap, DonViGui) {
    $("#DonYeuCauID").val(id);
    $("#NgayTiepNhanXuLy").val(defaultDate);

    NTS.getAjaxAPIAsync("GET", window.Laravel.layouts.getCurrentUserInfo, {})
        .then((response) => {
            if (response.status && response.data) {
                const nhanVien = response.data.nhanVien;
                $("#NguoiTiepNhanXuLyID").value(nhanVien.id);
                if (nhanVien && nhanVien.chuc_vu) {
                    $("#ChucVuNguoiTiepNhanXuLyID").value(nhanVien.chuc_vu.id);
                }
            }
        })
        .catch((err) => {
            console.error("Lỗi khi lấy thông tin người dùng:", err);
        });

    const noiDungText = `Thu hồi từ chối phiếu đề nghị cấp phôi văn bằng,chứng chỉ số: ${SoPhieu} ngày lập ${txtNgayLap} của ${DonViGui}.`;
    $('#NoiDungTiepNhanXuLy').val(noiDungText);

    $("#tieuDe_mdGuiDonXinCapPhoiBang").text("Thu hồi Từ chối phiếu đề nghị cấp phôi văn bằng, chứng chỉ");
    $("#alertMessage").html('Bạn đang thực hiện thu hồi từ chối phiếu đề nghị cấp phôi văn bằng, chứng chỉ số: <b id="SoPhieu_lbl"></b> ngày lập <b id="txtNgayLap_lbl"></b> của <b id="TenDonViGui_lbl"></b>, Vui lòng điền đầy đủ các thông tin bên dưới và nhấn vào nút <b>"Thu hồi"</b> để thực hiện thao tác thu hồi từ chối phiếu đề nghị.');
    $("#btnGuiDon").html('<i class="fa fa-share-square-o nts-iconThaoTacs" aria-hidden="true"></i>&ensp;Thu hồi (F9)');
    $('#SoPhieu_lbl').text(SoPhieu)
    $('#txtNgayLap_lbl').text(txtNgayLap)
    $('#TenDonViGui_lbl').text(DonViGui)

    $('label[for="NgayTiepNhanXuLy"]').text('Ngày thu hồi');
    $('label[for="NguoiTiepNhanXuLyID"]').text('Người thu hồi');
    $('label[for="ChucVuNguoiTiepNhanXuLyID"]').text('Chức vụ');
    $('label[for="NoiDungTiepNhanXuLy"]').text('Nội dung thu hồi');
    tempTrangThai = "40";
    $('#mdGuiDonXinCapPhoiBang').modal('show');
}

function ThuHoiDuyetDonXinCapPhoiBang(id, SoPhieu, txtNgayLap, DonViGui) {
    $("#DonYeuCauID").val(id);
    $("#NgayTiepNhanXuLy").val(defaultDate);


    NTS.getAjaxAPIAsync("GET", window.Laravel.layouts.getCurrentUserInfo, {})
        .then((response) => {
            if (response.status && response.data) {
                const nhanVien = response.data.nhanVien;
                $("#NguoiTiepNhanXuLyID").value(nhanVien.id);
                if (nhanVien && nhanVien.chuc_vu) {
                    $("#ChucVuNguoiTiepNhanXuLyID").value(nhanVien.chuc_vu.id);
                }
            }
        })
        .catch((err) => {
            console.error("Lỗi khi lấy thông tin người dùng:", err);
        });
    const noiDungText = `Thu hồi duyệt phiếu đề nghị cấp phôi văn bằng, chứng chỉ số: ${SoPhieu} ngày lập ${txtNgayLap} của ${DonViGui}.`;
    $('#NoiDungTiepNhanXuLy').val(noiDungText);

    $("#tieuDe_mdGuiDonXinCapPhoiBang").text("Thu hồi duyệt phiếu đề nghị cấp phôi văn bằng, chứng chỉ");
    $("#alertMessage").html('Bạn đang thực hiện thu hồi duyệt phiếu đề nghị cấp phôi văn bằng, chứng chỉ số: <b id="SoPhieu_lbl"></b> ngày lập <b id="txtNgayLap_lbl"></b> của <b id="TenDonViGui_lbl"></b>, Vui lòng điền đầy đủ các thông tin bên dưới và nhấn vào nút <b>"Thu hồi"</b> để thực hiện thao tác thu hồi duyệt phiếu đề nghị.');
    $("#btnGuiDon").html('<i class="fa fa-share-square-o nts-iconThaoTacs" aria-hidden="true"></i>&ensp;Thu hồi (F9)');
    $('#SoPhieu_lbl').text(SoPhieu)
    $('#txtNgayLap_lbl').text(txtNgayLap)
    $('#TenDonViGui_lbl').text(DonViGui)

    $('label[for="NgayTiepNhanXuLy"]').text('Ngày thu hồi');
    $('label[for="NguoiTiepNhanXuLyID"]').text('Người thu hồi');
    $('label[for="ChucVuNguoiTiepNhanXuLyID"]').text('Chức vụ');
    $('label[for="NoiDungTiepNhanXuLy"]').text('Nội dung thu hồi');
    tempTrangThai = "40";
    $('#mdGuiDonXinCapPhoiBang').modal('show');
}

async function CapPhatPhoiBang(id, soPhieu, tenDonViGui) {
    if (!QuyenThem()) {
        return;
    }
    $('#tbodyKetQuaLoaiVBCC').html(``)
    resetForm("#mdCapPhatPhoiBang");
    uploadedFileUrls = [];
    $("#list-file").empty();
    $('#TiepNhanPhoiVBCCID').val("");
    $("#tieuDeModalCapPhatPhoiBang").text("Thêm mới thông tin nhập kho/cấp phát phôi bằng");
    $("#mdCapPhatPhoiBang").find("input, textarea").val('');
    $("#NgayNhap").value(defaultDate);
    $("#mdCapPhatPhoiBang").modal("show");
    $('#DonViID_YeuCau').val(id);
    const formattedValue = `Số phiếu: ${soPhieu}, Đơn vị: ${tenDonViGui}`;
    $('#DonViID_YeuCau_fm').val(formattedValue);

    NTS.getAjaxAPIAsync("GET", window.Laravel.layouts.getCurrentUserInfo, {})
        .then((response) => {
            if (response.status && response.data) {
                const nhanVien = response.data.nhanVien;
                $("#NhanVienID_Nhap").value(nhanVien.id);
                if (nhanVien && nhanVien.chuc_vu) {
                    $("#ChucVuID").value(nhanVien.chuc_vu.id);
                }
            }
        })
        .catch((err) => {
            console.error("Lỗi khi lấy thông tin người dùng:", err);
        });

    try {
        var result = await NTS.getAjaxAPIAsync(
            "GET",
            window.Laravel.local.maTuTangUrl,
            {}
        );
        $("#SoHieuTu").value(result.SoChungTu);
        $("#SoHieuDen").value(addToCode(result.SoChungTu));
    } catch { }
    try {
        var result = await NTS.getAjaxAPIAsync(
            "GET",
            window.Laravel.local.soPhieuTuTangUrl,
            {}
        );
        $("#MaPhieu").value(result.SoChungTu);
    } catch { }
    $('#SoLuongNhap').val(1)
    tempthemCapPhoi = "them";
}

$("#btnLuuVaDongCapPhat").on("click", async function () {
    const validate = new NTSValidate("#mdCapPhatPhoiBang");
    if (!validate.trim().check()) return false;

    const tbody = document.getElementById("tbodyKetQuaLoaiVBCC");
    let DieuKien = [];
    tbody.querySelectorAll("tr").forEach((row) => {
        let rowData = {};
        const tds = row.querySelectorAll("td");
        rowData["LoaiPhoiVBCCID"] = tds[1].querySelector("select") ? tds[1].querySelector("select").value : tds[1].textContent;
        rowData["DonViTinhID"] = tds[2].querySelector("select") ? tds[2].querySelector("select").value : tds[2].textContent;
        rowData["SoLuongPhoi"] = tds[3].querySelector("input") ? tds[3].querySelector("input").value : tds[3].textContent;
        rowData["TenLoaiPhoiVBCC"] = tds[1].querySelector("select")
        ? (tds[1].querySelector("select").options[tds[1].querySelector("select").selectedIndex].text.trim() === "-Chọn-" ? '' : tds[1].querySelector("select").options[tds[1].querySelector("select").selectedIndex].text.trim())
        : tds[1].textContent.trim();
        rowData["SoHieuTu"] = tds[4].querySelector("input") ? tds[4].querySelector("input").value : tds[4].textContent;
        rowData["SoHieuDen"] = tds[5].querySelector("input") ? tds[5].querySelector("input").value : tds[5].textContent;
        DieuKien.push(rowData);
    });

    const payload = {
        TiepNhanPhoiVBCCID: $("#TiepNhanPhoiVBCCID").value(),
        MaPhieu: $("#MaPhieu").value(),
        NgayNhap: $("#NgayNhap").value(),
        CapHocID: $("#CapHocID").value(),
        NhanVienID_Nhap: $("#NhanVienID_Nhap").value(),
        ChucVuID: $("#ChucVuID").value(),
        LoaiPhoiVanBangChungChiID: DieuKien,
        DonViID_Cap: $("#DonViID_Cap").value(),
        DonViID_Nhap: $("#DonViID_Nhap").value(),
        DonViID_YeuCau: $("#DonViID_YeuCau").value(),
        SoLuongNhap: tinhTong(),
        SoHieuTu: $("#SoHieuTu").value(),
        SoHieuDen: $("#SoHieuDen").value(),
        GhiChu: $("#GhiChu").value(),
        txtDuongDanFileVB: $("#txtDuongDanFileVB").value()
    };
    var met = "POST";
    if (tempthemCapPhoi == "them") {
        met = "POST";
    } else {
        met = "PUT";
    }
    var result = await NTS.getAjaxAPIAsync(
        met,
        window.location.pathname + "/CapPhatPhoiBang",
        payload
    );
    if (!result.Err) {
        LoadDataTable();
        NTS.thanhcong(result.Msg);
        $("#mdCapPhatPhoiBang").modal("hide");
    } else {
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
        return false;
    }
});


$("#btnGuiDon").on("click", async function () {

    const validate = new NTSValidate("#mdGuiDonXinCapPhoiBang");
    if (!validate.trim().check()) return false;

    const payload = {
        DonYeuCauID: $("#DonYeuCauID").val(),
        NguoiTiepNhanXuLyID: $("#NguoiTiepNhanXuLyID").val(),
        ChucVuNguoiTiepNhanXuLyID: $("#ChucVuNguoiTiepNhanXuLyID").val(),
        NoiDungTiepNhanXuLy: $("#NoiDungTiepNhanXuLy").val(),
        NgayTiepNhanXuLy: $("#NgayTiepNhanXuLy").val(),
        TrangThaiXuLyID: tempTrangThai
    };

    var result = await NTS.getAjaxAPIAsync(
        "post",
        window.location.pathname + "/GuiDon",
        payload
    );
    if (!result.Err) {
        LoadDataTable();
        NTS.thanhcong(result.Msg);
        $("#mdGuiDonXinCapPhoiBang").modal("hide");
    } else {
        result.canhbao ? NTS.canhbao(result.Msg) : NTS.loi(result.Msg);
        return false;
    }
});


//#region Đính kèm

$("#btnChonTepVB_us").on("click", function () {
    $("#fileVB_us").trigger("click");
});

$("#fileVB_us").on("change", async function () {
    const files = Array.from(this.files);
    $("#list-file").empty();

    await NTS.getAjaxAPIAsync("POST", "/api/dungchung/files/clearTemp", {
        chucnang: "TiepNhanPhoiVBCC"
    });
    for (const file of files) {
        const form = new FormData();
        form.append("file", file);
        form.append("chucnang", "TiepNhanPhoiVBCC");
        try {
            const res = await NTS.getAjaxAPIAsync("POST",
                "/api/dungchung/files/uploadFileTemp",
                form,
            );
            if (!res.success) {
                NTS.loi(`Tải lên thất bại: ${file.name}`);
                continue;
            }
            // collect the returned URLs array (or wrap single URL)
            const url = Array.isArray(res.url) ? res.url[0] : res.url;
            uploadedFileUrls.push(url);
        } catch (err) {
            console.error("Upload error", err);
            NTS.loi(`Lỗi upload ${file.name}`);
        }
    }

    // 2) Render previews by iterating uploadedFileUrls
    uploadedFileUrls.forEach((url) => {
        renderAttachment(url);
    });

    const ttList = [].slice.call(
        document.querySelectorAll('[data-bs-toggle="tooltip"]')
    );
    ttList.forEach((el) => new bootstrap.Tooltip(el));

    $("#txtDuongDanFileVB").val(uploadedFileUrls.join("|"));
});

$("#btnXoaHetTepVB_us").on("click", async function () {
    CanhBaoXoa(async () => {
        $("#fileVB_us").val("");
        $("#list-file").empty();
        $("#txtDuongDanFileVB").val("");

        try {
            // call delete-multiple with our URL array
            if (uploadedFileUrls.length !== 0) {
                const res = await NTS.getAjaxAPIAsync("DELETE",
                    "/api/dungchung/files/delete-multiple",
                    { urls: uploadedFileUrls }

                );
                if (!res.success) {
                    return NTS.loi("Xóa tất cả thất bại: " + (res.message || ""));
                }
                if (res.loi) {
                    return NTS.loi("Xóa tất cả thất bại: " + (res.message || ""));
                }

                NTS.thanhcong("Xoá tất cả đính kèm thành công");
            }
        } catch (err) {
            console.error("Delete all error", err);
            NTS.loi("Có lỗi khi xóa tất cả đính kèm");
        } finally {
            uploadedFileUrls = []; // reset URL store
        }
    });
});
function renderAttachment(url) {
    const filename = url.split("/").pop();

    // 2) get the extension (e.g. "jpg")
    const ext = filename.split(".").pop().toLowerCase();

    // 3) decide if it’s an image
    const imageExts = ["jpg", "jpeg", "png", "gif", "bmp", "webp"];
    const isImage = imageExts.includes(ext);

    const $item = $(`
            <div class="file-preview position-relative d-inline-block text-center me-2 mb-2">
            <!-- delete button -->
                <button type="button" class="btn-close position-absolute top-0 end-0 m-1" aria-label="Delete"></button>
            </div>
              `);

    // image vs icon
    let $thumb;
    if (isImage) {
        $thumb = $(`
            <img class="img-thumbnail"
                 style="width:100px;height:100px;object-fit:cover;"
                 src="${url}">
          `);
    } else {
        $thumb = $(`
            <div class="file-icon bg-secondary text-white rounded
                        d-flex align-items-center justify-content-center mb-1"
                 style="width:100px;height:100px;font-size:2rem;">
              <i class="fa fa-file"></i>
            </div>
          `);
    }

    // 1) native tooltip
    $thumb.attr("title", filename);

    // 2) (optional) Bootstrap tooltip
    $thumb.attr("data-bs-toggle", "tooltip").attr("data-bs-placement", "top");

    // assemble
    $item.append($thumb);
    $item.append(
        $(
            '<a target="_blank" class="d-block small text-truncate" style="max-width:100px;"></a>'
        )
            .attr("href", url)
            .text(filename)
    );
    $("#list-file").append($item);

    $item.find(".btn-close").on("click", async () => {
        try {
            //call delete-multiple with a JSON array of this single URL
            const res = await NTS.getAjaxAPIAsync("DELETE",
                "/api/dungchung/files/delete-multiple",
                { urls: [url] }
            );
            if (!res.success) {
                return NTS.loi("Xóa file thất bại: " + filename);
            }
            // remove from uploadedFileUrls
            uploadedFileUrls = uploadedFileUrls.filter((u) => u !== url);
            // update hidden field
            // remove preview from DOM
            $item.remove();
            $("#txtDuongDanFileVB").val(uploadedFileUrls.join("|"));
        } catch (err) {
            console.error("Delete error", err);
            NTS.loi("Lỗi khi xóa " + filename);
        }
    });
}

function ThemDieuKienXepLoai(){
    var guid = crypto.randomUUID();
    var htmlTr=`<tr class="nts-table-tr" id="tr`+guid+`">
                    <td class="nts-table-td " colspan="0" rowspan="1">
                        <label class="nts-table-tile-font-size XoaLoaiVBCC"><i class="fa fa-minus-circle" style="color: #fd0909;font-size: 18px "></i></label>
                    </td>
                    <td class="nts-table-td " colspan="0" rowspan="1">
                        <select class="form-control selectLoaiChungTu" data="`+guid+`" tabindex="0" id="selectLoaiChungTu`+guid+`" >
                        </select>
                    </td>
                    <td class="nts-table-td " colspan="0" rowspan="1">
                        <select class="form-control selectdonvitinh" data="`+guid+`" tabindex="0" id="selectdonvitinh`+guid+`" >
                        </select>
                    </td>
                    <td class="nts-table-td " colspan="0" rowspan="1">
                        <input type="number" class="form-control inputsoluong" data="`+guid+`" style="text-align:center" autocomplete="off" value="1">
                    </td>

                     <td class="nts-table-td " colspan="0" rowspan="1">
                        <input type="text" class="form-control inputsohieutu" data="`+guid+`" id="inputsohieutu`+guid+`" style="text-align:center" autocomplete="off" >
                    </td>

                     <td class="nts-table-td " colspan="0" rowspan="1">
                        <input type="text" class="form-control inputsohieuden" data="`+guid+`" id="inputsohieuden`+guid+`" style="text-align:center" autocomplete="off" value="">
                    </td>
                </tr>  `;

    $('#tbodyKetQuaLoaiVBCC').append(htmlTr);
    NTS.loadDataComboAsync({
        name: "#selectLoaiChungTu"+guid,
        type: "POST",
        ajaxUrl: window.Laravel.local.getloaiphoivanbangchungchi,
        ajaxParam: {},
        columns: 1,
        indexValue: 2,
        indexText: 1,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
    NTS.loadDataComboAsync({
        name: "#selectdonvitinh"+guid,
        type: "GET",
        ajaxUrl: window.Laravel.local.getListDonViTinh,
        ajaxParam: {},
        columns: 1,
        indexValue: 0,
        indexText: 2,
        textShowTatCa: "-Chọn-",
        showTatCa: !0,
    });
}
$(document).on("click", ".XoaLoaiVBCC", function () {
    $(this).parent().parent().remove();
});


function tinhTong() {
    let tong = 0;
    const inputs = document.querySelectorAll('.inputsoluong');
    inputs.forEach(function(input) {
        let giatri = parseFloat(input.value);
        if (!isNaN(giatri)) {
            tong += giatri;
        }
    });
    return tong;
}

async function LayMaTuTangTheoLoaiCC(KyHieu) {
    const payload = {
        KyHieu:KyHieu
    };
    var result = await NTS.getAjaxAPIAsync(
        "get",
        window.location.pathname + "/LayMaTuTang",
        payload
    );
    return [{'prefix' : result.prefix,
            'numPart' : result.numPart,
            'suffix' : result.suffix}];
}
$(document).on("change", ".selectLoaiChungTu",async function () {
    var data = $(this).attr('data');
    var selectedText = $(this).find('option:selected').text().trim();
    var machungtu = '';
    if (selectedText === 'Bằng tốt nghiệp THCS') {
        machungtu = await LayMaTuTangTheoLoaiCC('NhapKhoPhoiBangTHCS');
    } else if (selectedText === 'Bằng tốt nghiệp THCS - Bản sao') {
        machungtu = await LayMaTuTangTheoLoaiCC('NhapKhoPhoiBangTHCS-BS');
    } else if (selectedText === 'Bằng tốt nghiệp THPT') {
        machungtu = await LayMaTuTangTheoLoaiCC('NhapKhoPhoiBangTHPT');
    } else if (selectedText === 'Bằng tốt nghiệp THPT - Bản sao') {
        machungtu = await LayMaTuTangTheoLoaiCC('NhapKhoPhoiBangTHPT-BS');
    }
    if (machungtu && machungtu[0]) {
        $('#inputsohieutu' + data).val(machungtu[0].prefix + machungtu[0].numPart + machungtu[0].suffix);
        $('#inputsohieutu' + data).attr('prefix',machungtu[0].prefix)
        $('#inputsohieutu' + data).attr('suffix',machungtu[0].suffix)

        $('#inputsohieuden' + data).val(machungtu[0].prefix + machungtu[0].numPart + machungtu[0].suffix);
        $('#inputsohieuden' + data).attr('prefix',machungtu[0].prefix)
        $('#inputsohieuden' + data).attr('suffix',machungtu[0].suffix)
    }
});

$(document).on('blur', '.inputsoluong', function () {
    var data = $(this).attr('data');
    var soLuong = parseInt($(this).val(), 10);
    if (!isNaN(soLuong) && soLuong > 0) {
        capNhatSoHieuDen(data, soLuong);
    }
});

function capNhatSoHieuDen(data, soLuong) {
    var $tu = $('#inputsohieutu' + data);
    var $den = $('#inputsohieuden' + data);

    // Lấy prefix và suffix từ thuộc tính đã gán
    var prefix = $tu.attr('prefix') || '';
    var suffix = $tu.attr('suffix') || '';

    // Tách phần số từ giá trị input
    var soHieuTu = $tu.val().replace(prefix, '').replace(suffix, '');
    var soHieuTuInt = parseInt(soHieuTu, 10);

    // Kiểm tra nếu không phải số thì thoát
    if (isNaN(soHieuTuInt)) {
        NTS.canhbao("Không thể phân tích số hiệu từ:", $tu.val());
        return;
    }

    // Tính số hiệu đến = từ + số lượng - 1
    var soHieuDenInt = soHieuTuInt + soLuong - 1;

    // Giữ định dạng số có số 0 đầu nếu có
    var doDaiSo = soHieuTu.length;
    var soHieuDenStr = soHieuDenInt.toString().padStart(doDaiSo, '0');

    // Gán vào input đích
    var giaTriMoi = prefix + soHieuDenStr + suffix;
    $den.val(giaTriMoi);
}

function kiemTraChenhLechSoHieu(data) {
    var $tu = $('#inputsohieutu' + data);
    var $den = $('#inputsohieuden' + data);

    var prefix = $tu.attr('prefix') || '';
    var suffix = $tu.attr('suffix') || '';

    // Lấy và tách phần số
    var soTu = $tu.val().replace(prefix, '').replace(suffix, '');
    var soDen = $den.val().replace(prefix, '').replace(suffix, '');

    var numTu = parseInt(soTu, 10);
    var numDen = parseInt(soDen, 10);

    // Kiểm tra hợp lệ
    if (isNaN(numTu) || isNaN(numDen)) {
        NTS.canhbao("Không thể phân tích số hiệu.");
        return false;
    }

    // So sánh
    if (numDen < numTu) {
         NTS.canhbao("Số hiệu đến không được nhỏ hơn số hiệu từ.");
        return false;
    }

    return true;
}

$(document).on('blur', '.inputsohieuden', function () {
    var data = $(this).attr('data');
    kiemTraChenhLechSoHieu(data);
});

//#endregion








