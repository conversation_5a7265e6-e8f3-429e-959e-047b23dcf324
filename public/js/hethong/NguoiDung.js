//#region Global
var tempthem = "them";
const btnThaoTacg2 = function (cell, formatterParams, onRendered) {
    return formaterbtnThaoTac2(cell.getRow().getData().id, "btnXoaGrid2");
};

window.ganTatCa = false;
let selectedPermId = null;
const quyenMap = {
    xem: "Xem",
    them: "Them",
    sua: "Sua",
    xoa: "Xoa",
    in: "In",
    xuatexcel: "XuatExcel",
    nhapexcel: "NhapExcel",
    unpublish: "P1",
    admin: "P2",
    superview: "P3",
    superdelete: "P4",
};
const quyenMapRev = {
    Xem: "Xem", // front "Xem" → back "xem"
    Them: "Them", // front "Them" → back "them"
    Xoa: "Xoa", // front "Xoa" → back "xoa"
    Sua: "Sua", // front "Sua" → back "sua"
    In: "In", // front "In" → back "in"
    "Xuat Excel": "XuatExcel", // front "XuatExcel" → back "xuatexcel"
    "Nhap Excel": "NhapExcel", // front "NhapExcel" → back "nhapexcel"
    P1: "P1", // front "P1" → back "unpublish"
    P2: "P2", // front "P2" → back "admin"
    P3: "P3", // front "P3" → back "superview"
    P4: "P4", // front "P4" → back "superdelete"
};
const flagOrder = [
    "xem", // index 0
    "them", // 1
    "sua", // 2
    "xoa", // 3
    "in", // 4
    "xuatexcel", // 5
    "nhapexcel", // 6
    "unpublish", // 7
    "admin", // 8
    "superview", // 9
    "superdelete", // 10
];
const comboConfig = {
    columns: 2,
    indexValue: 0,
    indexText: 1,
    indexText1: 2,
    textShowTatCa: "-Chọn-",
    showTatCa: true,
};
const commonColumnConfig = {
    headerHozAlign: "center",
    HeaderVertAlign: "center",
    vertAlign: "middle",
};
var headderTT = `<a class="black" title="Thao tác"><i style="font-weight:bold" class="fa fa-ellipsis-h bigger-130" aria-hidden="true"></i></a>`;
// common Tabulator options
const commonOpts = {
    layout: "fitColumns",
    pagination: "local",
    height: "80vh",
    paginationSize: 50,
    paginationSizeSelector: [50, 150, 200, 500, 1000],
    locale: true,
    paginationCounter: "rows",
    langs: TabulatorLangsVi,
    placeholder: "Không có dữ liệu",
    ajaxResponse: function (url, params, response) {
        // response should return { data: [...], last_page: x, total: y }
        return response;
    },
};
//#endregion

async function loadDataCombos(configs) {
    const promises = configs.map((cfg) => NTS.loadDataComboAsync(cfg, "GET"));
    await Promise.all(promises);
}

var btnThaoTacg1 = function (cell) {
    return `<div class="show-or-hide"><a class='text-primary btnSuaGrid1 btn-nts-sua' title="Sửa" data='${
        cell.getData().id
    }'><i class="fa fa-pencil"></i></a></b>&ensp;<a class='text-danger btnXoaGrid1 btn-nts-xoa' title="Xoá" data='${
        cell.getData().id
    }'><i class='fa fa-trash-o'></i></a>&ensp;<a class='text-success btnReset1 btn-nts-reset' title="Làm mới mật khẩu" data='${
        cell.getData().id
    }'><i class='fa fa-refresh'></i></a></div>`;
    /*return formaterbtnThaoTac(cell.getData().UserID);*/
};
var fmTrangThai = function (cell) {
    return formaterDangSD(cell.getValue(), cell.getData().id);
};

//#region Events
document.getElementById("TatCa").addEventListener("change", function () {
    const checked = this.checked;
    document.querySelectorAll("input.quyen").forEach((cb) => {
        if (cb.id !== "TatCa") {
            cb.checked = checked;
        }
    });
});

document.getElementById("GanTatCa").addEventListener("change", function () {
    window.ganTatCa = this.checked;
});

//#endregion
function parseDateDMY(str) {
    const [d, m, y] = (str || "").split("/").map((v) => parseInt(v, 10));
    if (!d || !m || !y) return NaN;
    return new Date(y, m - 1, d);
}

$(function () {
    //Thêm nút khoá/mở tài khoản
    $("#bulk-actions .dropdown .dropdown-menu").append(`
        <li>
          <button class="dropdown-item text-warning" id="TrangThaiCapNhat">
            <i class="fa fa-lock me-2"></i>Khoá/Mở tài khoản
          </button>
        </li>
      `);

    //#region Grid người dùng
    // --- Grid1: Users list ---
    window.Grid1 = new Tabulator("#Grid1", {
        ...commonOpts,
        ajaxURL: window.Laravel.getAllUsers,
        ajaxParams: {},
        selectable: true,
        selectRowOnClick: false,

        columns: [
            {
                formatter: "rowSelection", // built-in checkbox formatter
                titleFormatter: "rowSelection", // puts a “select all” checkbox in the header
                hozAlign: "center",
                vertAlign: "middle",
                headerHozAlign: "center",
                width: 40,
                print: false, // hide on print/export if you like
                frozen: true,
                headerSort: false,
            },
            {
                title: headderTT,
                formatter: btnThaoTacg1,
                headerHozAlign: "center",
                hozAlign: "center",
                width: 80,
                headerSort: false,
                headerHozAlign: "center",
                HeaderVertAlign: "center",
                vertAlign: "middle",
            },
            { title: "ID", field: "id", visible: false },
            {
                title: "Họ và tên",
                field: "name",
                headerHozAlign: "center",
                visible: false,
                ...commonColumnConfig,
            },
            {
                title: "Tên đăng nhập",
                field: "username",
                hozAlign: "left",
                headerHozAlign: "center",
                ...commonColumnConfig,
            },
            {
                title: "Tên đơn vị",
                field: "TenDonVi",
                hozAlign: "left",
                minWidth: 200,

                headerHozAlign: "center",
                ...commonColumnConfig,
            },
            {
                title: "Nhóm người dùng",
                field: "TenNhomNguoiDung",
                hozAlign: "left",
                headerHozAlign: "center",
                ...commonColumnConfig,
            },

            {
                title: "Email",
                field: "email",
                hozAlign: "left",
                headerHozAlign: "center",

                formatter: "textarea",
                widthGrow: 1, // give more space to email
                ...commonColumnConfig,
            },
            {
                title: "Ngày tạo",
                field: "created_at",
                hozAlign: "center",
                headerHozAlign: "center",
                width: 100,
                formatter: "textarea",
                ...commonColumnConfig,
                sorter: (a, b) => {
                    const da = parseDateDMY(a),
                        db = parseDateDMY(b);
                    // fallback to string compare if parse failed
                    if (isNaN(da) || isNaN(db)) return a.localeCompare(b);
                    return da - db;
                },
            },
            {
                title: "Trạng thái sử dụng",
                field: "TrangThai",
                hozAlign: "center",
                headerHozAlign: "center",
                vertAlign: "middle",
                formatter: fmTrangThai,
                headerSort: false,
                width: 125,
                headerHozAlign: "center",
                ...commonColumnConfig,
            },
        ],
        footerElement: "<span id='row-countg1'></span>",
        dataLoaded: updateFooterGrid1,
        dataFiltered: updateFooterGrid1,
    });

    bindGridSearch("#timKiem", Grid1, updateFooterGrid1);

    $(document).ready(function () {
        setTimeout(() => {
            $("#timKiem").val("");
        }, 150);
    });

    Grid1.on("rowDblClick", (e, row) => {
        // pass the correct ID field into your edit function
        suaUser(row.getData().id);
        $("#UserID").value(row.getData().id);
    });

    Grid1.on("rowSelectionChanged", function (data) {
        let count = data.length;
        if (count && count > 0) {
            $("#bulkActionsDropdown").show();
        } else {
            $("#bulkActionsDropdown").hide();
        }
    });

    $("#XoaChon").on("click", async function () {
        // grab selected rows
        let rows = Grid1.getSelectedRows(); // Tabulator RowComponent[]
        if (!rows.length) {
            return alert("Vui lòng chọn ít nhất một dòng để xoá.");
        }

        CanhBaoXoa(async () => {
            // extract your PK field (e.g. maKyThi or id)
            let ids = rows.map((r) => r.getData().id);
            try {
                var result = await NTS.getAjaxAPIAsync(
                    "DELETE",
                    Laravel.bulkDeleteUser,
                    { ids }
                );
                if (!result.err) {
                    await Grid1.setData();
                    NTS.thanhcong(result.msg);
                } else {
                    result.canhbao
                        ? NTS.canhbao(result.msg)
                        : NTS.loi(result.msg);
                }
            } catch (error) {
                console.error("Lỗi khi xóa:", error);
                NTS.loi("Có lỗi xảy ra khi xóa dữ liệu");
            }
        });
    });

    $("#TrangThaiCapNhat").on("click", async function () {
        // grab selected rows
        let rows = Grid1.getSelectedRows(); // Tabulator RowComponent[]
        if (!rows.length) {
            return alert("Vui lòng chọn ít nhất một dòng để xoá.");
        }
        let ids = rows.map((r) => r.getData().id);
        let payload = {
            IDs: ids,
            strBang: "users",
            strCotID: "TrangThai",
            value: true,
        };
        const selectedCount = payload.IDs.length;
        $.confirm({
            title: `<span class="text-dark">Cảnh báo</span>`,
            type: "blue",
            icon: "fa fa-question-circle",
            typeAnimated: true,
            theme: "material",
            columnClass: "col-md-6 col-md-offset-3",
            draggable: false, // disable dragging the dialog
            dragWindow: false, // disable dragging the window itself
            content: `
              <div>
                Bạn đang thao tác trên <strong>${selectedCount}</strong> tài khoản đã chọn.
              </div>
              <ul style="margin-top: .5em;">
                <li><strong>Mở khóa</strong>: Các tài khoản sẽ được kích hoạt và cho phép đăng nhập lại.</li>
                <li><strong>Khóa tài khoản</strong>: Các tài khoản sẽ bị vô hiệu hóa, không thể đăng nhập.</li>
              </ul>
              <div style="margin-top: 1em; font-weight: bold;">
                Vui lòng chọn hành động bạn muốn thực hiện:
              </div>
            `,
            buttons: {
                unlock: {
                    text: '<i class="fa fa-unlock"></i> Mở khóa',
                    btnClass: "btn-success",
                    action: async function () {
                        payload.value = true; // mở tài khoản
                        try {
                            var result = await NTS.getAjaxAPIAsync(
                                "POST",
                                Laravel.bulkDangSD,
                                payload
                            );
                            if (!result.err) {
                                await Grid1.setData();
                                NTS.thanhcong(result.msg);
                            } else {
                                result.canhbao
                                    ? NTS.canhbao(result.msg)
                                    : NTS.loi(result.msg);
                            }
                        } catch (error) {
                            console.error("Lỗi khi xóa:", error);
                            NTS.loi("Có lỗi xảy ra khi xóa dữ liệu");
                        }
                    },
                },
                lock: {
                    text: '<i class="fa fa-lock"></i> Khóa tài khoản',
                    btnClass: "btn-warning",
                    action: async function () {
                        payload.value = false; // khoá tài khoản
                        try {
                            var result = await NTS.getAjaxAPIAsync(
                                "POST",
                                Laravel.bulkDangSD,
                                payload
                            );
                            if (!result.err) {
                                await Grid1.setData();
                                NTS.thanhcong(result.msg);
                            } else {
                                result.canhbao
                                    ? NTS.canhbao(result.msg)
                                    : NTS.loi(result.msg);
                            }
                        } catch (error) {
                            console.error("Lỗi khi xóa:", error);
                            NTS.loi("Có lỗi xảy ra khi xóa dữ liệu");
                        }
                    },
                },
                cancel: {
                    text: '<i class="fa fa-times"></i> Huỷ Bỏ',
                    btnClass: "btn-danger",
                    action: function () {},
                },
            },
        });
    });

    checkDangSD(".checkTrangThai", "users", "TrangThai");
    $(document).on("click", ".btnSuaGrid1", async function () {
        if (!(await ntspermiss.sua)) {
            NTS.canhbao(
                "User bạn đang sử dụng không thể thực hiện thao tác cập nhật. Vui lòng kiểm tra lại."
            );
            return false;
        }
        let id = $(this).attr("data");
        $("#UserID").value(id);
        suaUser(id);
    });

    $(document).on("click", ".btnXoaGrid1", async function () {
        if (!(await ntspermiss.xoa)) {
            NTS.canhbao(
                "User bạn đang sử dụng không thể thực hiện thao tác thêm xoá. Vui lòng kiểm tra lại."
            );
            return false;
        }
        let idXoa = $(this).attr("data");

        var result_ktxoa = await NTS.getAjaxAPIAsync(
            "GET",
            window.Laravel.layouts.KiemTraXoa,
            {
                ma: idXoa,
                model: "User",
            }
        );

        if (!result_ktxoa.Err) {
            if (result_ktxoa.Result == null || result_ktxoa.Result == "") {
                CanhBaoXoa(async () => {
                    await NTS.getAjaxAPIAsync(
                        "DELETE",
                        window.Laravel.deleteUser(idXoa)
                    );
                    NTS.thanhcong("Xóa dữ liệu thành công!");
                    Grid1.setData();
                });
            } else CanhBaoDuLieuTrangThai(result_ktxoa.Result);
        }
        // Lỗi khi kiểm tra xóa
        else
            result_ktxoa.CanhBao
                ? NTS.canhbao(result_ktxoa.Msg)
                : NTS.loi(result_ktxoa.Msg);
    });
    //#endregion

    //#region Grid phân quyền
    // --- Grid2: User permissions ---
    window.Grid2 = new Tabulator("#Grid2", {
        ...commonOpts,
        selectable: 1,
        ajaxURL: window.Laravel.getAllFunctions,
        ajaxResponse: (url, params, response) => {
            if (response.Err) {
                // optionally handle errors here
                console.error("API error:", response.Msg, response.Logs);
                return [];
            }
            return response.Result;
        },
        columns: [
            {
                title: "<button id='btnThemMoiQuyen' class='btn btn-xs btn-primary'><i class='fa fa-plus'></i></button>",
                hozAlign: "center",
                formatter: btnThaoTacg2,
                width: 60,
                headerSort: false,
                ...commonColumnConfig,
            },
            { title: "UserID", field: "UserID", visible: false },
            { title: "UserPerID", field: "UserPerID", visible: false },
            { title: "MenuID", field: "MenuID", visible: false },
            {
                title: "Mã chức năng",
                field: "MenuCode",
                hozAlign: "left",
                width: 150,
                ...commonColumnConfig,
            },
            {
                title: "Chức năng",
                field: "TenMenu",
                hozAlign: "left",
                ...commonColumnConfig,
            },
            {
                title: "Quyền",
                field: "Quyen",
                hozAlign: "left",
                width: 400,
                ...commonColumnConfig,
            },
        ],
        footerElement: "<span id='row-countg2'></span>",
        dataLoaded: updateFooterGrid2,
        dataFiltered: updateFooterGrid2,
    });
    bindGridSearch("#timKiem2", Grid2, updateFooterGrid2);

    Grid2.on("rowClick", function (e, row) {
        let data = row.getData();
        selectedPermId = data.MenuID;
        resetQuyenInput();

        let perms = data.Quyen.split(";");

        perms.forEach((perm) => {
            $(`#${quyenMapRev[removeDiacritics(perm)]}`).prop(
                "checked",
                perms.includes(perm)
            );
        });

        $("#ChonAll,#GanTatCa").prop("checked", false);
    });

    //#endregion

    //#region Grid thêm chức năng
    // --- Grid3: Function list for assignment ---
    window.Grid3 = new Tabulator("#Grid3", {
        ...commonOpts,
        ajaxURL: window.Laravel.getAllFunctions,
        ajaxResponse: (url, params, response) => {
            if (response.Err) {
                // optionally handle errors here
                console.error("API error:", response.Msg, response.Logs);
                return [];
            }
            return response.Result;
        },
        selectable: true,
        selectablePersistence: false,
        columns: [
            {
                formatter: "rowSelection",
                titleFormatter: "rowSelection",
                hozAlign: "center",
                headerHozAlign: "center",
                vertAlign: "middle",
                cellClick: (e, cell) => cell.getRow().toggleSelect(),
                width: 80,
                headerSort: false,
                ...commonColumnConfig,
                formatter: "textarea",
            },
            {
                title: "Mã",
                field: "MaChucNang",
                hozAlign: "left",
                width: 110,
                ...commonColumnConfig,
                formatter: "textarea",
            },
            {
                title: "Chức năng",
                field: "TenChucNang",
                hozAlign: "left",
                width: 180,
                ...commonColumnConfig,
                formatter: "textarea",
            },
            {
                title: "Thuộc chức năng",
                field: "TenMenuCha",
                hozAlign: "left",
                ...commonColumnConfig,
                formatter: "textarea",
            },

            { title: "MenuID", field: "id", visible: false },
        ],
        footerElement: "<span id='row-countg3'></span>",
        dataLoaded: updateFooterGrid3,
        dataFiltered: updateFooterGrid3,
    });
    bindGridSearch("#timKiemChucNang", Grid3, updateFooterGrid3);
    $(document).on("click", "#btnThemMoiQuyen", function () {
        if (!QuyenThem()) {
            return false;
        }
        $("#mdChucNang_us").modal("show");

        // this will trigger Tabulator to fetch with the current #UserID
        if ($("#UserID").val()) {
            Grid3.setData(
                window.Laravel.getAllFunctions +
                    `?id=${$("#UserID").val()}&groupId=${$(
                        "#NhomNguoiDung"
                    ).val()}`
            );
        }
    });
    //#endregion

    // initial load
    Grid1.setData();
});

$(document).on(
    "click change",
    ".tabulator-page, .tabulator-page-size, .tabulator-footer",
    () => {
        updateFooterGrid1();
        updateFooterGrid2();
        updateFooterGrid3();
    }
);

//#region Lưu modal chính
$(document).on("click", "#btnLuuVaDong", async function () {
    var favorite = [];
    $.each($("input.quyen:checked"), function () {
        favorite.push($(this).val());
    });
    var Permission = "";
    Permission = favorite.join(";");
    const email = $("#Email").val().trim();
    debugger;
    if (email && !validateEmail(email)) {
        NTS.canhbao("Email không đúng định dạng");
        return false;
    }
    if ($("#TenDangNhap").value().indexOf(" ") !== -1) {
        NTS.canhbao("Tên đăng nhập phải viết liền");
        return false;
    }
    if (isEmty($("#TenDangNhap").value())) {
        NTS.canhbao("Tên đăng nhập không được để trống");
        return false;
    }

    if (tempthem == "them") {
        if (!QuyenThem()) {
            return false;
        }
        if (validatePassword($("#MatKhau").value()) == false) return false;
    }
    if (isEmty($("#NhomNguoiDung").value())) {
        NTS.canhbao("Nhóm người dùng không được để trống");
        return false;
    }
    if (isEmty($("#DonVi").value())) {
        NTS.canhbao("Đơn vị không được để trống");
        return false;
    }
    if (isEmty($("#TrangChu").value())) {
        NTS.canhbao("Trang chủ không được để trống");
        return false;
    }
    var param = new Array();
    param[0] = tempthem;
    param[1] = $("#TenDangNhap").value();
    param[2] = $("#TrangThai").value();
    param[3] = $("#MatKhau").value();
    // param[4] = $("#MaXacNhan").value();
    param[5] = $("#Email").value();
    param[6] = $("#NhomNguoiDung").value();
    param[7] = $("#DonVi").value();
    param[8] = $("#UserID").val();
    param[9] = Permission;
    param[10] = "";
    param[11] = $("#TrangChu").value();
    param[12] = $("#NhanVienID").value();
    var result = await NTS.getAjaxAPIAsync(
        "POST",
        "/hethong/nguoidung/luuthongtin",
        {
            data: param,
        }
    );
    if (result.split("_")[0] == "1") {
        tempthem = "sua";
        $("#UserID").value(result.split("_")[1]);
        Grid1.setData();
        Grid2.setData(window.Laravel.getPermissions(result.split("_")[1]));
        NTS.thanhcong(result.split("_")[2]);
        return false;
    }
    if (result.split("_")[0] == "0") {
        NTS.canhbao(result.split("_")[1]);
        return false;
    } else {
        NTS.loi("Thêm thất bại");
        return false;
    }
});
//#endregion

//#region Export
$("#btnXuatExcel").on("click", async () => {
    if (!(await ntspermiss.xuatexcel)) {
        NTS.canhbao("User của bạn không có quyền thực hiện thao tác này");
    }
    xemTruocExport("excel");
});

$(document).on("click", "#TatCa", async function () {});
document.getElementById("btnPrint").addEventListener("click", () => {
    Grid1.print(false, true);
    return false;
});

//#endregion

// any time you need to refresh data remotely:
function refreshGrids() {
    Grid1.setData();
    Grid2.setData({ ID: $("#UserID").val() });
    Grid3.setData({ UserID: $("#UserID").val() });
}

function updateFooterGrid1() {
    // var el = document.getElementById("row-countg1");
    // if (Grid1 != undefined) {
    //     var Grid = Grid1;
    //     if (Grid.rowManager.activeRows.length > 0) {
    //         el.innerHTML =
    //             "Dòng: " +
    //             (Grid.rowManager.table.footerManager.links[0].page *
    //                 Grid.rowManager.table.footerManager.links[0].size -
    //                 Grid.rowManager.table.footerManager.links[0].size +
    //                 1) +
    //             " - " +
    //             (Grid.rowManager.table.footerManager.links[0].page *
    //                 Grid.rowManager.table.footerManager.links[0].size -
    //                 Grid.rowManager.table.footerManager.links[0].size +
    //                 Grid.rowManager.displayRowsCount) +
    //             " của " +
    //             Grid.rowManager.activeRows.length +
    //             " - ";
    //     } else {
    //         el.innerHTML = "Dòng: 0 - 0 của 0 - ";
    //     }
    // }
}

function updateFooterGrid2() {
    var el = document.getElementById("row-countg2");
    if (Grid2 != undefined) {
        var Grid = Grid2;
        if (Grid.rowManager.activeRows.length > 0) {
            el.innerHTML =
                "Dòng: " +
                (Grid.rowManager.table.footerManager.links[0].page *
                    Grid.rowManager.table.footerManager.links[0].size -
                    Grid.rowManager.table.footerManager.links[0].size +
                    1) +
                " - " +
                (Grid.rowManager.table.footerManager.links[0].page *
                    Grid.rowManager.table.footerManager.links[0].size -
                    Grid.rowManager.table.footerManager.links[0].size +
                    Grid.rowManager.displayRowsCount) +
                " của " +
                Grid.rowManager.activeRows.length +
                " - ";
        } else {
            el.innerHTML = "Dòng: 0 - 0 của 0 - ";
        }
    }
}

function updateFooterGrid3() {
    var el = document.getElementById("row-countg3");
    if (Grid3 != undefined) {
        var Grid = Grid3;
        if (Grid.rowManager.activeRows.length > 0) {
            el.innerHTML =
                "Dòng: " +
                (Grid.rowManager.table.footerManager.links[0].page *
                    Grid.rowManager.table.footerManager.links[0].size -
                    Grid.rowManager.table.footerManager.links[0].size +
                    1) +
                " - " +
                (Grid.rowManager.table.footerManager.links[0].page *
                    Grid.rowManager.table.footerManager.links[0].size -
                    Grid.rowManager.table.footerManager.links[0].size +
                    Grid.rowManager.displayRowsCount) +
                " của " +
                Grid.rowManager.activeRows.length +
                " - ";
        } else {
            el.innerHTML = "Dòng: 0 - 0 của 0 - ";
        }
    }
}
var TrangChu = null;
var NhanVien = null;

//#region Sua nguoi dung
async function suaUser(id) {
    if (!QuyenSua()) {
        return false;
    }

    $("#tieuDeModal").text("Cập nhật thông tin người dùng");
    $("#mdThemMoi").modal("show");
    tempthem = "sua";
    $("input[name=quyen]").attr("checked", false);
    $("#TatCa").attr("checked", false);
    $("#QuyenTatCa").attr("checked", false);
    $("#TenDangNhap").attr("disabled", "disabled");
    $("#UserID").val(id);
    $("#groupMatKhau").hide();

    const result = await NTS.getAjaxAPIAsync(
        "GET",
        window.Laravel.loadEditData(id),
        {}
    );

    if (result.length > 0) {
        const data = result[0];
        await loadDataCombos([
            {
                name: "#DonVi",
                ajaxUrl: window.Laravel.getAllDonVi,
                ...comboConfig,
            },
            {
                name: "#NhomNguoiDung",
                ajaxUrl: window.Laravel.getAllNhomNguoiDung,
                ...comboConfig,
            },
        ]);

        $("#NhomNguoiDung").value(result[0].NhomNguoiDungID);
        $("#DonVi").value(result[0].DonViID);

        Grid2.setData(window.Laravel.getPermissions($("#UserID").val())),
            //Điền dữ liệu
            $("#MatKhau").attr("disabled", true);
        $("#TenDangNhap").value(result[0].username);
        $("#TrangThai").value(result[0].TrangThai);
        $("#MatKhau").value("fixedpasswordbecausewerenotshowing");
        $("#Email").value(result[0].email);
        TrangChu = result[0].ChucNangID_TrangChu;
        NhanVien = result[0].NhanVienID;

        UpdateLabelTrangThai("#TrangThai");
    } else {
        NTS.loi("Tải dữ liệu sửa thất bại");
    }
}
//#endregion
$("#TrangThai").on("change", () => UpdateLabelTrangThai("#TrangThai"));
function UpdateLabelTrangThai(selector) {
    UpdateLabel(selector, "Đang sử dụng", "Ngưng sử dụng");
}

//#region Lưu thêm chức năng
$("#btnChonDoiTuongVaDong_us").click(async function () {
    // gather the CSV of IDs
    let param = Grid3.getSelectedData()
        .map((r) => r.id)
        .join(",");
    if (!param) {
        NTS.canhbao("Vui lòng chọn chức năng trước khi thực hiện thao tác");
        return false;
    }

    const par = [param, $("#UserID").val(), $("#NhomNguoiDung").val()];

    var result = await NTS.getAjaxAPIAsync(
        "POST",
        window.Laravel.addFunctions,
        { saveData: par }
    );

    if (result.split("_")[0] == "1") {
        Grid2.setData(window.Laravel.getPermissions($("#UserID").val()));
        NTS.thanhcong("Thêm mới chức năng thành công!");
    } else {
        NTS.loi("Thêm chức năng thất bại!");
    }

    $("#mdChucNang_us").modal("hide");
    return false;
});
//#endregion

//#region Thêm mới user
$("#btnThemMoi").click(async function () {
    if (!QuyenThem()) {
        return false;
    }

    await loadDataCombos([
        {
            name: "#NhomNguoiDung",
            ajaxUrl: window.Laravel.getAllNhomNguoiDung,
            ...comboConfig,
        },
        {
            name: "#DonVi",
            ajaxUrl: window.Laravel.getAllDonVi,
            ...comboConfig,
        },
        {
            name: "#TrangChu",
            ajaxUrl:
                window.Laravel.getAllFunctions +
                `?id=${$("#UserID").val()}&groupId=${$(
                    "#NhomNguoiDung"
                ).val()}`,
            ...comboConfig,
        },
    ]);

    $("#TenDangNhap").value("");
    $("#TrangThai").value(true);
    $("#groupMatKhau").show();
    $("#MatKhau").value("@Abc@123");
    // $("#MaXacNhan").value("654321");
    $("#MatKhau").attr("disabled", false);
    $("#Email").value("");
    $("#NhomNguoiDung").value("");
    $("#DonVi").value("");
    $("#TrangChu").value("");
    $("#NhanVienID").value("");
    $("#UserID").val("");
    $("input[name=quyen]").attr("checked", false);
    $("#TatCa").attr("checked", false);
    $("#QuyenTatCa").attr("checked", false);
    $("#TenDangNhap").removeAttr("disabled");
    Grid2.clearData();
    tempthem = "them";
    $("#mdThemMoi").modal("show");
    $("#tieuDeModal").text("Thêm mới thông tin người dùng");
});
//#endregion

$("#DonVi").on("change", async function () {
    NTS.loadDataComboAsync(
        {
            name: "#NhanVienID",
            ajaxUrl: window.Laravel.getAllNhanVien($("#DonVi").value() ?? ""),
            ...comboConfig,
        },
        "GET"
    ).then(() => {
        $("#NhanVienID").value(NhanVien);
    });
});

$("#NhomNguoiDung").on("change", function () {
    NTS.loadDataComboAsync(
        {
            name: "#TrangChu",
            ajaxUrl:
                window.Laravel.getAllFunctions +
                `?id=&groupId=${$("#NhomNguoiDung").val()}`,
            ...comboConfig,
        },
        "GET"
    ).then(() => {
        $("#TrangChu").value(TrangChu);
    });
});

//#region Lưu phân quyền
$("#btnGanQuyen").on("click", async () => {
    if (!QuyenThem()) return false;

    const ganTatCaCN = $("#GanTatCaChucNang").is(":checked");

    if (!selectedPermId && ganTatCaCN == false) {
        NTS.canhbao("Vui lòng chọn chức năng để phân quyền");
        return;
    }
    // build an 11-element boolean array in the exact order flagOrder expects:
    const bits = flagOrder.map((flagKey) => {
        const chkId = quyenMap[flagKey];
        return $(`#${chkId}`).is(":checked");
    });

    // then tack on setAll, groupId, permId:
    let idsToApply = [];
    if (ganTatCaCN) {
        // all rows in Tabulator
        idsToApply = Grid2.getData().map((row) => ({
            id: row.MenuID,
            name: row.TenMenu, // giữ lại Tên Menu để hiển thị
        }));
    } else {
        idsToApply = [
            {
                id: selectedPermId,
                name:
                    Grid2.getData().find((r) => r.MenuID === selectedPermId)
                        ?.TenMenu || "",
            },
        ];
    }
    console.log(idsToApply);

    const requestPromises = idsToApply.map(({ id, name }) => {
        const payload = [
            ...bits,
            $("#GanTatCa").is(":checked"),
            $("#UserID").val(),
            id,
        ];

        return NTS.getAjaxAPIAsync("PUT", window.Laravel.updatePermission, {
            saveData: payload,
        })
            .then((res) => ({ res, id, name }))
            .catch((error) => ({ error, id, name }));
    });

    try {
        const results = await Promise.all(requestPromises);

        let anySuccess = false;
        let anyError = false;

        results.forEach(({ res, error, id, name }) => {
            const label = name ? `${name} (ID ${id})` : `ID ${id}`;

            if (error) {
                anyError = true;
                NTS.loi(
                    `Lỗi cập nhật chức năng ${label}: ${error.message || error}`
                );
                return;
            }

            if (res.err) {
                anyError = true;
                const msg = `Lỗi cập nhật chức năng ${label}: ${res.msg}`;
                res.canhbao ? NTS.canhbao(msg) : NTS.loi(msg);
                return;
            }

            if (!res.success) {
                anyError = true;
                NTS.canhbao(
                    `Không có gì được cập nhật cho chức năng ${label}: ${res.msg}`
                );
                return;
            }

            anySuccess = true; // at least one row really updated
        });

        if (anySuccess) {
            NTS.thanhcong("Phân quyền thành công cho các chức năng.");
            reloadPerms();
            resetQuyenInput();
        } else if (!anyError) {
            NTS.canhbao("Không có chức năng nào được cập nhật.");
        }
    } catch (e) {
        NTS.loi("Cập nhật thông tin thất bại: " + (e.message || e));
    }
});
//#endregion

//#region Reset password
$(document).on("click", ".btnReset1", function () {
    var ID = $(this).attr("data");
    ResetMatKhau(ID);
});
function ResetMatKhau(ID) {
    $.confirm({
        title: '<span class="text-dark">Thông báo!</span>',
        type: "red",
        icon: "fa fa-warning",
        typeAnimated: true,
        theme: "material",
        columnClass: "col-md-5 col-md-offset-3 w-max-400px",
        content:
            'Xác nhận đổi mật khẩu về mặt định [@Abc@123] <b>"Xác nhận"</b>, hủy bỏ chọn <b>"Hủy bỏ"</b>',
        buttons: {
            confirm: {
                text: '<i class="fa fa-check"></i> Xác nhận',
                btnClass: "btn-primary",
                keys: ["shift"],
                action: async function () {
                    debugger;
                    var Data = await NTS.getAjaxAPIAsync(
                        "PUT",
                        window.Laravel.doiMatKhau,
                        {
                            param: {
                                id: ID,
                                newPassword: "@Abc@123",
                            },
                        }
                    );
                    debugger;
                    if (Data.split("_")[0] == "0") {
                        NTS.canhbao(Data.split("_")[1]);
                    } else {
                        NTS.thanhcong("Đổi mật khẩu thành công!");
                    }
                },
            },
            cancel: {
                text: '<i class="fa fa-close"></i> Hủy bỏ',
                btnClass: "btn-danger",
                keys: ["enter", "esc", "space"],
            },
        },
    });
}
//#endregion

//#region Validation
function validateEmail($email) {
    var emailReg = /^([\w-\.]+@([\w-]+\.)+[\w-]{2,4})?$/;
    return emailReg.test($email);
}
var regularExpression =
    /^(?=.*[0-9])(?=.*[!@#$%^&*])[a-zA-Z0-9!@#$%^&*]{6,16}$/;
function validatePassword(p) {
    if (p.length < 8) {
        NTS.canhbao("Mật khẩu phải lớn hơn 8 ký tự");
        return false;
    }
    if (p.search(/[0-9]/) < 0) {
        NTS.canhbao("Mật khẩu phải chứa ít nhất 1 chữ số");
        return false;
    }
    if (p.search(/[a-z]/) < 0) {
        NTS.canhbao("Mật khẩu phải chứa ít nhất 1 ký tự chữ thường");
        return false;
    }
    if (p.search(/[A-Z]/) < 0) {
        NTS.canhbao("Mật khẩu phải chứa ít nhất 1 ký tự chữ hoa");
        return false;
    }

    if (p.search(/[!@#\$%\^&\*_]/) < 0) {
        NTS.canhbao("Mật khẩu phải chứa ít nhất 1 ký tự đặc biệt");
        return false;
    }
    return true;
}

function removeDiacritics(str) {
    // 1) normalize into decomposed form (NFD)
    // 2) strip out all the accent marks in the U+0300–U+036F range
    return str.normalize("NFD").replace(/[\u0300-\u036f]/g, "");
}
//#endregion

function resetQuyenInput() {
    let keys = Object.keys(quyenMap);
    keys.forEach((frontId) => {
        var id = quyenMap[frontId];
        $(`#${id}`).prop("checked", false);
    });
}

function reloadPerms() {
    if (!$("#UserID").val()) return;
    Grid2.setData(window.Laravel.getPermissions($("#UserID").val()));
}

function bindGridSearch(inputSelector, grid, updateFooterFn) {
    // listen for typing
    document.querySelector(inputSelector).addEventListener("keyup", (e) => {
        const v = e.target.value;
        if (v) {
            grid.setFilter(matchAny, { value: v });
        } else {
            grid.clearFilter();
        }
        updateFooterFn();
    });
}

//#region Export
function layDuLieuLuoi() {
    const data = Grid1.getData(); // assumes your Tabulator instance is in `table`
    const cols = Grid1.getColumns().filter(
        (c) => c.getField() && c.getField() !== "actions"
    );
    const headings = cols.map((c) => c.getDefinition().title);
    const rows = data.map((row) => {
        const obj = {};
        cols.forEach((col) => {
            const d = col.getDefinition();
            obj[d.title] =
                d.field === "fullName"
                    ? `${row.firstName} ${row.lastName}`
                    : row[d.field];
        });
        return obj;
    });
    return { headings, rows };
}

async function postAndDownload(url, data, filename) {
    const token = document.querySelector('meta[name="csrf-token"]').content;
    const res = await fetch(url, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "X-CSRF-TOKEN": token,
        },
        body: JSON.stringify(data),
    });
    if (!res.ok) return alert("Export lỗi: " + res.statusText);
    const blob = await res.blob();
    const blobUrl = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = blobUrl;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    a.remove();
    URL.revokeObjectURL(blobUrl);
}

async function xemTruocExport(type) {
    const { headings, rows } = layDuLieuLuoi();

    if (type === "pdf") {
        const res = await fetch(window.Laravel.exportPdfUrl, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "X-CSRF-TOKEN": document.querySelector(
                    'meta[name="csrf-token"]'
                ).content,
            },
            body: JSON.stringify({ headings, rows }),
        });
        if (!res.ok) return alert("Error generating PDF");
        const blob = await res.blob();
        const blobUrl = URL.createObjectURL(blob);
        document.getElementById("exportPreviewTitle").textContent =
            "PDF Preview";
        document.getElementById(
            "previewContent"
        ).innerHTML = `<iframe style="width:100%;height:75vh;border:none" src="${blobUrl}"></iframe>`;
        document.getElementById("confirmDownloadBtn").onclick = () => {
            postAndDownload(
                window.Laravel.exportPdfUrl,
                { headings, rows },
                `user_${Date.now()}.pdf`
            );
            bootstrap.Modal.getInstance(
                document.getElementById("exportPreviewModal")
            ).hide();
        };
    } else {
        // excel
        let html =
            '<div class="table-responsive"><table class="table table-bordered"><thead><tr>';
        headings.forEach((h) => (html += `<th>${h}</th>`));
        html += "</tr></thead><tbody>";
        rows.forEach((r) => {
            html += "<tr>";
            headings.forEach((h) => (html += `<td>${r[h] || ""}</td>`));
            html += "</tr>";
        });
        html += "</tbody></table></div>";
        document.getElementById("exportPreviewTitle").textContent =
            "Xem trước dữ liệu";
        document.getElementById("previewContent").innerHTML = html;
        document.getElementById("confirmDownloadBtn").onclick = () => {
            postAndDownload(
                window.Laravel.exportExcelUrl,
                { headings, rows },
                `user_${Date.now()}.xlsx`
            );
            bootstrap.Modal.getInstance(
                document.getElementById("exportPreviewModal")
            ).hide();
        };
    }

    bootstrap.Modal.getOrCreateInstance(
        document.getElementById("exportPreviewModal")
    ).show();
}

//#endregion
