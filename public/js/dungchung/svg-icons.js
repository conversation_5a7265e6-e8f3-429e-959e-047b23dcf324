const icons = {
  xem: `<i class="fa fa-eye nts-iconTT-xem nts-iconThaoTacs" aria-hidden="true"></i>`,
  them: `<i class="fa fa fa-plus nts-iconTT-them nts-iconThaoTacs" aria-hidden="true"></i>`,
  sua: `<i class="fa fa-pencil-square-o nts-iconTT-sua nts-iconThaoTacs" aria-hidden="true"></i>`,
  xoa: `<i class="fa fa-trash-o nts-iconTT-xoa nts-iconThaoTacs" aria-hidden="true"></i>`,
  xoact: `<i class="fa fa-spinner nts-iconTT-xoa ts-iconThaoTacs" aria-hidden="true"></i>`,
  in: `<i class="fa fa-print nts-iconTT-in nts-iconThaoTacs" aria-hidden="true"></i>`,
  excel: `<i class="fa fa-file-excel-o nts-iconTT-excel nts-iconThaoTacs " aria-hidden="true"></i>`,
  guiduyet: `<i class="fa fa-paper-plane nts-iconTT-guipheduyet nts-iconThaoTacs" aria-hidden="true"></i>`,
  duyet: `<i class="fa fa-check-circle nts-iconTT-pheduyet nts-iconThaoTacs" aria-hidden="true"></i>`,
  tuchoi: `<i class="fa fa-times-circle nts-iconTT-tuchoi nts-iconThaoTacs" aria-hidden="true"></i>`,
  capphat: `<i class="fa fa-code-fork nts-iconTT-capphat nts-iconThaoTacs" aria-hidden="true"></i>`,
  banhanh: `<i class="fa fa-calendar-check-o nts-iconTT-banhanh nts-iconThaoTacs" aria-hidden="true"></i>`,
  thuhoiBH: `<i class="fa fa-repeat nts-iconTT-thuhoiBH nts-iconThaoTacs" aria-hidden="true"></i>`,
  giao: `<i class="fa fa-paper-plane-o nts-iconTT-giao nts-iconThaoTacs" aria-hidden="true"></i>`,
  thietlap: `<i class="fa fa fa-gear nts-iconTT-thietlap nts-iconThaoTacs" aria-hidden="true"></i>`,
  ocr: `<svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 20 20" fill="none">
  <path d="M3 1C2.46957 1 1.96086 1.21071 1.58579 1.58579C1.21071 1.96086 1 2.46957 1 3V7.5H3V3H8V1H3ZM1 17V11.5H3V17H8V19H3C2.46957 19 1.96086 18.7893 1.58579 18.4142C1.21071 18.0391 1 17.5304 1 17ZM12 17V19H17C17.5304 19 18.0391 18.7893 18.4142 18.4142C18.7893 18.0391 19 17.5304 19 17V11.5H17V17H12ZM17 7.5H19V3C19 2.46957 18.7893 1.96086 18.4142 1.58579C18.0391 1.21071 17.5304 1 17 1H12V3H17V7.5Z" fill="#F76707"/>
  <path d="M6 5H14V6H6V5ZM5 8H15V9H5V8ZM6 11H14V12H6V11ZM5 14H15V15H5V14Z" fill="#F76707"/>
</svg>`,
};

/**
 * Return the HTML for an icon by key,
 * or an empty string if it doesn’t exist.
 */
function getIcon(name) {
  return icons[name] || "";
}
