{{-- resources/views/usergroup/index.blade.php --}}
@extends('layouts.layouts')

@section('title', '<PERSON><PERSON>ản l<PERSON>ư<PERSON>')

@push('styles')
    <style>
        #Grid2 .tabulator .tabulator-header,
        #Grid3 .tabulator .tabulator-header {
            max-height: 26px !important;
        }

        .input-group-append .input-group-text[data-bs-toggle="tooltip"] {
            cursor: pointer;
        }

        /* enlarge & restyle the Bootstrap tooltip */
        .tooltip .tooltip-inner {
            background-color: #fff !important;
            color: #000 !important;
            borer: 1px solid black;
            font-size: 1rem !important;
            padding: 0.5rem 0.75rem !important;
            max-width: none !important;
        }

        /* keep the arrow matching the white bg */
        .tooltip.bs-tooltip-top .tooltip-arrow::before {
            border-top-color: #fff !important;
        }

        /* keep your custom CSS here */
    </style>
@endpush

@section('content')
    <input type="hidden" id="UserID" />
    @php
        $filters = [
            // 1. <PERSON><PERSON> kỳ thi / Tên kỳ thi
            '<div class="row mb-3">
            <div class="col-md-6">
                <label class="form-label" for="MaKyThi_Loc">Mã kỳ thi</label>
                <input type="text" class="form-control input-sm" id="MaKyThi_Loc" placeholder="Nhập mã kỳ thi…">
            </div>
            <div class="col-md-6">
                <label class="form-label" for="TenKyThi_Loc">Tên kỳ thi</label>
                <input type="text" class="form-control input-sm" id="TenKyThi_Loc" placeholder="Nhập tên kỳ thi…">
            </div>
        </div>',

            // 2. Năm tổ chức / Cấp tổ chức
            '<div class="row mb-3">
            <div class="col-md-6">
                <label class="form-label" for="NamToChuc_Loc">Năm tổ chức</label>
                <select class="form-control input-sm" id="NamToChuc_Loc">
                    <option value="">— Chọn năm —</option>
                    <!-- JS sẽ populate các năm ở đây -->
                </select>
            </div>
            <div class="col-md-6">
                <label class="form-label" for="CapToChuc_Loc">Cấp tổ chức</label>
                <select class="form-control input-sm" id="CapToChuc_Loc">
                    <option value="">— Chọn cấp —</option>
                    <option value="quoc_gia">Quốc gia</option>
                    <option value="tinh_thanh_pho">Tỉnh/Thành phố</option>
                    <option value="truong">Trường</option>
                    <option value="khac">Khác</option>
                </select>
            </div>
        </div>',
        ];
    @endphp

    @include('partials.filter-panel', [
        'filters' => $filters,
        'actions' => null,
        'showTimKiemNangCao' => false,
        'showBulkActions' => true,
        'leftColRatio' => 6,
        'rightColRatio' => 6
    ])


    {{-- Quyền của người dùng --}}
    <div class="row" style="margin-top:4px">
        <div class="col-md-12 gx-0">
            <div id="Grid1" class="GridData"></div>
        </div>
    </div>

    <div class="modal fade show" id="mdThemMoi" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered" style="max-width: 80%;" role="document">
            <div class="modal-content">
                <div class="modal-header nts-modal">
                    <h5 class="modal-title" id="tieuDeModal"></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">

                    </button>
                </div>
                <div class="modal-body">

                    <fieldset class="KhungVien" style="margin-top:5px">
                        <legend>1. Thông tin người dùng</legend>
                        <div class="form-group">
                            <div class="row">

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="">Tên đăng nhập</label>
                                        <input class="form-control" maxlength="50" type="text" id="TenDangNhap" required />
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group" id="groupMatKhau">
                                        <label for="MatKhau">Mật khẩu</label>
                                        <div class="input-group">
                                            <input type="password" class="form-control" id="MatKhau" maxlength="50" required>
                                            <span class="input-group-text input-tooltip" data-bs-toggle="tooltip"
                                                data-bs-trigger="hover click" data-bs-placement="top"
                                                title="Mật khẩu mặc định @Abc@123">
                                                <i class="fas fa-info-circle" style="font-size: 1.25rem; color: #00c6cb;"></i>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="">Email</label>
                                        <input class="form-control" type="text" id="Email" />
                                    </div>

                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="">Nhóm người dùng</label>
                                        <select class="form-control" id="NhomNguoiDung" required></select>
                                    </div>

                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="">Đơn vị</label>
                                        <select class="form-control" id="DonVi" data-dropdown-parent="#mdThemMoi" required>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="">Nhân viên</label>
                                        <select class="form-control" data-dropdown-parent="#mdThemMoi" id="NhanVienID">
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="" for="TrangChu">Trang chủ</label>
                                        <select class="form-control" data-dropdown-parent="#mdThemMoi" id="TrangChu"
                                            required>
                                        </select>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </fieldset>
                    @php
                        // id ⇒ label
                        $perms = [
                            'Xem' => 'Xem', // xem
                            'Them' => 'Thêm', // them
                            'Xoa' => 'Xóa', // xoa
                            'Sua' => 'Sửa', // sua
                            'In' => 'In', // in
                            'XuatExcel' => 'Xuất Excel', // xuatexcel
                            'NhapExcel' => 'Nhập Excel', // nhapexcel
                            'P1' => 'P1', // unpublish
                            'P2' => 'P2', // admin
                            'P3' => 'P3', // superview
                            'P4' => 'P4', // superdelete
                        ];
                    @endphp

                    <div class="">
                        <div class="row d-flex align-items-stretch">
                            <div class="col-md-10">
                                <fieldset class="KhungVien h-100">
                                    <legend>2. Danh sách quyền người dùng</legend>
                                    <div class="">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="input-icon">
                                                    <input type="text" value="" class="form-control"
                                                        placeholder="Nội dung tìm kiếm ..." id="timKiem2"
                                                        autocomplete="off">
                                                    <span class="input-icon-addon">
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24"
                                                            height="24" viewBox="0 0 24 24" stroke-width="2"
                                                            stroke="currentColor" fill="none" stroke-linecap="round"
                                                            stroke-linejoin="round">
                                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                            <path d="M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0"></path>
                                                            <path d="M21 21l-6 -6"></path>
                                                        </svg>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="col">
                                            </div>
                                        </div>
                                        <div class="row" style="margin-top:4px">
                                            <div class="col-md-12">
                                                <div id="Grid2" class="GridData"></div>
                                            </div>
                                        </div>
                                        <br>
                                    </div>
                                </fieldset>
                            </div>
                            <div class="col-md-2">
                                <fieldset class="KhungVien h-100">
                                    <legend>3. Gán quyền</legend>
                                    <div id="danhSachQuyen">
                                        <label class="form-check">
                                            <input class="form-check-input" type="checkbox" id="TatCa">
                                            <span class="form-check-label">Chọn/Bỏ tất cả</span>
                                        </label>
                                        <hr>
                                        <!-- Permissions List -->
                                        <div class="row">
                                            @foreach ($perms as $id => $label)
                                                <div class="form-check">
                                                    <input class="form-check-input quyen" type="checkbox" id="{{ $id }}"
                                                        value="{{ $id }}">
                                                    <label class="form-check-label" for="{{ $id }}">{{ $label }}</label>
                                                </div>
                                            @endforeach
                                        </div>
                                        <hr>
                                        <div class="form-check mt-2">
                                            <input class="form-check-input" type="checkbox" id="GanTatCa">
                                            <label class="form-check-label" for="GanTatCa">Gán tất cả quyền</label>
                                        </div>
                                        <div class="form-check mt-2">
                                            <input class="form-check-input" type="checkbox" id="GanTatCaChucNang">
                                            <label class="form-check-label" for="GanTatCaChucNang">Gán quyền cho tất cả chức
                                                năng</label>
                                        </div>
                                        <button id="btnGanQuyen" class="btn btn-primary btn-them mt-3">
                                            <i class="fa fa-check"></i>&nbsp;&nbsp;Gán quyền
                                        </button>
                                    </div>
                                    <br>
                                </fieldset>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer bg-whitesmoke br" style="justify-content: space-between;">
                    <label style="margin-bottom: unset; float: left;" class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="TrangThai">
                        <label class="form-check-label custom-switch-description" for="TrangThai">Đang sử dụng</label>
                    </label>
                    <div>
                        <a href="#" class="btn btn-sm btn-outline-danger" data-bs-dismiss="modal"><i
                                class="fa fa-close"></i>&nbsp;Đóng (F4)</a>
                        <button href="#" id="btnLuuVaDong" class="btn btn-sm btn-success nts-color-luu">
                            <i class="ace-icon fa fa-floppy-o bigger-110"></i>&nbsp;Lưu (F9)
                        </button>
                    </div>

                </div>
            </div>
        </div>

    </div>

    {{-- Modal stays the same --}}
    <div class="modal fade show" id="mdChucNang_us" data-bs-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header nts-modal">
                    <h5 class="modal-title" id="tieuDeChonDoiTuong_us">Chọn chức năng</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">

                    <fieldset class="KhungVien">
                        <legend>Danh sách chức năng</legend>
                        <div class="row d-flex align-items-end">
                          
                            <div class="flex-end">

                                <div class="input-icon">
                                    <input type="text" value="" class="form-control" placeholder="Nội dung tìm kiếm ..."
                                        id="timKiemChucNang" autocomplete="off">
                                    <span class="input-icon-addon">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24"
                                            viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none"
                                            stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                            <path d="M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0"></path>
                                            <path d="M21 21l-6 -6"></path>
                                        </svg>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="row" style="margin-top:4px">
                            <div class="col-md-12">
                                <div id="Grid3" class="GridData"></div>
                            </div>
                        </div>
                    </fieldset>
                </div>
                <div class="modal-footer bg-whitesmoke br" style="justify-content: space-between;">
                    <label style="margin-bottom: unset; float: left;" class="form-check form-switch">

                    </label>
                    <div>
                        <a href="#" class="btn btn-sm btn-nts-outline-danger" data-bs-dismiss="modal"><i
                                class="fa fa-close"></i>&nbsp;Đóng</a>
                        <button href="#" id="btnChonDoiTuongVaDong_us" class="btn btn-sm btn-success btn-nts-luu">
                            <i class="ace-icon fa fa-floppy-o bigger-110"></i>&nbsp;Chọn và đóng
                        </button>
                    </div>

                </div>
            </div>
            </div>
        </div>

@endsection
    
    @push('scripts')
            <script>
            window.Laravel = {
                ...window.Laravel,
                exportExcelUrl: "{{ url('export/excel_v2') }}",
                exportPdfUrl: "{{ url('export/pdf_v2') }}",
                getAllUsers: "{{ route('hethong.nguoidung.getAll') }}",
                getPermissions: id => `{{ route('hethong.nguoidung.getPermiss', ['id' => '__ID__']) }}`.replace('__ID__',
                    id),
                getAllFunctions: "{{ route('hethong.nguoidung.getChucNang') }}",
                addFunctions: "{{ route('hethong.nguoidung.themChucNang') }}",
                updatePermission: "{{ route('hethong.nguoidung.luuThongTinQuyen') }}",
            doiMatKhau: "{{ route('hethong.nguoidung.doiMatKhau') }}",
                saveUser: "{{ route('hethong.nguoidung.luuThongTinUser') }}",
                deleteUser: id => `{{ route('hethong.nguoidung.xoaUser', ['id' => '__ID__']) }}`.replace('__ID__', id),
                deleteFunction: id => `{{ route('hethong.nguoidung.xoaChucNang', ['id' => '__ID__']) }}`.replace('__ID__',
                    id),
                loadEditData: id => `{{ route('hethong.nguoidung.loadDuLieuSua', ['id' => '__ID__']) }}`.replace('__ID__',
                    id),

                // DanhMuc lookups
                getAllDonVi: "{{ route('hethong.nguoidung.getAllDonVi') }}",
                getAllNhanVien: donviId => `{{ route('hethong.nguoidung.getAllNhanVien', ['donviId' => '__ID__']) }}`
                    .replace(
                         '__ID__', donviId),
                getAllPhongBan: "{{ route('hethong.nguoidung.getAllPhongBan') }}",
                getAllNhomNguoiDung: "{{ route('hethong.nguoidung.getAllNhomNguoiDung') }}",
                bulkDeleteUser: "{{ route('hethong.nguoidung.bulkDeleteUser') }}",
                bulkDangSD: "{{ route('dungchung.luu-dang-sd-nhieu') }}",
            }
            document.addEventListener('DOMContentLoaded', function() {
                document
                .querySelectorAll('[data-bs-toggle="tooltip"]')
                .forEach(el => {
                    new bootstrap.Tooltip(el, {
                        trigger: 'hover click'
                    });
                });
        });
        </script>
        <script src="{{ asset('js/hethong/NguoiDung.js') }}" defer></script>
    @endpush