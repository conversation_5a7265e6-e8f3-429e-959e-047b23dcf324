<?php
namespace App\Providers;

use Illuminate\Support\Facades\Gate;
use Illuminate\Support\ServiceProvider;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider;


class CustomAuthServiceProvider extends AuthServiceProvider
{
    public function boot()
    {
        $this->registerPolicies();

        // C<PERSON>ch sử dụng:
        //Gọi Route::middleware('can:quyen') khi đăng ký route
        // Route::middleware(['auth', 'can:xuatexcel'])
        //Hoặc authorize trong method controller
        //public function update(Request $req, XepLoai $xeploai)
        // {
        //     $this->authorize('xuatexcel');
        //     // logic xử lý thông thường…
        // }
        // Sử dụng trên view blade
        // @can('danhmuc.xeploai.create')
        //   <a href="{{ route('danhmuc.xeploai.create') }}">New Xếp Loại</a>
        // @endcan
        // @can('danhmuc.xeploai.delete')
        //   <button>Delete</button>
        // @endcan
        Gate::before(function ($user, $quyen) {
            return $user->hasPermOn($quyen)
                ? true
                : null;   // allow other policies to run or deny
        });

        Gate::define('download-ocr-training', function ($user) {
            // e.g. check a role, a flag, a group membership…
            return $user->hasRole('ocr‑trainer');
        });
    }

}