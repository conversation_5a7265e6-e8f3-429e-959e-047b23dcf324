<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use App\Http\Controllers\HeThong\NhomNguoiDungController;
use App\Models\DanhMuc\NhanVien;
use App\Models\HeThong\ChucNang;
use App\Models\HeThong\UserGroup;
use App\Models\HeThong\UserPermiss;
use Carbon\Carbon;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
// use Illuminate\Foundation\Auth\User as Authenticatable;
use MongoDB\Laravel\Eloquent\Model as Eloquent;
use Illuminate\Auth\Authenticatable;
use Illuminate\Auth\Passwords\CanResetPassword;
use Illuminate\Foundation\Auth\Access\Authorizable;
use Illuminate\Contracts\Auth\Authenticatable as AuthenticatableContract;
use Illuminate\Contracts\Auth\Access\Authorizable as AuthorizableContract;
use Illuminate\Contracts\Auth\CanResetPassword as CanResetPasswordContract;
use Illuminate\Notifications\Notifiable;
use App\Models\DanhMuc\DonVi;
use Illuminate\Support\Str;
use App\Http\Controllers\HeThong\Utilities\PermissionCryptV2;
class User extends Eloquent implements AuthenticatableContract, AuthorizableContract, CanResetPasswordContract
{
    use HasFactory, Notifiable;
    use Authenticatable, Authorizable, CanResetPassword;

    /**
     * The MongoDB connection and collection for this model.
     */
    protected $connection = 'mongodb';
    protected $collection = 'users';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        // 'name',
        'username',      // ← new
        'email',
        'password',
        'DonViID',       // ← new
        'NhomNguoiDungID',
        'NhanVienID',
        'ChucNangID_TrangChu',
        'TrangThai',
        'tenNhanVien',    // nvarchar(max)
        'ghiChu',         // nvarchar(max)
        'gioiTinhID',     // uniqueidentifier
        'ngaySinh',       // date
        'cmnd',           // nvarchar(20)
        'ngayCap',        // date
        'noiCap',         // nvarchar(250)
        'avatar',         // nvarchar(max)
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    // protected function casts(): array
    // {
    //     return [
    //         'email_verified_at' => 'datetime',
    //         'password' => 'hashed',
    //     ];
    // }

    /**
     * Attribute casting.
     *
     * @var array<string,string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'TrangThai' => 'boolean',  // cast status
        'NhanVienID' => 'string',
        'NhomNguoiDungID' => 'string', // ← cast to ObjectId
        'ChucNangID_TrangChu' => 'string',
        'DonViID' => 'string', // ← cast to ObjectId
    ];

    /**
     * Return tuNgay as dd/mm/yyyy
     */
    public function getCreatedAtAttribute($value)
    {
        return $value
            ? Carbon::parse($value)->format('d/m/Y')
            : null;
    }

    /**
     * Relation: a User belongs to a DonVi.
     */
    public function donVi()
    {
        return $this->belongsTo(DonVi::class, 'DonViID', '_id');
    }

    public function nhanVien()
    {
        return $this->belongsTo(NhanVien::class, 'NhanVienID', '_id');
    }
    /**
     * Relation: a User belongs to a DonVi.
     */
    public function nhomnguoidung()
    {
        return $this->belongsTo(UserGroup::class, 'NhomNguoiDungID', '_id');
    }

    public function logs()
    {
        return $this->hasMany(Logs::class, 'user_id', '_id');
    }


    public function hasChucNang(string $maChucNang): bool
    {
        return $this->chucNangs()
            ->where('MaChucNang', $maChucNang)
            ->exists();
    }

    /**
     * Get all the raw permission records for this user.
     */
    public function rawPermiss()
    {
        return $this->hasMany(
            UserPermiss::class,
            'UserID',
            '_id'
        );
    }

    /**
     * Check if the user has a given permission on a given ChucNangID.
     */
    public function hasPermOn(string $permissionKey): bool
    {
        $nhomnguoidungid = $this->NhomNguoiDungID;
        if ($nhomnguoidungid != null) {
            $groupCode = UserGroup::codeById($this->NhomNguoiDungID);

            if ($groupCode !== null && strcasecmp($groupCode, 'Admin') === 0) {
                return true;
            }
        }


        // 1) Normalize the incoming path (no leading/trailing slashes)
        $raw = trim(request()->path(), '/');
        // e.g. "danhmuc/xeploai/getall"

        // 2) Split into segments
        $segments = explode('/', $raw);
        $cn = null;

        // 3) Try progressively shorter paths
        while (count($segments) > 0) {
            // rebuild the path to this point
            $partial = implode('/', $segments);   // e.g. "danhmuc/xeploai"

            // generate all slash‐variants you want to match
            $variants = [
                $partial,
                '/' . $partial,
                $partial . '/',
                '/' . $partial . '/',
            ];

            // query either DuongDan or DuongDanTuyetDoi
            $cn = ChucNang::whereIn('DuongDan', $variants)
                ->orWhereIn('DuongDanTuyetDoi', $variants)
                ->first();
            if ($cn) {
                break;
            }

            // drop the last segment and try again
            array_pop($segments);
        }

        if (!$cn) {
            return true;
        }

        // 3) fetch the permission cipher
        $up = UserPermiss::where('UserID', (string) $this->getKey())
            ->where('ChucNangID', (string) $cn->_id)
            ->first();
        if (!$up) {
            return false;
        }

        // 4) decrypt & test the flag
        return PermissionCryptV2::has($up->Permission, $permissionKey);
    }

    /**
     * Check if the user has a given permission on a given ChucNangID.
     */
    public function hasRole(string $roleName): bool
    {
        $nhomnguoidungid = $this->NhomNguoiDungID;
        if ($nhomnguoidungid != null) {
            $groupCode = UserGroup::codeById($this->NhomNguoiDungID);

            if ($groupCode !== null && strcasecmp($groupCode, $roleName) === 0) {
                return true;
            }
        }
        return false;
    }
}