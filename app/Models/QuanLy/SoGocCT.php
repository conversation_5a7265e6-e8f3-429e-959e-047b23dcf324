<?php

namespace App\Models\QuanLy;
use MongoDB\Laravel\Eloquent\Model;

class SoGocCT extends Model
{
    protected $connection = 'mongodb';
    protected $collection = 'so_goc_c_t_s';

    protected $fillable = [
        'SoGocID',
        'DoiTuongID_HocSinh',
        'XepLoaiID',
        '<PERSON><PERSON><PERSON><PERSON>',
        'SoVaoSoGoc',
        'SoVaoSoGocBanSao',
        'SoHieuVanBang',
        '<PERSON><PERSON><PERSON><PERSON>',
        'NgayT<PERSON>Tac',
        'NgayCapNhat',
        'UserID_CapNhat',
        'UserID_ThaoTac',

'MaHocSinh',
'TenHocSinh',
'CCCD',
'NgaySinh',
'GioiTinhID',
'DanToc<PERSON>',
'NoiSinh',
'LopHocID',
'<PERSON>a<PERSON><PERSON>',
'DiaBanHCID_Tinh',
    ];

    protected $casts = [
        'NgayThaoTac' => 'datetime',
        'NgayCapNhat' => 'datetime',
    ];
}