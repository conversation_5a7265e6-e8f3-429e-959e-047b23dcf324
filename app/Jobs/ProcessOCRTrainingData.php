<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;
use Log;
use Illuminate\Support\Str;


class ProcessOCRTrainingData implements ShouldQueue
{
    use Dispatchable,                // ← add this
        InteractsWithQueue,
        Queueable,
        SerializesModels;

    protected $data;

    public function __construct(array $trainingData)
    {
        $this->data = $trainingData;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Storage::disk('local')->makeDirectory('ocr_training');


        foreach ($this->data as $i => $item) {
            $raw = preg_replace('#^data:image/\w+;base64,#i', '', $item['crop_image_base64']);

            $bin = base64_decode($raw);

            $time = now()->format('Ymd_His');
            $rand = substr(md5(uniqid()), 0, 8);
            $name = "{$time}_{$rand}";

            $imgPath = "ocr_training/{$name}.png";
            $labelPath = "ocr_training/{$name}.txt";

            Storage::disk('local')->put($imgPath, $bin);
            Storage::disk('local')->put($labelPath, $item['text']);
        }
    }
}