<?php

namespace App\Http\Controllers\DungChung;

use App\Http\Controllers\Controller;
use App\Jobs\ProcessOCRTrainingData;
use App\Models\DanhMuc\DonVi;
use App\Models\DanhMuc\GioiTinh;
use App\Models\DanhMuc\LopHoc;
use App\Models\QuanLy\DoiTuong;
use App\Models\QuanLy\NhatKyThaoTac;
use App\Models\QuanLy\TruongDuLieu;
use App\Services\DungChungDb;
use App\Services\DungChungNoDb;
use App\Services\ThongBao;
use DB;
use Exception;
use Gate;
use Illuminate\Http\Request;
use MongoDB\BSON\ObjectId; // from mongodb/mongodb
use GuzzleHttp\Client;
use Storage;

class QuanLyController extends Controller
{
    protected DungChungDb $dungChungDb;
    protected DungChungNoDb $logService;

    public function __construct(DungChungDb $dungChungDb, DungChungNoDb $logService)
    {
        $this->dungChungDb = $dungChungDb;
        $this->logService = $logService;
    }
    public function getDoiTuongByDonViTruongHoc($donviId_TruongHoc)
    {

        try {
            $list = DoiTuong::with(['danToc', 'donViHoc', 'diaBanTinh'])
                ->where('DonViID_Hoc', new ObjectId($donviId_TruongHoc))
                ->where('TrangThai', true)
                ->orderBy('MaDoiTuong') // or another field to order by
                ->get();

            return response()->json([
                'Err' => false,
                'Result' => $list,
            ]);
        } catch (Exception $e) {
            // Assuming $this->dungChungDb and ThongBao are accessible in this controller
            $code = $this->dungChungDb->insertMaCodeLoi($e);
            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }

    public function getDoiTuongByDonViTruongHoc_DemSoLuong(Request $request, $donviId_TruongHoc)
    {
        // 1) Extract and validate the BangCha/BangCon objects
        $bangCha = $request->input('BangCha', []);
        $bangCon = $request->input('BangCon', []);

        if (

            empty($bangCha['ID']) ||
            empty($bangCon['TenCollection']) ||
            empty($bangCon['TenCotReferenceCha']) ||
            empty($bangCon['TenCotReferenceDoiTuong'])
        ) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Missing BangCha or BangCon parameters',
            ], 400);
        }

        try {
            // // 2) Build ObjectIds and collection names
            $chaIdValue = new ObjectId($bangCha['ID']);

            $conCollection = $bangCon['TenCollection'];
            $conRefChaField = $bangCon['TenCotReferenceCha'];
            $conRefDoiTuongField = $bangCon['TenCotReferenceDoiTuong'];

            // 3) Find all DoiTuong IDs already referenced in BangCon for this BangCha
            $referencedIds = DB::connection('mongodb')
                ->table($conCollection)
                ->where($conRefChaField, $chaIdValue)
                ->pluck($conRefDoiTuongField)
                ->map(fn($id) => new ObjectId($id))
                ->toArray();

            // 4) Query DoiTuong, excluding those IDs
            $query = DoiTuong::with(['danToc', 'donViHoc', 'diaBanTinh', "gioiTinh_"])
                ->where('DonViID_Hoc', new ObjectId($donviId_TruongHoc))
                ->where('TrangThai', true);

            if (!empty($referencedIds)) {
                $query->whereNotIn('_id', $referencedIds);
            }


            $list = $query
                ->orderBy('MaDoiTuong')
                ->get();
            $list->each(function ($item) {
                $item->TenLopHoc = optional(
                    LopHoc::find($item->LopHocID)
                )->TenLopHoc ?? '';
            });

            return response()->json([
                'Err' => false,
                'Result' => $list,
            ]);

        } catch (Exception $e) {
            $code = $this->dungChungDb->insertMaCodeLoi($e);
            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }


    public function getDanhSachTruongHoc()
    {
        try {
            // Step 1: Get danh sách đơn vị
            $donViIds = DoiTuong::where('TrangThai', true)
                ->get(['DonViID_Hoc'])
                ->pluck('DonViID_Hoc')
                ->filter()
                ->values();

            if ($donViIds->isEmpty()) {
                return response()->json([
                    'Err' => false,
                    'Result' => [],
                ]);
            }

            // Step 2: Đếm số đối tượng

            $aggregationResult = DoiTuong::raw(function ($collection) use ($donViIds) {
                return $collection->aggregate([
                    [
                        '$match' => [
                            'TrangThai' => true,
                            'DonViID_Hoc' => ['$in' => $donViIds->toArray()],
                        ]
                    ],
                    [
                        '$group' => [
                            '_id' => '$DonViID_Hoc',
                            'total' => ['$sum' => 1],
                        ]
                    ]
                ]);
            });

            // Convert aggregation cursor to a collection for easy mapping
            $counts = collect(iterator_to_array($aggregationResult))
                ->mapWithKeys(function ($item) {
                    return [(string) $item->_id => $item->total];
                });

            // Step 3: Lấy danh sách cuối cùng
            $donViList = DonVi::whereIn('_id', $donViIds)
                ->where('TrangThai', true)
                ->orderBy('MaDonVi')
                ->get()
                ->map(function ($donVi) use ($counts) {
                    $idStr = (string) $donVi->_id;
                    return [
                        'id' => $idStr,
                        'code' => $donVi->MaDonVi,
                        'name' => $donVi->TenDonVi,
                        'count' => $counts->get($idStr, 0), // number of DoiTuong referencing this DonVi
                    ];
                });

            return response()->json([
                'Err' => false,
                'Result' => $donViList,
            ]);
        } catch (Exception $e) {
            $code = $this->dungChungDb->insertMaCodeLoi($e); // Adjust if needed
            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }

    public function getDanhSachTruongHoc_DemSoLuong(Request $request)
    {
        // 1) Extract BangCha / BangCon from JSON payload
        $bangCha = $request->input('BangCha', []);
        $bangCon = $request->input('BangCon', []);

        // simple validation
        if (
            empty($bangCha['ID']) ||
            empty($bangCon['TenCollection']) ||
            empty($bangCon['TenCotReferenceCha']) ||
            empty($bangCon['TenCotReferenceDoiTuong'])
        ) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Missing BangCha or BangCon parameters',
            ], 400);
        }

        try {

            $chaId = new ObjectId($bangCha['ID']);

            $conColl = $bangCon['TenCollection'];
            $conChaRef = $bangCon['TenCotReferenceCha'];
            $conObjRef = $bangCon['TenCotReferenceDoiTuong'];

            // 3) Find all DonViIDs that have at least one student
            $donViIds = DoiTuong::where('TrangThai', true)
                ->pluck('DonViID_Hoc')
                ->filter()
                ->map(fn($v) => new ObjectId($v))
                ->values();

            if ($donViIds->isEmpty()) {
                return response()->json(['Err' => false, 'Result' => []]);
            }

            // 4) Find which students have already been chosen in BangCon
            $chosenIds = DB::connection('mongodb')
                ->table($conColl)
                ->where($conChaRef, $chaId)
                ->pluck($conObjRef)
                ->map(fn($id) => new ObjectId($id))
                ->toArray();

            // 5) Aggregate: total count and chosen count per DonViID_Hoc
            $agg = DoiTuong::raw(function ($collection) use ($donViIds, $chosenIds) {
                return $collection->aggregate([
                    [
                        '$match' => [
                            'TrangThai' => true,
                            'DonViID_Hoc' => ['$in' => $donViIds->toArray()],
                        ]
                    ],
                    [
                        // mark each document whether its _id is in the chosen list
                        '$project' => [
                            'DonViID_Hoc' => 1,
                            'isChosen' => [
                                '$cond' => [
                                    ['$in' => ['$_id', $chosenIds]],
                                    1,
                                    0
                                ]
                            ],
                        ],
                    ],
                    [
                        // now group by school, summing both total and chosen flags
                        '$group' => [
                            '_id' => '$DonViID_Hoc',
                            'count' => ['$sum' => 1],
                            'daChonCount' => ['$sum' => '$isChosen'],
                        ],
                    ],
                ]);
            });
            // turn cursor into a lookup map
            $stats = collect(iterator_to_array($agg))
                ->mapWithKeys(fn($item) => [
                    (string) $item->_id => [
                        'count' => $item->count,
                        'daChonCount' => $item->daChonCount,
                    ]
                ]);

            // 6) Load DonVi and merge in our stats
            $donViList = DonVi::whereIn('_id', $donViIds)
                ->where('TrangThai', true)
                ->orderBy('MaDonVi')
                ->get()
                ->map(fn($dv) => [
                    'id' => (string) $dv->_id,
                    'code' => $dv->MaDonVi,
                    'name' => $dv->TenDonVi,
                    'count' => $stats->get((string) $dv->_id, [])['count'] ?? 0,
                    'daChonCount' => $stats->get((string) $dv->_id, [])['daChonCount'] ?? 0,
                ]);

            return response()->json([
                'Err' => false,
                'Result' => $donViList,
            ]);

        } catch (Exception $e) {
            $code = $this->dungChungDb->insertMaCodeLoi($e);
            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }

    public function bangDiemMainGridSvg()
    {
        // return an SVG document with the correct header
        return response(
            <<<'SVG'
    <svg xmlns="http://www.w3.org/2000/svg" width="155" height="120" viewBox="0 0 155 120" fill="none">
    <rect x="0.5" y="0.5" width="154" height="119" rx="3.5" fill="white" />
    <path d="M28.7699 102.72C55.1314 115.264 97.8686 115.267 124.23 102.72C150.592 90.1736 150.588 69.8375 124.23 57.2936C97.8717 44.7498 55.1314 44.7498 28.7699 57.2936C2.40845 69.8375 2.41159 90.1794 28.7699 102.72Z" fill="#F0F0F0"/>
    <path d="M129.826 78.1575C129.826 78.1575 130.157 76.6444 132.837 75.4751C135.445 74.3288 136.949 74.5953 138.739 73.939C140.53 73.2827 139.243 70.199 134.335 71.5689C129.839 72.8242 129.383 76.3692 129.383 76.3692L129.826 78.1575Z" fill="#BA68C8"/>
    <path opacity="0.2" d="M129.826 78.1575C129.826 78.1575 130.157 76.6444 132.837 75.4751C135.445 74.3288 136.949 74.5953 138.739 73.939C140.53 73.2827 139.243 70.199 134.335 71.5689C129.839 72.8242 129.383 76.3692 129.383 76.3692L129.826 78.1575Z" fill="black"/>
    <path d="M129.356 77.5898C129.338 77.5925 129.32 77.5925 129.302 77.5898C129.281 77.583 129.261 77.5724 129.244 77.5587C129.227 77.545 129.213 77.5283 129.203 77.5097C129.193 77.491 129.187 77.4708 129.186 77.4501C129.185 77.4294 129.188 77.4087 129.195 77.3892C129.824 75.7499 131.397 74.2367 133.449 73.3139C136.079 72.1418 137.951 72.5143 138.029 72.5315C138.074 72.5407 138.112 72.5654 138.136 72.6003C138.16 72.6353 138.168 72.6775 138.158 72.7178C138.148 72.7581 138.121 72.7931 138.083 72.8151C138.045 72.8372 137.998 72.8444 137.954 72.8353C137.935 72.8353 136.113 72.4742 133.6 73.5948C131.633 74.4717 130.107 75.9276 129.51 77.4923C129.497 77.5202 129.475 77.5441 129.448 77.5613C129.421 77.5785 129.389 77.5884 129.356 77.5898Z" fill="#FAFAFA"/>
    <path d="M129.441 76.2717C129.441 76.2717 129.755 75.412 130.967 73.7297C133.056 70.8123 136.463 69.537 137.954 67.869C139.559 66.0693 136.784 64.9573 134.289 65.5763C132.861 65.9374 131.558 67.1239 130.875 67.9235C129.429 69.6623 128.6 71.7621 128.5 73.9418L129.441 76.2717Z" fill="#BA68C8"/>
    <path d="M128.97 76.3402C128.924 76.3388 128.881 76.3211 128.85 76.2911C128.819 76.2611 128.802 76.2211 128.803 76.1798C128.949 74.2866 129.54 72.4443 130.536 70.7776C132.424 67.6911 135.224 66.5935 135.344 66.5476C135.383 66.5391 135.425 66.5437 135.462 66.5607C135.498 66.5776 135.527 66.6057 135.543 66.64C135.558 66.6744 135.56 66.7127 135.548 66.7483C135.536 66.7838 135.51 66.8143 135.476 66.8342C133.505 67.733 131.885 69.1616 130.835 70.9266C129.864 72.5489 129.288 74.3424 129.146 76.1855C129.145 76.2062 129.14 76.2266 129.131 76.2456C129.122 76.2646 129.109 76.2817 129.093 76.2961C129.076 76.3105 129.057 76.3218 129.036 76.3294C129.015 76.3369 128.992 76.3406 128.97 76.3402Z" fill="#FAFAFA"/>
    <path d="M37.4873 78.8509C35.2252 77.4667 35.1245 73.7411 35.4517 71.3997C35.9708 67.674 33.8787 66.8143 32.8121 68.4593C32.2962 69.2589 31.648 70.9956 31.7204 73.0189C31.8368 76.1885 33.3438 79.98 36.9933 81.3557L37.4873 78.8509Z" fill="#BA68C8"/>
    <path opacity="0.2" d="M37.4873 78.8509C35.2252 77.4667 35.1245 73.7411 35.4517 71.3997C35.9708 67.674 33.8787 66.8143 32.8121 68.4593C32.2962 69.2589 31.648 70.9956 31.7204 73.0189C31.8368 76.1885 33.3438 79.98 36.9933 81.3557L37.4873 78.8509Z" fill="black"/>
    <path d="M37.0224 81.1435C36.9854 81.1435 36.9497 81.1312 36.9218 81.1091C33.0929 78.1258 32.492 72.7982 33.5554 69.1041C33.5606 69.0852 33.5697 69.0675 33.5824 69.052C33.5951 69.0364 33.611 69.0232 33.6293 69.0132C33.6475 69.0033 33.6678 68.9967 33.6888 68.9938C33.7099 68.9909 33.7314 68.9919 33.752 68.9966C33.7727 69.0013 33.7921 69.0097 33.8092 69.0212C33.8263 69.0328 33.8408 69.0473 33.8517 69.0639C33.8627 69.0805 33.87 69.099 33.8731 69.1182C33.8762 69.1373 33.8752 69.1569 33.87 69.1757C32.8318 72.7838 33.4075 77.9882 37.1357 80.8913C37.1514 80.9034 37.1643 80.9183 37.1737 80.935C37.183 80.9517 37.1885 80.9699 37.19 80.9886C37.1915 81.0073 37.1888 81.0261 37.1822 81.0438C37.1756 81.0616 37.1651 81.0779 37.1514 81.092C37.1362 81.1095 37.1164 81.1233 37.0939 81.1323C37.0715 81.1413 37.0469 81.1451 37.0224 81.1435Z" fill="#FAFAFA"/>
    <path d="M40.8878 85.729C38.9183 87.0186 34.391 88.1334 32.9186 85.9239C31.55 83.869 32.4656 82.972 29.6875 83.3274C26.9095 83.6828 26.2708 81.7168 26.9284 80.0546C27.5859 78.3924 24.8488 75.9879 26.8466 74.1137C28.8443 72.2394 29.9927 76.1369 31.1505 76.5582C32.3082 76.9795 33.0161 76.071 34.3501 76.733C35.6841 77.3951 35.813 80.1033 37.1124 80.1549C39.2392 80.2437 42.8195 80.2294 43.0901 81.9575C43.3606 83.6856 40.8878 85.729 40.8878 85.729Z" fill="#BA68C8"/>
    <path d="M41.7019 84.9723H41.6861C31.3825 83.9922 27.3964 75.8016 27.151 74.6094C27.1472 74.5906 27.1476 74.5713 27.1521 74.5526C27.1566 74.5339 27.165 74.5162 27.177 74.5005C27.189 74.4848 27.2043 74.4714 27.2219 74.4611C27.2396 74.4507 27.2593 74.4437 27.28 74.4403C27.3006 74.4369 27.3218 74.4373 27.3423 74.4413C27.3628 74.4454 27.3822 74.4531 27.3995 74.464C27.4167 74.4749 27.4314 74.4888 27.4428 74.5049C27.4541 74.521 27.4619 74.539 27.4656 74.5578C27.7047 75.7299 31.609 83.7285 41.7239 84.6886C41.7446 84.6904 41.7647 84.6959 41.783 84.7048C41.8014 84.7137 41.8176 84.7259 41.8308 84.7405C41.8439 84.7552 41.8538 84.7721 41.8597 84.7902C41.8657 84.8083 41.8676 84.8274 41.8655 84.8462C41.86 84.8819 41.8403 84.9145 41.8102 84.9378C41.78 84.9611 41.7414 84.9734 41.7019 84.9723Z" fill="#FAFAFA"/>
    <path d="M30.4367 81.3038C29.7865 81.3038 29.1373 81.2558 28.4955 81.1605C28.4547 81.1535 28.4186 81.1322 28.395 81.101C28.3714 81.0699 28.3623 81.0315 28.3697 80.9943C28.3731 80.9757 28.3805 80.9579 28.3916 80.942C28.4027 80.9261 28.4172 80.9124 28.4342 80.9017C28.4513 80.891 28.4705 80.8836 28.4907 80.8798C28.511 80.876 28.5319 80.8759 28.5522 80.8797C29.7272 81.0559 30.926 81.0559 32.101 80.8797C32.1423 80.872 32.1852 80.8794 32.2205 80.9003C32.2558 80.9212 32.2807 80.954 32.2898 80.9914C32.2939 81.0099 32.294 81.0289 32.2901 81.0474C32.2861 81.0658 32.2783 81.0834 32.2669 81.0991C32.2555 81.1148 32.2408 81.1283 32.2237 81.1389C32.2065 81.1494 32.1873 81.1568 32.1671 81.1605C31.5972 81.2623 31.0174 81.3103 30.4367 81.3038Z" fill="#FAFAFA"/>
    <path d="M33.511 82.1611H33.4764C33.4361 82.1529 33.4009 82.1305 33.3786 82.0989C33.3562 82.0672 33.3484 82.0288 33.3568 81.992C33.6746 80.7159 33.7501 79.3996 33.5802 78.1002C33.5735 78.0622 33.5837 78.0233 33.6085 77.9921C33.6333 77.961 33.6706 77.94 33.7123 77.934C33.7541 77.9279 33.7967 77.9371 33.8309 77.9597C33.8652 77.9823 33.8881 78.0163 33.8948 78.0543C34.0771 79.3935 34.0005 80.7514 33.6683 82.0665C33.6568 82.0956 33.6353 82.1206 33.6069 82.1376C33.5786 82.1547 33.5449 82.1629 33.511 82.1611Z" fill="#FAFAFA"/>
    <path d="M14.2282 70.9466L19.1676 68.3501C19.4053 68.2413 19.6676 68.1846 19.9337 68.1846C20.1997 68.1846 20.4621 68.2413 20.6998 68.3501L21.329 68.6797C21.5526 68.8119 21.7387 68.9903 21.8722 69.2005C22.0056 69.4107 22.0825 69.6467 22.0967 69.8891V75.0821C22.082 75.3244 22.0049 75.5602 21.8715 75.7703C21.7381 75.9804 21.5523 76.159 21.329 76.2914L16.3927 78.8879C16.155 78.9968 15.8927 79.0535 15.6266 79.0535C15.3606 79.0535 15.0982 78.9968 14.8605 78.8879L14.2313 78.5583C14.0083 78.4259 13.8228 78.2473 13.6899 78.0371C13.557 77.8269 13.4806 77.5912 13.4668 77.349V72.156C13.481 71.9142 13.5573 71.6788 13.6895 71.4688C13.8217 71.2588 14.0062 71.0799 14.2282 70.9466Z" fill="#BA68C8"/>
    <path opacity="0.5" d="M14.2282 70.9466L19.1676 68.3501C19.4053 68.2413 19.6676 68.1846 19.9337 68.1846C20.1997 68.1846 20.4621 68.2413 20.6998 68.3501L21.329 68.6797C21.5526 68.8119 21.7387 68.9903 21.8722 69.2005C22.0056 69.4107 22.0825 69.6467 22.0967 69.8891V75.0821C22.082 75.3244 22.0049 75.5602 21.8715 75.7703C21.7381 75.9804 21.5523 76.159 21.329 76.2914L16.3927 78.8879C16.155 78.9968 15.8927 79.0535 15.6266 79.0535C15.3606 79.0535 15.0982 78.9968 14.8605 78.8879L14.2313 78.5583C14.0083 78.4259 13.8228 78.2473 13.6899 78.0371C13.557 77.8269 13.4806 77.5912 13.4668 77.349V72.156C13.481 71.9142 13.5573 71.6788 13.6895 71.4688C13.8217 71.2588 14.0062 71.0799 14.2282 70.9466Z" fill="#FAFAFA"/>
    <path d="M22.0907 69.8891C22.0907 69.4421 21.7478 69.2615 21.3231 69.485L16.3868 72.0815C16.1635 72.214 15.9777 72.3925 15.8443 72.6026C15.7109 72.8128 15.6338 73.0486 15.6191 73.2909V78.4838C15.6191 78.928 15.9621 79.1086 16.3868 78.8879L21.3231 76.2915C21.5464 76.159 21.7322 75.9804 21.8656 75.7703C21.999 75.5602 22.0761 75.3244 22.0907 75.0821V69.8891Z" fill="#BA68C8"/>
    <path d="M13.4668 77.349C13.4796 77.5922 13.5556 77.8291 13.6885 78.0404C13.8214 78.2516 14.0074 78.4311 14.2313 78.5641L14.8605 78.8937C15.0764 78.9924 15.3128 79.0483 15.5541 79.0577C15.7955 79.0671 16.0362 79.0297 16.2606 78.9481C15.8988 79.0742 15.6313 78.888 15.6313 78.4896V73.2909C15.6397 73.0476 15.7178 72.8105 15.8579 72.6031L13.6996 71.4568C13.5623 71.6651 13.4855 71.9018 13.4762 72.1446L13.4668 77.349Z" fill="#BA68C8"/>
    <path opacity="0.1" d="M13.4668 77.349C13.4796 77.5922 13.5556 77.8291 13.6885 78.0404C13.8214 78.2516 14.0074 78.4311 14.2313 78.5641L14.8605 78.8937C15.0764 78.9924 15.3128 79.0483 15.5541 79.0577C15.7955 79.0671 16.0362 79.0297 16.2606 78.9481C15.8988 79.0742 15.6313 78.888 15.6313 78.4896V73.2909C15.6397 73.0476 15.7178 72.8105 15.8579 72.6031L13.6996 71.4568C13.5623 71.6651 13.4855 71.9018 13.4762 72.1446L13.4668 77.349Z" fill="black"/>
    <path d="M16.7631 73.6462L20.9632 71.4309C21.0733 71.3707 21.1645 71.4309 21.1645 71.5369V74.1162C21.1609 74.1807 21.1408 74.2436 21.1059 74.2998C21.0709 74.356 21.0221 74.404 20.9632 74.44L16.7631 76.6725C16.6498 76.7327 16.5586 76.6725 16.5586 76.5636V73.9844C16.5601 73.9171 16.5795 73.8512 16.6151 73.7923C16.6507 73.7334 16.7015 73.6833 16.7631 73.6462Z" fill="#FAFAFA"/>
    <path d="M16.5586 73.7551L19.0661 74.4974L21.1645 71.322L19.1353 74.7639L16.5586 73.7551Z" fill="#E0E0E0"/>
    <path d="M14.2313 57.841L19.1707 55.2446C19.408 55.1344 19.6705 55.0769 19.9368 55.0769C20.2031 55.0769 20.4657 55.1344 20.7029 55.2446L21.3321 55.5741C21.5548 55.7067 21.7398 55.8854 21.8721 56.0956C22.0044 56.3058 22.0803 56.5415 22.0935 56.7835V61.9765C22.0794 62.2185 22.0024 62.4541 21.869 62.6638C21.7355 62.8736 21.5494 63.0515 21.3259 63.183L16.3896 65.7795C16.1523 65.8897 15.8898 65.9471 15.6235 65.9471C15.3572 65.9471 15.0946 65.8897 14.8574 65.7795L14.2282 65.4528C14.005 65.3186 13.8197 65.1384 13.6874 64.9268C13.5551 64.7151 13.4795 64.478 13.4668 64.2348V59.0504C13.4813 58.8084 13.5581 58.5728 13.6909 58.3628C13.8237 58.1527 14.0088 57.974 14.2313 57.841Z" fill="#BA68C8"/>
    <path opacity="0.5" d="M14.2313 57.841L19.1707 55.2446C19.408 55.1344 19.6705 55.0769 19.9368 55.0769C20.2031 55.0769 20.4657 55.1344 20.7029 55.2446L21.3321 55.5741C21.5548 55.7067 21.7398 55.8854 21.8721 56.0956C22.0044 56.3058 22.0803 56.5415 22.0935 56.7835V61.9765C22.0794 62.2185 22.0024 62.4541 21.869 62.6638C21.7355 62.8736 21.5494 63.0515 21.3259 63.183L16.3896 65.7795C16.1523 65.8897 15.8898 65.9471 15.6235 65.9471C15.3572 65.9471 15.0946 65.8897 14.8574 65.7795L14.2282 65.4528C14.005 65.3186 13.8197 65.1384 13.6874 64.9268C13.5551 64.7151 13.4795 64.478 13.4668 64.2348V59.0504C13.4813 58.8084 13.5581 58.5728 13.6909 58.3628C13.8237 58.1527 14.0088 57.974 14.2313 57.841Z" fill="#FAFAFA"/>
    <path d="M22.0907 56.7837C22.0907 56.3366 21.7478 56.156 21.3231 56.3796L16.3868 58.976C16.1632 59.1082 15.9771 59.2866 15.8436 59.4968C15.7102 59.707 15.6333 59.943 15.6191 60.1854V65.3812C15.6191 65.8254 15.9621 66.006 16.3868 65.7825L21.3231 63.186C21.5466 63.0545 21.7328 62.8766 21.8662 62.6668C21.9997 62.4571 22.0766 62.2215 22.0907 61.9795V56.7837Z" fill="#BA68C8"/>
    <path d="M13.4668 64.2347C13.4813 64.4767 13.5581 64.7123 13.6909 64.9223C13.8237 65.1324 14.0088 65.3111 14.2313 65.4441L14.8605 65.7708C15.0759 65.8707 15.3123 65.9275 15.5537 65.9373C15.7952 65.9472 16.0362 65.91 16.2606 65.8281C15.8988 65.9513 15.6313 65.7679 15.6313 65.3696V60.1852C15.6397 59.9419 15.7178 59.7048 15.8579 59.4974L13.6996 58.3511C13.5623 58.5594 13.4855 58.7961 13.4762 59.0389L13.4668 64.2347Z" fill="#BA68C8"/>
    <path opacity="0.1" d="M13.4668 64.2347C13.4813 64.4767 13.5581 64.7123 13.6909 64.9223C13.8237 65.1324 14.0088 65.3111 14.2313 65.4441L14.8605 65.7708C15.0759 65.8707 15.3123 65.9275 15.5537 65.9373C15.7952 65.9472 16.0362 65.91 16.2606 65.8281C15.8988 65.9513 15.6313 65.7679 15.6313 65.3696V60.1852C15.6397 59.9419 15.7178 59.7048 15.8579 59.4974L13.6996 58.3511C13.5623 58.5594 13.4855 58.7961 13.4762 59.0389L13.4668 64.2347Z" fill="black"/>
    <path d="M18.8074 63.0883L18.2977 63.0367C17.6055 62.9851 16.7812 62.7501 16.7812 61.8359C16.8012 61.504 16.9067 61.181 17.0893 60.8932C17.2719 60.6054 17.5263 60.3609 17.8321 60.1794C18.2851 59.9415 18.6689 60.039 18.8168 60.3829C18.9659 59.8728 19.3166 59.4309 19.8015 59.142C20.3836 58.8382 20.8524 59.0847 20.8524 59.6951C20.8524 60.5778 20.0344 61.7012 19.3454 62.4778L18.8074 63.0883Z" fill="#FAFAFA"/>
    <path d="M14.2282 44.7468L19.1676 42.1503C19.4048 42.0401 19.6674 41.9827 19.9337 41.9827C20.2 41.9827 20.4625 42.0401 20.6998 42.1503L21.329 42.4799C21.5525 42.6114 21.7387 42.7893 21.8721 42.9991C22.0056 43.2088 22.0825 43.4444 22.0967 43.6864V48.8794C22.0828 49.1219 22.0059 49.3579 21.8725 49.5681C21.739 49.7784 21.5528 49.9568 21.329 50.0888L16.3927 52.6852C16.1555 52.7954 15.8929 52.8529 15.6266 52.8529C15.3603 52.8529 15.0978 52.7954 14.8605 52.6852L14.2313 52.3585C14.0088 52.2256 13.8237 52.0468 13.6909 51.8368C13.5581 51.6267 13.4813 51.3912 13.4668 51.1491V45.9476C13.4824 45.7073 13.5594 45.4736 13.6916 45.2652C13.8237 45.0567 14.0075 44.8792 14.2282 44.7468Z" fill="#BA68C8"/>
    <path opacity="0.5" d="M14.2282 44.7468L19.1676 42.1503C19.4048 42.0401 19.6674 41.9827 19.9337 41.9827C20.2 41.9827 20.4625 42.0401 20.6998 42.1503L21.329 42.4799C21.5525 42.6114 21.7387 42.7893 21.8721 42.9991C22.0056 43.2088 22.0825 43.4444 22.0967 43.6864V48.8794C22.0828 49.1219 22.0059 49.3579 21.8725 49.5681C21.739 49.7784 21.5528 49.9568 21.329 50.0888L16.3927 52.6852C16.1555 52.7954 15.8929 52.8529 15.6266 52.8529C15.3603 52.8529 15.0978 52.7954 14.8605 52.6852L14.2313 52.3585C14.0088 52.2256 13.8237 52.0468 13.6909 51.8368C13.5581 51.6267 13.4813 51.3912 13.4668 51.1491V45.9476C13.4824 45.7073 13.5594 45.4736 13.6916 45.2652C13.8237 45.0567 14.0075 44.8792 14.2282 44.7468Z" fill="#FAFAFA"/>
    <path d="M16.3868 52.6766C15.9621 52.9002 15.6191 52.7196 15.6191 52.2754V47.0825C15.633 46.84 15.7099 46.6039 15.8433 46.3937C15.9767 46.1835 16.163 46.005 16.3868 45.8731L21.3231 43.2766C21.7478 43.0531 22.0907 43.2336 22.0907 43.6778V48.8708C22.0768 49.1133 22 49.3493 21.8666 49.5596C21.7331 49.7698 21.5469 49.9482 21.3231 50.0802" fill="#BA68C8"/>
    <path d="M13.4668 51.1405C13.4813 51.3825 13.5581 51.6181 13.6909 51.8281C13.8237 52.0382 14.0088 52.2169 14.2313 52.3499L14.8605 52.6766C15.0759 52.7765 15.3123 52.8333 15.5537 52.8432C15.7952 52.853 16.0362 52.8158 16.2606 52.7339C15.8988 52.8571 15.6313 52.6737 15.6313 52.2754V47.0824C15.6397 46.8391 15.7178 46.602 15.8579 46.3946L13.6996 45.2483C13.5623 45.4566 13.4855 45.6933 13.4762 45.9361L13.4668 51.1405Z" fill="#BA68C8"/>
    <path opacity="0.1" d="M13.4668 51.1405C13.4813 51.3825 13.5581 51.6181 13.6909 51.8281C13.8237 52.0382 14.0088 52.2169 14.2313 52.3499L14.8605 52.6766C15.0759 52.7765 15.3123 52.8333 15.5537 52.8432C15.7952 52.853 16.0362 52.8158 16.2606 52.7339C15.8988 52.8571 15.6313 52.6737 15.6313 52.2754V47.0824C15.6397 46.8391 15.7178 46.602 15.8579 46.3946L13.6996 45.2483C13.5623 45.4566 13.4855 45.6933 13.4762 45.9361L13.4668 51.1405Z" fill="black"/>
    <path d="M17.9531 47.8993C17.9531 47.8993 18.5289 46.6899 18.5824 46.5151C18.6109 46.2157 18.6109 45.9146 18.5824 45.6152C18.5824 45.5006 19.1078 45.1767 19.2776 45.5092C19.4411 45.8628 19.472 46.2556 19.3657 46.6269L20.6462 45.9477C20.9168 45.8044 21.0269 46.005 21.03 46.1655C21.0213 46.3485 20.9485 46.5242 20.8224 46.667C20.8224 46.667 21.0804 46.5982 21.0804 46.9106C21.0727 47.0204 21.0409 47.1278 20.9869 47.2262C20.9328 47.3247 20.8576 47.4123 20.7658 47.4838C20.7658 47.4838 20.8476 47.5411 20.8507 47.7703C20.8128 47.9951 20.6851 48.1992 20.4921 48.3435C20.5346 48.4339 20.5539 48.5319 20.5487 48.6301C20.5329 48.7673 20.4823 48.8993 20.4009 49.0157C20.3196 49.132 20.2097 49.2296 20.0799 49.3007L17.9814 50.4213L17.9531 47.8993Z" fill="#FAFAFA"/>
    <path d="M16.7403 51.4128L17.6622 50.9227L17.6496 47.9995L16.7246 48.4896L16.7403 51.4128Z" fill="#FAFAFA"/>
    <path d="M11.525 29.4032L19.0946 25.4225C19.4582 25.2544 19.8603 25.1667 20.2681 25.1667C20.6759 25.1667 21.078 25.2544 21.4416 25.4225L22.3854 25.9241C22.7281 26.1268 23.0133 26.4004 23.2178 26.7227C23.4223 27.0449 23.5403 27.4066 23.5621 27.7783V35.7396C23.5386 36.1094 23.4198 36.4689 23.2154 36.789C23.0109 37.1092 22.7266 37.3809 22.3854 37.5824L14.8347 41.5717C14.4705 41.7398 14.0679 41.8275 13.6596 41.8275C13.2513 41.8275 12.8487 41.7398 12.4845 41.5717L11.5407 41.0701C11.199 40.8667 10.9146 40.5929 10.7108 40.2707C10.5069 39.9486 10.3892 39.5872 10.3672 39.2159V31.2546C10.3874 30.8849 10.5027 30.5248 10.7038 30.2032C10.9049 29.8816 11.1862 29.6076 11.525 29.4032Z" fill="#BA68C8"/>
    <path opacity="0.1" d="M11.525 29.4032L19.0946 25.4225C19.4582 25.2544 19.8603 25.1667 20.2681 25.1667C20.6759 25.1667 21.078 25.2544 21.4416 25.4225L22.3854 25.9241C22.7281 26.1268 23.0133 26.4004 23.2178 26.7227C23.4223 27.0449 23.5403 27.4066 23.5621 27.7783V35.7396C23.5386 36.1094 23.4198 36.4689 23.2154 36.789C23.0109 37.1092 22.7266 37.3809 22.3854 37.5824L14.8347 41.5717C14.4705 41.7398 14.0679 41.8275 13.6596 41.8275C13.2513 41.8275 12.8487 41.7398 12.4845 41.5717L11.5407 41.0701C11.199 40.8667 10.9146 40.5929 10.7108 40.2707C10.5069 39.9486 10.3892 39.5872 10.3672 39.2159V31.2546C10.3874 30.8849 10.5027 30.5248 10.7038 30.2032C10.9049 29.8816 11.1862 29.6076 11.525 29.4032Z" fill="black"/>
    <path d="M23.5765 27.7295C23.5765 27.3311 23.3625 26.3395 22.2393 26.9672C21.1162 27.5948 15.5979 30.4349 14.8365 30.8475C14.3082 31.1116 13.891 31.5279 13.6504 32.0311L14.2513 32.5814L23.5765 27.7295Z" fill="#BA68C8"/>
    <path opacity="0.2" d="M23.5765 27.7295C23.5765 27.3311 23.3625 26.3395 22.2393 26.9672C21.1162 27.5948 15.5979 30.4349 14.8365 30.8475C14.3082 31.1116 13.891 31.5279 13.6504 32.0311L14.2513 32.5814L23.5765 27.7295Z" fill="black"/>
    <path d="M14.8349 41.5716C14.1836 41.9126 13.6582 41.6375 13.6582 40.9554V32.9969C13.6796 32.6248 13.7973 32.2625 14.0018 31.9397C14.2063 31.617 14.4918 31.3429 14.8349 31.1399L22.3856 27.1592C23.0368 26.8182 23.5622 27.0961 23.5622 27.7782V35.7396C23.5387 36.1093 23.4199 36.4688 23.2155 36.7889C23.0111 37.1091 22.7268 37.3809 22.3856 37.5823" fill="#BA68C8"/>
    <path opacity="0.1" d="M14.8349 41.5716C14.1836 41.9126 13.6582 41.6375 13.6582 40.9554V32.9969C13.6796 32.6248 13.7973 32.2625 14.0018 31.9397C14.2063 31.617 14.4918 31.3429 14.8349 31.1399L22.3856 27.1592C23.0368 26.8182 23.5622 27.0961 23.5622 27.7782V35.7396C23.5387 36.1093 23.4199 36.4688 23.2155 36.7889C23.0111 37.1091 22.7268 37.3809 22.3856 37.5823" fill="black"/>
    <path d="M10.3524 39.2158C10.3744 39.5871 10.4921 39.9484 10.696 40.2706C10.8999 40.5927 11.1842 40.8666 11.5259 41.07L12.4698 41.5715C12.8009 41.7235 13.1636 41.8097 13.5341 41.8245C13.9045 41.8394 14.2741 41.7824 14.6186 41.6575C14.0649 41.8495 13.6433 41.5658 13.6433 40.9553V32.9968C13.6576 32.6241 13.7768 32.261 13.9894 31.9422L10.6828 30.2026C10.471 30.5216 10.3529 30.8847 10.3398 31.2573L10.3524 39.2158Z" fill="#BA68C8"/>
    <path opacity="0.2" d="M10.3524 39.2158C10.3744 39.5871 10.4921 39.9484 10.696 40.2706C10.8999 40.5927 11.1842 40.8666 11.5259 41.07L12.4698 41.5715C12.8009 41.7235 13.1636 41.8097 13.5341 41.8245C13.9045 41.8394 14.2741 41.7824 14.6186 41.6575C14.0649 41.8495 13.6433 41.5658 13.6433 40.9553V32.9968C13.6576 32.6241 13.7768 32.261 13.9894 31.9422L10.6828 30.2026C10.471 30.5216 10.3529 30.8847 10.3398 31.2573L10.3524 39.2158Z" fill="black"/>
    <path d="M18.8442 30.2112L19.8257 31.7875L22.028 30.9678C22.2357 30.8904 22.3206 31.114 22.1696 31.349L20.5808 33.8136L20.9552 35.9172C20.9898 36.1178 20.7727 36.3958 20.5871 36.3929L18.6113 36.3384L16.645 38.4592C16.4594 38.6598 16.2423 38.6082 16.2769 38.3703L16.6576 35.8627L15.0845 35.0689C14.9335 34.9943 15.0185 34.682 15.2261 34.5415L17.4284 33.0513L18.41 30.4405C18.4823 30.2026 18.7498 30.0622 18.8442 30.2112Z" fill="#FAFAFA"/>
    <path d="M125.264 66.5789C121.489 68.2153 115.385 68.2153 111.616 66.5789C107.847 64.9425 107.841 62.2801 111.616 60.6551C115.392 59.0302 121.495 59.0187 125.264 60.6551C129.033 62.2915 129.021 64.9425 125.264 66.5789Z" fill="#E0E0E0"/>
    <path d="M39.4395 85.5768C39.4731 85.9936 39.6139 86.3973 39.8506 86.7552C40.0872 87.1132 40.4131 87.4154 40.8017 87.6374L78.9738 107.698C79.396 107.894 79.8627 107.995 80.3361 107.995C80.8095 107.995 81.2762 107.894 81.6984 107.698L133.924 80.2291C134.336 80.0312 134.68 79.7348 134.921 79.3717C135.161 79.0086 135.288 78.5926 135.288 78.1686C135.288 77.7446 135.161 77.3286 134.921 76.9655C134.68 76.6024 134.336 76.306 133.924 76.108L95.7553 56.047C95.3332 55.8519 94.8664 55.7502 94.393 55.7502C93.9197 55.7502 93.4529 55.8519 93.0308 56.047L40.8049 83.5163C40.4156 83.7379 40.0892 84.04 39.852 84.398C39.6147 84.756 39.4734 85.1598 39.4395 85.5768Z" fill="#37474F"/>
    <path d="M55.1887 75.9504L40.8014 83.5163C40.3898 83.7142 40.0452 84.0107 39.8049 84.3738C39.5645 84.7369 39.4375 85.1529 39.4375 85.5769C39.4375 86.0009 39.5645 86.4168 39.8049 86.7799C40.0452 87.143 40.3898 87.4395 40.8014 87.6374L78.9735 107.698C79.3933 107.898 79.8623 107.997 80.3358 107.985V75.9504H55.1887Z" fill="#263238"/>
    <path d="M38.1601 83.5592C37.7803 83.3251 37.4667 83.0125 37.2447 82.6469C37.0227 82.2813 36.8987 81.8732 36.8828 81.4557V39.9609C36.9079 39.5302 37.0444 39.111 37.2811 38.7375C37.5179 38.3639 37.8482 38.0467 38.2451 37.8115L90.471 10.3566C90.8652 10.1313 91.3194 10.0082 91.7846 10.0004C92.2499 9.99259 92.7087 10.1004 93.1118 10.3123C93.5148 10.5241 93.8469 10.832 94.0723 11.2029C94.2977 11.5737 94.4079 11.9935 94.3911 12.4171V53.8976C94.3657 54.3282 94.2292 54.7474 93.9925 55.1208C93.7557 55.4943 93.4256 55.8116 93.0288 56.047L40.8029 83.5162C40.3972 83.7124 39.9464 83.8186 39.4872 83.8261C39.0279 83.8336 38.5732 83.7421 38.1601 83.5592Z" fill="#455A64"/>
    <path d="M43.2741 41.881L37.2965 38.7285C37.0523 39.098 36.9158 39.5183 36.9 39.9494V81.4556C36.8836 81.8788 36.9939 82.2981 37.2192 82.6685C37.4444 83.0389 37.7761 83.3465 38.1787 83.5582C38.5812 83.77 39.0394 83.878 39.5042 83.8705C39.969 83.8631 40.4228 83.7406 40.817 83.5162L46.7726 80.3838L43.2741 41.881Z" fill="#263238"/>
    <path d="M39.4395 41.308V82.7999C39.4395 83.5909 40.0498 83.9118 40.8017 83.5164L93.0276 56.0471C93.4244 55.8118 93.7546 55.4945 93.9913 55.121C94.228 54.7475 94.3646 54.3284 94.3899 53.8977V12.4058C94.3899 11.6148 93.7795 11.2939 93.0276 11.6893L40.8017 39.1557C40.4051 39.3918 40.0751 39.7097 39.8384 40.0836C39.6017 40.4576 39.465 40.877 39.4395 41.308Z" fill="#37474F"/>
    <path d="M40.8018 84.9492L78.9739 105.025C79.3961 105.22 79.8628 105.321 80.3362 105.321C80.8096 105.321 81.2763 105.22 81.6985 105.025L133.924 77.5553C134.679 77.1598 134.679 76.5179 133.924 76.1224L95.7554 56.047C95.3333 55.8519 94.8665 55.7502 94.3932 55.7502C93.9198 55.7502 93.453 55.8519 93.0309 56.047L40.805 83.5163C40.0499 83.9118 40.0499 84.5537 40.8018 84.9492Z" fill="#455A64"/>
    <path d="M41.5348 41.4599L92.2977 14.7644C92.7476 14.5265 93.1157 14.7186 93.1157 15.1943V51.1121C93.0999 51.3704 93.0176 51.6217 92.8755 51.8457C92.7334 52.0697 92.5355 52.2602 92.2977 52.4017L41.5348 79.0972C41.0818 79.3351 40.7168 79.143 40.7168 78.6673V42.7409C40.7334 42.4839 40.816 42.2341 40.9581 42.0116C41.1002 41.789 41.2978 41.6001 41.5348 41.4599Z" fill="#FAFAFA"/>
    <path d="M45.2296 83.8743L93.7085 58.3682C93.9196 58.2707 94.1529 58.22 94.3896 58.22C94.6263 58.22 94.8596 58.2707 95.0707 58.3682L97.553 59.6722C97.6352 59.6976 97.7066 59.7458 97.7571 59.81C97.8077 59.8743 97.8349 59.9513 97.8349 60.0304C97.8349 60.1094 97.8077 60.1865 97.7571 60.2508C97.7066 60.315 97.6352 60.3632 97.553 60.3886L49.0648 85.8919C48.8536 85.9893 48.6203 86.0401 48.3836 86.0401C48.1469 86.0401 47.9136 85.9893 47.7025 85.8919L45.2296 84.5822C45.1507 84.5551 45.0826 84.5067 45.0346 84.4434C44.9866 84.3802 44.9609 84.3051 44.9609 84.2283C44.9609 84.1514 44.9866 84.0763 45.0346 84.0131C45.0826 83.9499 45.1507 83.9015 45.2296 83.8743Z" fill="#263238"/>
    <path d="M51.6214 87.2362L100.1 61.73C100.311 61.6326 100.545 61.5818 100.781 61.5818C101.018 61.5818 101.251 61.6326 101.463 61.73L116.715 69.7544C116.797 69.7798 116.869 69.828 116.919 69.8923C116.97 69.9565 116.997 70.0336 116.997 70.1126C116.997 70.1917 116.97 70.2688 116.919 70.333C116.869 70.3973 116.797 70.4455 116.715 70.4709L68.2928 95.9369C68.0811 96.0344 67.8473 96.0851 67.6101 96.0851C67.3729 96.0851 67.139 96.0344 66.9274 95.9369L51.6246 87.9469C51.543 87.9219 51.4721 87.8743 51.4217 87.8108C51.3714 87.7472 51.3441 87.6709 51.3438 87.5925C51.3434 87.5142 51.37 87.4377 51.4198 87.3738C51.4696 87.3098 51.5401 87.2617 51.6214 87.2362Z" fill="#263238"/>
    <path d="M95.0774 73.2105L96.597 72.4109C96.6599 72.3765 96.6568 72.3192 96.597 72.2848L94.8414 71.362C94.804 71.3444 94.7625 71.3352 94.7203 71.3352C94.6782 71.3352 94.6367 71.3444 94.5992 71.362L93.0828 72.1616C93.0167 72.1931 93.0198 72.2504 93.0828 72.2877L94.8383 73.2105C94.8754 73.2276 94.9163 73.2365 94.9579 73.2365C94.9994 73.2365 95.0404 73.2276 95.0774 73.2105ZM92.0823 70.038L90.5658 70.8261C90.4998 70.8605 90.5029 70.9178 90.5658 70.9551L92.3214 71.8779C92.3591 71.8948 92.4005 71.9036 92.4425 71.9036C92.4845 71.9036 92.526 71.8948 92.5636 71.8779L94.0832 71.0783C94.1462 71.0439 94.143 70.9866 94.0832 70.9522L92.3277 70.0265C92.2886 70.0109 92.2461 70.0038 92.2035 70.0058C92.1609 70.0078 92.1194 70.0188 92.0823 70.038ZM87.1775 72.6173L84.6102 73.9785C84.5441 74.0129 84.5504 74.0703 84.6102 74.1075L86.3657 75.0303C86.4034 75.0473 86.4449 75.0561 86.4869 75.0561C86.5289 75.0561 86.5703 75.0473 86.608 75.0303L89.1753 73.6805C89.2382 73.6461 89.235 73.5888 89.1753 73.5544L87.4197 72.6287C87.383 72.61 87.3421 72.5992 87.3001 72.5972C87.258 72.5952 87.2161 72.6021 87.1775 72.6173ZM89.8643 71.2044L88.3447 72.004C88.2817 72.0355 88.2849 72.0928 88.3447 72.1301L90.1002 73.0529C90.1377 73.0705 90.1792 73.0797 90.2213 73.0797C90.2635 73.0797 90.305 73.0705 90.3425 73.0529L91.8589 72.2533C91.925 72.2189 91.9218 72.1616 91.8589 72.1272L90.1034 71.2044C90.0663 71.1873 90.0253 71.1784 89.9838 71.1784C89.9423 71.1784 89.9013 71.1873 89.8643 71.2044ZM83.9086 74.3368L82.3922 75.1249C82.3261 75.1593 82.3324 75.2166 82.3922 75.2539L84.1477 76.1767C84.1852 76.1943 84.2267 76.2034 84.2689 76.2034C84.311 76.2034 84.3525 76.1943 84.39 76.1767L85.9096 75.38C85.9725 75.3456 85.9693 75.2882 85.9096 75.2539L84.154 74.3282C84.1152 74.3121 84.0728 74.3045 84.0302 74.306C83.9876 74.3075 83.946 74.318 83.9086 74.3368ZM81.6906 75.5032L80.1741 76.2999C80.1081 76.3343 80.1112 76.3916 80.1741 76.4289L81.9297 77.3517C81.9674 77.3686 82.0088 77.3774 82.0508 77.3774C82.0928 77.3774 82.1343 77.3686 82.1719 77.3517L83.6915 76.5521C83.7544 76.5177 83.7513 76.4604 83.6915 76.426L81.936 75.5003C81.8977 75.4834 81.8555 75.4748 81.8129 75.4753C81.7703 75.4758 81.7284 75.4853 81.6906 75.5032ZM88.5964 79.2575L90.1128 78.4579C90.1757 78.4235 90.1726 78.3662 90.1128 78.3318L88.3478 77.4176C88.3104 77.4 88.2688 77.3908 88.2267 77.3908C88.1845 77.3908 88.143 77.4 88.1056 77.4176L86.5891 78.2172C86.5231 78.2487 86.5294 78.306 86.5891 78.3433L88.3447 79.2661C88.3841 79.2838 88.4278 79.2923 88.4717 79.2908C88.5157 79.2893 88.5585 79.2779 88.5964 79.2575ZM86.3752 80.4239L87.8948 79.6243C87.9577 79.5899 87.9545 79.5354 87.8948 79.4982L86.1298 78.5639C86.0923 78.5463 86.0508 78.5372 86.0087 78.5372C85.9665 78.5372 85.925 78.5463 85.8875 78.5639L84.3648 79.3692C84.2987 79.4008 84.305 79.4581 84.3648 79.4953L86.1203 80.4181C86.1588 80.4386 86.2024 80.4498 86.247 80.4508C86.2915 80.4518 86.3356 80.4426 86.3752 80.4239ZM84.1572 81.5903L85.6768 80.7907C85.7397 80.7592 85.7365 80.7018 85.6768 80.6646L83.9212 79.7418C83.8837 79.7242 83.8422 79.715 83.8001 79.715C83.7579 79.715 83.7164 79.7242 83.6789 79.7418L82.1625 80.5356C82.0964 80.57 82.0996 80.6273 82.1625 80.6617L83.918 81.5845C83.9546 81.6026 83.9953 81.6125 84.0369 81.6135C84.0784 81.6144 84.1196 81.6065 84.1572 81.5903ZM81.9549 82.7567L83.4745 81.9571C83.5374 81.9256 83.5342 81.8683 83.4745 81.831L81.7189 80.9082C81.6814 80.8906 81.6399 80.8814 81.5978 80.8814C81.5556 80.8814 81.5141 80.8906 81.4767 80.9082L79.935 81.7164C79.869 81.7508 79.8721 81.8081 79.935 81.8425L81.6906 82.7653C81.7296 82.7825 81.7727 82.7908 81.816 82.7893C81.8594 82.7878 81.9017 82.7766 81.9391 82.7567H81.9549ZM92.8594 74.3769L94.3758 73.5773C94.4419 73.5429 94.4387 73.4885 94.3758 73.4512L92.6203 72.5284C92.5828 72.5108 92.5413 72.5017 92.4991 72.5017C92.457 72.5017 92.4155 72.5108 92.378 72.5284L90.8616 73.328C90.7955 73.3624 90.7987 73.4168 90.8616 73.4541L92.6171 74.3769C92.6546 74.3945 92.6961 74.4037 92.7383 74.4037C92.7804 74.4037 92.8219 74.3945 92.8594 74.3769ZM90.8144 78.1025L94.1996 76.32C94.2626 76.2856 94.2594 76.2283 94.1996 76.1939L89.9366 73.9528C89.8992 73.9352 89.8576 73.926 89.8155 73.926C89.7734 73.926 89.7318 73.9352 89.6944 73.9528L87.9451 74.8727C87.879 74.9071 87.8822 74.9644 87.9451 75.0017L90.204 76.1881C90.2733 76.2254 90.2764 76.2827 90.204 76.3142L88.804 77.0536C88.7411 77.0851 88.7442 77.1425 88.804 77.1797L90.5596 78.1025C90.5999 78.1196 90.6441 78.1275 90.6885 78.1255C90.7329 78.1235 90.7761 78.1117 90.8144 78.0911V78.1025ZM79.4726 76.6696L77.9561 77.4692C77.8901 77.5007 77.8932 77.558 77.9561 77.5953L79.7117 78.5181C79.7493 78.535 79.7908 78.5438 79.8328 78.5438C79.8748 78.5438 79.9163 78.535 79.9539 78.5181L81.4735 77.7185C81.5364 77.6841 81.5333 77.6268 81.4735 77.5924L79.718 76.6696C79.6801 76.6515 79.638 76.6421 79.5953 76.6421C79.5525 76.6421 79.5104 76.6515 79.4726 76.6696ZM77.2703 77.836L75.7507 78.6356C75.6878 78.67 75.6909 78.7244 75.7507 78.7617L77.5062 79.6845C77.5437 79.7021 77.5852 79.7112 77.6274 79.7112C77.6695 79.7112 77.711 79.7021 77.7485 79.6845L79.2649 78.8849C79.331 78.8505 79.3278 78.7932 79.2649 78.7588L77.5094 77.836C77.4703 77.8164 77.4265 77.8062 77.3819 77.8062C77.3374 77.8062 77.2936 77.8164 77.2545 77.836H77.2703ZM66.1833 90.9186C66.2207 90.9362 66.2623 90.9454 66.3044 90.9454C66.3465 90.9454 66.3881 90.9362 66.4255 90.9186L67.9451 90.1191C68.008 90.0847 68.0049 90.0274 67.9451 89.993L66.1896 89.0702C66.1521 89.0526 66.1106 89.0434 66.0684 89.0434C66.0263 89.0434 65.9848 89.0526 65.9473 89.0702L64.4309 89.8697C64.3648 89.9041 64.3679 89.9586 64.4309 89.9958L66.1833 90.9186ZM59.5103 87.1615L57.9907 87.9611C57.9278 87.9926 57.9309 88.0499 57.9907 88.0872L59.7463 89.01C59.7839 89.0269 59.8254 89.0357 59.8674 89.0357C59.9094 89.0357 59.9509 89.0269 59.9885 89.01L61.505 88.2104C61.571 88.176 61.5679 88.1187 61.505 88.0843L59.7494 87.1615C59.7124 87.1444 59.6714 87.1355 59.6299 87.1355C59.5883 87.1355 59.5474 87.1444 59.5103 87.1615ZM63.0812 89.8984C63.1186 89.916 63.1602 89.9252 63.2023 89.9252C63.2444 89.9252 63.286 89.916 63.3234 89.8984L64.8399 89.0988C64.9059 89.0644 64.8996 89.01 64.8399 88.9727L63.0875 88.0499C63.05 88.0323 63.0085 88.0231 62.9663 88.0231C62.9242 88.0231 62.8827 88.0323 62.8452 88.0499L61.3256 88.8495C61.2627 88.8839 61.2659 88.9383 61.3256 88.9756L63.0812 89.8984ZM70.0876 91.6179L71.6072 90.8183C71.6701 90.7839 71.667 90.7266 71.6072 90.6922L69.8517 89.7666C69.814 89.7496 69.7726 89.7408 69.7305 89.7408C69.6885 89.7408 69.6471 89.7496 69.6094 89.7666L68.093 90.5661C68.0269 90.5977 68.0301 90.655 68.093 90.6922L69.8485 91.615C69.8839 91.634 69.9237 91.6451 69.9646 91.6476C70.0056 91.6501 70.0466 91.6438 70.0845 91.6294L70.0876 91.6179ZM67.6273 92.7786C67.6648 92.7962 67.7063 92.8054 67.7485 92.8054C67.7906 92.8054 67.8321 92.7962 67.8696 92.7786L69.3892 91.979C69.4521 91.9446 69.449 91.8873 69.3892 91.8529L67.6336 90.9301C67.5962 90.9125 67.5547 90.9033 67.5125 90.9033C67.4704 90.9033 67.4288 90.9125 67.3914 90.9301L65.8624 91.7469C65.7963 91.7784 65.7994 91.8357 65.8624 91.873L67.6273 92.7786ZM61.7126 86.0152L60.193 86.8119C60.1301 86.8463 60.1332 86.9036 60.193 86.9408L61.9486 87.8636C61.9862 87.8806 62.0277 87.8894 62.0697 87.8894C62.1117 87.8894 62.1531 87.8806 62.1908 87.8636L63.7073 87.0641C63.7733 87.0297 63.7702 86.9724 63.7073 86.938L61.9674 86.0152C61.9282 85.9963 61.8844 85.9865 61.84 85.9865C61.7956 85.9865 61.7519 85.9963 61.7126 86.0152ZM72.8185 80.1688L71.2989 80.9684C71.236 81.0028 71.2391 81.0601 71.2989 81.0945L73.0544 82.0173C73.0919 82.0349 73.1334 82.0441 73.1756 82.0441C73.2177 82.0441 73.2592 82.0349 73.2967 82.0173L74.8131 81.2177C74.8792 81.1862 74.8729 81.1289 74.8131 81.0916L73.0607 80.1688C73.0231 80.1519 72.9816 80.1431 72.9396 80.1431C72.8976 80.1431 72.8561 80.1519 72.8185 80.1688ZM63.9432 84.8373L62.4268 85.634C62.3607 85.6684 62.3639 85.7257 62.4268 85.763L64.1823 86.6858C64.22 86.7027 64.2614 86.7115 64.3035 86.7115C64.3455 86.7115 64.3869 86.7027 64.4246 86.6858L65.9442 85.8891C66.0071 85.8547 66.0039 85.7973 65.9442 85.7601L64.1886 84.8373C64.1505 84.8199 64.1085 84.8108 64.0659 84.8108C64.0233 84.8108 63.9813 84.8199 63.9432 84.8373ZM75.0365 79.0024L73.5169 79.802C73.454 79.8364 73.4571 79.8908 73.5169 79.9281L75.2725 80.8509C75.3099 80.8685 75.3514 80.8777 75.3936 80.8777C75.4357 80.8777 75.4773 80.8685 75.5147 80.8509L77.0312 80.0513C77.0972 80.0169 77.0941 79.9625 77.0312 79.9252L75.2756 79.0024C75.2386 78.9853 75.1976 78.9764 75.1561 78.9764C75.1145 78.9764 75.0736 78.9853 75.0365 79.0024ZM70.5973 81.3352L69.0809 82.1348C69.0148 82.1692 69.0211 82.2265 69.0809 82.2609L70.8364 83.1866C70.8741 83.2035 70.9155 83.2123 70.9575 83.2123C70.9995 83.2123 71.041 83.2035 71.0787 83.1866L72.5951 82.3898C72.6612 82.3555 72.6549 82.2981 72.5951 82.2609L70.8364 81.3409C70.7996 81.3236 70.759 81.314 70.7176 81.313C70.6762 81.312 70.635 81.3196 70.5973 81.3352ZM68.3793 82.5045L66.8628 83.3012C66.7968 83.3356 66.8031 83.3929 66.8628 83.4273L68.6341 84.353C68.6712 84.3699 68.7122 84.3787 68.7537 84.3787C68.7952 84.3787 68.8361 84.3699 68.8732 84.353L70.3928 83.5563C70.4557 83.5219 70.4526 83.4645 70.3928 83.4273L68.6341 82.5073C68.5953 82.4873 68.5517 82.4766 68.5071 82.4761C68.4625 82.4756 68.4186 82.4853 68.3793 82.5045ZM66.1612 83.6709L64.6448 84.4676C64.5787 84.502 64.585 84.5593 64.6448 84.5965L66.4004 85.5194C66.438 85.5363 66.4795 85.5451 66.5215 85.5451C66.5635 85.5451 66.6049 85.5363 66.6426 85.5194L68.1622 84.7226C68.2251 84.6883 68.222 84.6309 68.1622 84.5937L66.4066 83.6709C66.3686 83.6535 66.3265 83.6444 66.2839 83.6444C66.2413 83.6444 66.1993 83.6535 66.1612 83.6709ZM77.5031 85.0895L79.0164 84.2956C79.0824 84.2612 79.0793 84.2039 79.0164 84.1667L77.2608 83.2439C77.2234 83.2263 77.1818 83.2171 77.1397 83.2171C77.0976 83.2171 77.056 83.2263 77.0186 83.2439L75.499 84.0406C75.4361 84.075 75.4392 84.1323 75.499 84.1695L77.2545 85.0923C77.2933 85.1098 77.336 85.1186 77.3792 85.1181C77.4224 85.1176 77.4648 85.1078 77.5031 85.0895ZM96.9871 67.4587L95.4675 68.2468C95.4046 68.2784 95.4078 68.3357 95.4675 68.3729L97.2231 69.2957C97.2607 69.3127 97.3022 69.3215 97.3442 69.3215C97.3862 69.3215 97.4277 69.3127 97.4653 69.2957L98.9818 68.4962C99.0478 68.4618 99.0447 68.4045 98.9818 68.3701L97.2262 67.4473C97.1882 67.4319 97.1467 67.4249 97.1052 67.4269C97.0636 67.4289 97.0232 67.4398 96.9871 67.4587ZM94.3003 68.8716L92.7839 69.6798C92.7178 69.7142 92.721 69.7715 92.7839 69.8087L94.5363 70.7315C94.5737 70.7491 94.6153 70.7583 94.6574 70.7583C94.6995 70.7583 94.7411 70.7491 94.7785 70.7315L96.2981 69.9348C96.361 69.9004 96.3579 69.8431 96.2981 69.8087L94.5426 68.8831C94.5059 68.8643 94.4649 68.8535 94.4229 68.8515C94.3809 68.8496 94.3389 68.8564 94.3003 68.8716ZM99.2051 66.2923L97.6856 67.1005C97.6226 67.1349 97.6258 67.1922 97.6856 67.2295L99.4411 68.1523C99.4788 68.1692 99.5202 68.178 99.5622 68.178C99.6042 68.178 99.6457 68.1692 99.6834 68.1523L101.2 67.3527C101.266 67.3183 101.263 67.261 101.2 67.2266L99.4442 66.3009C99.4077 66.2831 99.3672 66.273 99.3258 66.2716C99.2844 66.2701 99.2431 66.2772 99.2051 66.2923ZM87.7217 77.0794L89.2413 76.2712C89.3042 76.2368 89.3011 76.1795 89.2413 76.1451L87.4858 75.2223C87.4483 75.2047 87.4068 75.1956 87.3646 75.1956C87.3225 75.1956 87.281 75.2047 87.2435 75.2223L85.7239 76.019C85.661 76.0534 85.6642 76.1107 85.7239 76.148L87.4795 77.0708C87.5164 77.0891 87.5575 77.0994 87.5995 77.1009C87.6415 77.1024 87.6833 77.095 87.7217 77.0794ZM101.669 65.1288C101.631 65.1118 101.589 65.103 101.547 65.103C101.505 65.103 101.464 65.1118 101.426 65.1288L99.9099 65.9255C99.8438 65.9599 99.847 66.0172 99.9099 66.0545L101.665 66.9773C101.703 66.9949 101.744 67.004 101.787 67.004C101.829 67.004 101.87 66.9949 101.908 66.9773L103.427 66.1806C103.49 66.1462 103.487 66.0888 103.427 66.0516L101.669 65.1288ZM79.7211 83.9231L81.2375 83.1264C81.3036 83.092 81.3005 83.0347 81.2375 82.9974L79.482 82.0746C79.4443 82.0577 79.4029 82.0489 79.3609 82.0489C79.3189 82.0489 79.2774 82.0577 79.2397 82.0746L77.7139 82.8627C77.6509 82.8971 77.6541 82.9544 77.7139 82.9888L79.4694 83.9145C79.5075 83.9342 79.5503 83.9452 79.5941 83.9467C79.6379 83.9482 79.6815 83.9401 79.7211 83.9231ZM106.926 69.6167L108.445 68.8171C108.508 68.7827 108.505 68.7254 108.445 68.691L106.69 67.7682C106.652 67.7506 106.611 67.7415 106.569 67.7415C106.527 67.7415 106.485 67.7506 106.448 67.7682L104.928 68.5678C104.865 68.5993 104.868 68.6566 104.928 68.6939L106.684 69.6167C106.721 69.6343 106.763 69.6435 106.805 69.6435C106.847 69.6435 106.888 69.6343 106.926 69.6167ZM102.207 69.4648L103.723 68.6652C103.789 68.6309 103.786 68.5735 103.723 68.5391L101.967 67.6163C101.93 67.5987 101.888 67.5896 101.846 67.5896C101.804 67.5896 101.763 67.5987 101.725 67.6163L100.206 68.4159C100.143 68.4474 100.146 68.5048 100.206 68.542L101.961 69.4648C101.998 69.4819 102.039 69.4909 102.081 69.4909C102.122 69.4909 102.163 69.4819 102.2 69.4648H102.207ZM104.185 66.4471C104.148 66.4301 104.106 66.4213 104.064 66.4213C104.022 66.4213 103.981 66.4301 103.943 66.4471L102.427 67.2466C102.361 67.2782 102.364 67.3355 102.427 67.3727L104.182 68.2956C104.22 68.3125 104.261 68.3213 104.303 68.3213C104.345 68.3213 104.387 68.3125 104.425 68.2956L105.944 67.496C106.007 67.4616 106.004 67.4043 105.944 67.3699L104.185 66.4471ZM104.651 66.2035L108.905 68.4446C108.942 68.4615 108.984 68.4703 109.026 68.4703C109.068 68.4703 109.109 68.4615 109.147 68.4446L110.667 67.645C110.729 67.6106 110.726 67.5533 110.667 67.5189L106.388 65.2807C106.35 65.2637 106.309 65.2549 106.267 65.2549C106.225 65.2549 106.183 65.2637 106.146 65.2807L104.629 66.0774C104.566 66.1089 104.569 66.1662 104.639 66.2035H104.651ZM99.5072 68.7827L97.9876 69.5823C97.9247 69.6138 97.9278 69.6712 97.9876 69.7084L99.7431 70.6312C99.7806 70.6488 99.8221 70.658 99.8643 70.658C99.9064 70.658 99.9479 70.6488 99.9854 70.6312L101.502 69.8317C101.568 69.7973 101.565 69.7399 101.502 69.7056L99.7463 68.7827C99.7082 68.7628 99.6652 68.7518 99.6212 68.7508C99.5773 68.7498 99.5338 68.7588 99.4946 68.777L99.5072 68.7827ZM82.7917 77.558L81.2722 78.3576C81.2092 78.392 81.2124 78.4464 81.2722 78.4837L83.0277 79.4065C83.0651 79.4241 83.1067 79.4333 83.1488 79.4333C83.191 79.4333 83.2325 79.4241 83.2699 79.4065L84.7864 78.6069C84.8525 78.5725 84.8493 78.5181 84.7864 78.4808L83.0308 77.558C82.9938 77.5409 82.9528 77.532 82.9113 77.532C82.8697 77.532 82.8288 77.5409 82.7917 77.558ZM104.459 70.7774C104.497 70.795 104.538 70.8042 104.58 70.8042C104.622 70.8042 104.664 70.795 104.701 70.7774L106.218 69.9778C106.284 69.9434 106.281 69.889 106.218 69.8517L104.462 68.9289C104.425 68.9113 104.383 68.9021 104.341 68.9021C104.299 68.9021 104.258 68.9113 104.22 68.9289L102.701 69.7285C102.638 69.7629 102.641 69.8173 102.701 69.8546L104.459 70.7774ZM85.0098 76.3916L83.4902 77.1912C83.4273 77.2227 83.4304 77.28 83.4902 77.3173L85.2457 78.2401C85.2832 78.2577 85.3247 78.2668 85.3668 78.2668C85.409 78.2668 85.4505 78.2577 85.488 78.2401L87.0044 77.4405C87.0705 77.4061 87.0673 77.3488 87.0044 77.3144L85.2489 76.3916C85.2126 76.3754 85.1728 76.367 85.1325 76.367C85.0921 76.367 85.0523 76.3754 85.0161 76.3916H85.0098ZM68.3856 89.7408C68.4232 89.7577 68.4647 89.7665 68.5067 89.7665C68.5487 89.7665 68.5901 89.7577 68.6278 89.7408L70.1474 88.9412C70.2103 88.9068 70.2072 88.8495 70.1474 88.8151L68.3919 87.8923C68.3544 87.8747 68.3129 87.8655 68.2707 87.8655C68.2286 87.8655 68.1871 87.8747 68.1496 87.8923L66.6332 88.6919C66.5671 88.7234 66.5702 88.7807 66.6332 88.818L68.3856 89.7408ZM65.0507 86.8749L63.5311 87.6745C63.4682 87.706 63.4713 87.7633 63.5311 87.8006L65.2866 88.7234C65.3243 88.7403 65.3657 88.7491 65.4077 88.7491C65.4498 88.7491 65.4912 88.7403 65.5289 88.7234L67.0453 87.9238C67.1114 87.8894 67.1082 87.8321 67.0453 87.7977L65.2898 86.8749C65.2512 86.8604 65.2095 86.8544 65.1679 86.8574C65.1264 86.8604 65.0862 86.8723 65.0507 86.8921V86.8749ZM70.8364 88.5944L72.356 87.7948C72.4189 87.7605 72.4158 87.7031 72.356 87.6688L70.6004 86.7459C70.563 86.7283 70.5215 86.7192 70.4793 86.7192C70.4372 86.7192 70.3956 86.7283 70.3582 86.7459L68.8418 87.5427C68.7757 87.577 68.782 87.6344 68.8418 87.6716L70.5973 88.5944C70.6344 88.6113 70.6754 88.6201 70.7169 88.6201C70.7584 88.6201 70.7993 88.6113 70.8364 88.5944ZM73.0576 87.428L74.574 86.6285C74.6401 86.5941 74.6338 86.5367 74.574 86.4995L72.8185 85.5767C72.7808 85.5597 72.7394 85.5509 72.6973 85.5509C72.6553 85.5509 72.6139 85.5597 72.5762 85.5767L71.0598 86.3734C70.9969 86.4078 71 86.4651 71.0598 86.5024L72.8153 87.4252C72.8542 87.4435 72.8974 87.4531 72.9412 87.4531C72.985 87.4531 73.0281 87.4435 73.067 87.4252L73.0576 87.428ZM75.2599 86.2588L76.7763 85.462C76.8424 85.4277 76.8361 85.3703 76.7763 85.3331L75.0239 84.4103C74.9862 84.3933 74.9448 84.3845 74.9028 84.3845C74.8608 84.3845 74.8193 84.3933 74.7817 84.4103L73.2621 85.207C73.1992 85.2414 73.2023 85.2987 73.2621 85.3359L75.0176 86.2588C75.0587 86.2792 75.1049 86.2896 75.1517 86.2891C75.1986 86.2886 75.2445 86.2772 75.285 86.2559L75.2599 86.2588ZM67.2687 85.7286L65.7491 86.5253C65.6862 86.5597 65.6893 86.617 65.7491 86.6542L67.5046 87.577C67.5423 87.594 67.5838 87.6028 67.6258 87.6028C67.6678 87.6028 67.7092 87.594 67.7469 87.577L69.2633 86.7775C69.3294 86.7431 69.3263 86.6858 69.2633 86.6514L67.5109 85.7286C67.4733 85.7116 67.4318 85.7028 67.3898 85.7028C67.3478 85.7028 67.3064 85.7116 67.2687 85.7286ZM69.4867 84.5622L67.9671 85.3589C67.9042 85.3933 67.9074 85.4506 67.9671 85.4878L69.7227 86.4106C69.7601 86.4282 69.8017 86.4374 69.8438 86.4374C69.8859 86.4374 69.9275 86.4282 69.9649 86.4106L71.4845 85.6139C71.5474 85.5795 71.5443 85.5222 71.4845 85.485L69.729 84.5622C69.6915 84.5448 69.6502 84.5355 69.6082 84.535C69.5662 84.5345 69.5246 84.5428 69.4867 84.5593V84.5622ZM78.362 79.8937L76.8424 80.6933C76.7763 80.7276 76.7826 80.785 76.8424 80.8194L78.5979 81.7422C78.6354 81.7598 78.6769 81.7689 78.7191 81.7689C78.7612 81.7689 78.8027 81.7598 78.8402 81.7422L80.3566 80.9426C80.4227 80.9111 80.4164 80.8537 80.3566 80.8165L78.6011 79.8937C78.5642 79.8763 78.5234 79.867 78.4819 79.8665C78.4404 79.866 78.3993 79.8744 78.362 79.8908V79.8937ZM76.1408 81.063L74.6118 81.8568C74.5457 81.8912 74.552 81.9485 74.6118 81.9829L76.3642 82.9086C76.4018 82.9255 76.4433 82.9343 76.4853 82.9343C76.5273 82.9343 76.5688 82.9255 76.6064 82.9086L78.126 82.1119C78.1889 82.0775 78.1858 82.0201 78.126 81.9829L76.3705 81.0601C76.3347 81.0443 76.2954 81.0362 76.2556 81.0362C76.2159 81.0362 76.1766 81.0443 76.1408 81.0601V81.063ZM80.5894 78.7244L79.0699 79.524C79.0069 79.5584 79.0101 79.6157 79.0699 79.6501L80.8254 80.5729C80.8629 80.5905 80.9044 80.5997 80.9465 80.5997C80.9887 80.5997 81.0302 80.5905 81.0676 80.5729L82.5841 79.7733C82.6502 79.7418 82.6439 79.6845 82.5841 79.6472L80.8317 78.7244C80.7942 78.7068 80.7527 78.6976 80.7106 78.6976C80.6684 78.6976 80.6269 78.7068 80.5894 78.7244ZM71.7142 83.3929L70.1977 84.1896C70.1317 84.224 70.1348 84.2813 70.1977 84.3186L71.9533 85.2414C71.991 85.2583 72.0324 85.2671 72.0744 85.2671C72.1164 85.2671 72.1579 85.2583 72.1955 85.2414L73.7151 84.4447C73.778 84.4103 73.7749 84.353 73.7151 84.3157L71.9596 83.3929C71.9203 83.3741 71.8766 83.3642 71.8322 83.3642C71.7878 83.3642 71.744 83.3741 71.7047 83.3929H71.7142ZM73.9322 82.2265L72.4095 83.0232C72.3434 83.0576 72.3466 83.1149 72.4095 83.1522L74.165 84.075C74.2027 84.0919 74.2441 84.1007 74.2861 84.1007C74.3282 84.1007 74.3696 84.0919 74.4073 84.075L75.9269 83.2783C75.9898 83.2439 75.9866 83.1866 75.9269 83.1493L74.1713 82.2265C74.1331 82.2079 74.0904 82.1982 74.047 82.1982C74.0037 82.1982 73.961 82.2079 73.9228 82.2265H73.9322ZM72.3119 90.463L73.8315 89.6634C73.8945 89.629 73.8913 89.5717 73.8315 89.5373L72.076 88.6116C72.0383 88.5947 71.9969 88.5859 71.9549 88.5859C71.9128 88.5859 71.8714 88.5947 71.8337 88.6116L70.3173 89.4083C70.2512 89.4427 70.2575 89.5 70.3173 89.5373L72.0697 90.4601C72.1059 90.4761 72.1455 90.4847 72.1857 90.4852C72.226 90.4857 72.2659 90.4781 72.3025 90.463H72.3119ZM59.2964 89.265L57.5408 88.3422C57.5034 88.3246 57.4618 88.3155 57.4197 88.3155C57.3776 88.3155 57.336 88.3246 57.2986 88.3422L55.779 89.1418C55.7161 89.1733 55.7192 89.2306 55.779 89.2679L57.5345 90.1907C57.572 90.2083 57.6135 90.2175 57.6557 90.2175C57.6978 90.2175 57.7393 90.2083 57.7768 90.1907L59.2932 89.3911C59.3593 89.3567 59.353 89.2994 59.2869 89.265H59.2964ZM62.6187 90.1535L60.8632 89.2306C60.8257 89.2131 60.7842 89.2039 60.742 89.2039C60.6999 89.2039 60.6584 89.2131 60.6209 89.2306L58.2865 90.4601C58.2235 90.4945 58.2267 90.5489 58.2865 90.5862L60.042 91.509C60.0795 91.5266 60.121 91.5358 60.1631 91.5358C60.2053 91.5358 60.2468 91.5266 60.2843 91.509L62.6187 90.2796C62.6848 90.2452 62.6785 90.1907 62.6093 90.1535H62.6187ZM56.6033 87.4796L54.8477 86.5568C54.8103 86.5392 54.7687 86.53 54.7266 86.53C54.6845 86.53 54.6429 86.5392 54.6055 86.5568L52.7367 87.5398C52.6706 87.5742 52.6769 87.6315 52.7367 87.6688L54.4922 88.5916C54.5299 88.6085 54.5713 88.6173 54.6133 88.6173C54.6554 88.6173 54.6968 88.6085 54.7345 88.5916L56.6001 87.6086C56.6788 87.5742 56.6788 87.5169 56.5938 87.4796H56.6033ZM58.0348 86.7259C58.0722 86.7435 58.1138 86.7527 58.1559 86.7527C58.198 86.7527 58.2396 86.7435 58.277 86.7259L59.7935 85.9263C59.8595 85.8919 59.8564 85.8375 59.7935 85.8002L58.0379 84.8774C58.0005 84.8598 57.9589 84.8506 57.9168 84.8506C57.8746 84.8506 57.8331 84.8598 57.7957 84.8774L56.2761 85.677C56.2132 85.7114 56.2163 85.7658 56.2761 85.8031L58.0348 86.7259ZM65.7019 91.1737L63.9464 90.2509C63.9089 90.2333 63.8674 90.2241 63.8252 90.2241C63.7831 90.2241 63.7416 90.2333 63.7041 90.2509L60.7845 91.787C60.7216 91.8214 60.7247 91.8787 60.7845 91.9131L62.54 92.8359C62.5775 92.8535 62.619 92.8627 62.6612 92.8627C62.7033 92.8627 62.7448 92.8535 62.7823 92.8359L65.7019 91.2998C65.7743 91.2568 65.7711 91.2024 65.7019 91.1737ZM67.1586 93.0451L65.403 92.1223C65.3656 92.1047 65.324 92.0955 65.2819 92.0955C65.2398 92.0955 65.1982 92.1047 65.1608 92.1223L63.2857 93.091C63.2228 93.1253 63.2259 93.1827 63.2857 93.2171L65.0412 94.1427C65.0789 94.1597 65.1203 94.1685 65.1623 94.1685C65.2044 94.1685 65.2458 94.1597 65.2835 94.1427L67.1523 93.1597C67.2309 93.1282 67.2278 93.0738 67.1586 93.0365V93.0451ZM74.5205 78.0595C74.5574 78.0771 74.5985 78.0863 74.6401 78.0863C74.6817 78.0863 74.7227 78.0771 74.7596 78.0595L76.2792 77.26C76.3422 77.2284 76.339 77.1711 76.2792 77.1339L74.5237 76.2111C74.4862 76.1935 74.4447 76.1843 74.4026 76.1843C74.3604 76.1843 74.3189 76.1935 74.2814 76.2111L72.765 77.0106C72.6989 77.045 72.7052 77.1023 72.765 77.1367L74.5205 78.0595ZM70.0813 80.3952C70.119 80.4121 70.1605 80.4209 70.2025 80.4209C70.2445 80.4209 70.2859 80.4121 70.3236 80.3952L71.8432 79.5956C71.9061 79.5641 71.9029 79.5068 71.8432 79.4695L70.0876 78.5467C70.05 78.5298 70.0085 78.521 69.9665 78.521C69.9245 78.521 69.883 78.5298 69.8454 78.5467L68.3195 79.3348C68.2534 79.3692 68.2566 79.4265 68.3195 79.4609L70.0813 80.3952ZM60.2434 85.5681C60.281 85.585 60.3225 85.5938 60.3645 85.5938C60.4065 85.5938 60.4479 85.585 60.4856 85.5681L62.0021 84.7685C62.0681 84.7341 62.065 84.6768 62.0021 84.6424L60.2402 83.7225C60.2028 83.7049 60.1612 83.6957 60.1191 83.6957C60.0769 83.6957 60.0354 83.7049 59.998 83.7225L58.4784 84.522C58.4155 84.5536 58.4186 84.6109 58.4784 84.6481L60.2434 85.5681ZM72.2994 79.2259C72.3368 79.2435 72.3783 79.2527 72.4205 79.2527C72.4626 79.2527 72.5042 79.2435 72.5416 79.2259L74.0612 78.4264C74.1241 78.3948 74.121 78.3375 74.0612 78.3003L72.3057 77.3775C72.2682 77.3599 72.2267 77.3507 72.1845 77.3507C72.1424 77.3507 72.1009 77.3599 72.0634 77.3775L70.547 78.177C70.4809 78.2114 70.4872 78.2687 70.547 78.3031L72.2994 79.2259ZM67.8633 81.5616C67.901 81.5785 67.9424 81.5874 67.9844 81.5874C68.0264 81.5874 68.0679 81.5785 68.1056 81.5616L69.6251 80.7649C69.6881 80.7305 69.6849 80.6732 69.6251 80.6359L67.8602 79.7103C67.8227 79.6927 67.7812 79.6835 67.739 79.6835C67.6969 79.6835 67.6554 79.6927 67.6179 79.7103L66.1172 80.5012C66.0511 80.5356 66.0543 80.5929 66.1172 80.6302L67.8633 81.5616ZM68.3761 93.1798C68.3387 93.1622 68.2971 93.153 68.255 93.153C68.2129 93.153 68.1713 93.1622 68.1339 93.1798L65.8026 94.4093C65.7397 94.4436 65.7428 94.501 65.8026 94.5382L67.5644 95.4725C67.6021 95.4894 67.6435 95.4982 67.6856 95.4982C67.7276 95.4982 67.769 95.4894 67.8067 95.4725L70.1411 94.2459C70.204 94.2115 70.2009 94.1542 70.1411 94.1169L68.3761 93.1798ZM64.6794 83.2209C64.7169 83.2385 64.7584 83.2477 64.8005 83.2477C64.8427 83.2477 64.8842 83.2385 64.9217 83.2209L66.4318 82.4328C66.4947 82.3984 66.4916 82.3411 66.4318 82.3039L64.6763 81.3811C64.6386 81.3641 64.5972 81.3553 64.5551 81.3553C64.5131 81.3553 64.4717 81.3641 64.434 81.3811L62.9176 82.1778C62.8515 82.2122 62.8547 82.2695 62.9176 82.3067L64.6794 83.2209ZM62.4614 84.3873C62.4991 84.4043 62.5405 84.4131 62.5825 84.4131C62.6245 84.4131 62.666 84.4043 62.7036 84.3873L64.2232 83.5878C64.2861 83.5534 64.283 83.4961 64.2232 83.4617L62.4677 82.5389C62.4302 82.5213 62.3887 82.5121 62.3466 82.5121C62.3044 82.5121 62.2629 82.5213 62.2254 82.5389L60.7058 83.3356C60.6429 83.37 60.6461 83.4273 60.7058 83.4645L62.4614 84.3873ZM109.477 71.5712C109.44 71.5543 109.398 71.5455 109.356 71.5455C109.314 71.5455 109.273 71.5543 109.235 71.5712L105.497 73.5372C105.435 73.5716 105.438 73.6289 105.497 73.6633L107.253 74.589C107.291 74.6059 107.332 74.6147 107.374 74.6147C107.416 74.6147 107.458 74.6059 107.495 74.589L111.23 72.623C111.296 72.5915 111.293 72.5342 111.23 72.4969L109.477 71.5712ZM104.572 74.1505C104.535 74.1329 104.493 74.1237 104.451 74.1237C104.409 74.1237 104.368 74.1329 104.33 74.1505L102.814 74.9501C102.748 74.9845 102.751 75.0418 102.814 75.0762L104.569 75.999C104.607 76.0166 104.648 76.0258 104.69 76.0258C104.733 76.0258 104.774 76.0166 104.812 75.999L106.331 75.1994C106.394 75.1679 106.391 75.1106 106.331 75.0733L104.572 74.1505ZM74.5237 89.2937L76.0401 88.497C76.103 88.4626 76.0999 88.4053 76.0401 88.368L74.2751 87.4481C74.2375 87.4311 74.196 87.4223 74.154 87.4223C74.112 87.4223 74.0706 87.4311 74.0329 87.4481L72.5164 88.2448C72.4504 88.2792 72.4567 88.3365 72.5164 88.3738L74.272 89.2966C74.3109 89.3152 74.3543 89.3247 74.3982 89.3242C74.4422 89.3237 74.4853 89.3132 74.5237 89.2937ZM100.136 76.4833C100.099 76.4657 100.057 76.4565 100.015 76.4565C99.9731 76.4565 99.9316 76.4657 99.8941 76.4833L98.3746 77.28C98.3116 77.3144 98.3148 77.3717 98.3746 77.409L100.13 78.3318C100.168 78.3487 100.209 78.3575 100.251 78.3575C100.293 78.3575 100.335 78.3487 100.372 78.3318L101.889 77.5351C101.955 77.5007 101.952 77.4434 101.889 77.4061L100.136 76.4833ZM111.695 70.4048C111.658 70.3872 111.616 70.3781 111.574 70.3781C111.532 70.3781 111.491 70.3872 111.453 70.4048L109.933 71.2044C109.871 71.2388 109.874 71.2961 109.933 71.3305L111.689 72.2533C111.726 72.2709 111.768 72.2801 111.81 72.2801C111.852 72.2801 111.894 72.2709 111.931 72.2533L113.448 71.4537C113.514 71.4222 113.511 71.3649 113.448 71.3276L111.695 70.4048ZM115.669 70.1612L111.422 67.9201C111.384 67.9025 111.343 67.8933 111.3 67.8933C111.258 67.8933 111.217 67.9025 111.179 67.9201L109.663 68.7197C109.597 68.7512 109.6 68.8085 109.663 68.8458L113.926 71.0869C113.963 71.1045 114.005 71.1137 114.047 71.1137C114.089 71.1137 114.131 71.1045 114.168 71.0869L115.688 70.2873C115.741 70.2529 115.738 70.1985 115.669 70.1612ZM91.8463 80.8566C91.8087 80.8397 91.7672 80.8309 91.7252 80.8309C91.6832 80.8309 91.6417 80.8397 91.6041 80.8566L89.7353 81.8396C89.6723 81.8711 89.6755 81.9284 89.7353 81.9657L91.4908 82.8885C91.5283 82.9061 91.5698 82.9153 91.6119 82.9153C91.6541 82.9153 91.6956 82.9061 91.7331 82.8885L93.5987 81.9055C93.6648 81.8711 93.6616 81.8138 93.5987 81.7765L91.8463 80.8566ZM97.4496 77.9105C97.4119 77.8936 97.3705 77.8848 97.3285 77.8848C97.2865 77.8848 97.245 77.8936 97.2073 77.9105L94.8729 79.14C94.81 79.1744 94.8131 79.2317 94.8729 79.2661L96.6347 80.1688C96.6722 80.1864 96.7137 80.1956 96.7559 80.1956C96.798 80.1956 96.8395 80.1864 96.877 80.1688L99.2114 78.9393C99.2744 78.9078 99.2712 78.8505 99.2114 78.8132L97.4496 77.9105ZM86.7087 83.5591C86.6712 83.5415 86.6297 83.5323 86.5876 83.5323C86.5454 83.5323 86.5039 83.5415 86.4664 83.5591L73.9825 90.1105C73.9165 90.1449 73.9228 90.2022 73.9825 90.2394L75.7381 91.1622C75.7758 91.1792 75.8172 91.188 75.8592 91.188C75.9012 91.188 75.9427 91.1792 75.9803 91.1622L88.4705 84.5937C88.5366 84.5593 88.5334 84.502 88.4705 84.4647L86.7087 83.5591ZM73.5138 90.4974C73.4761 90.4804 73.4347 90.4716 73.3926 90.4716C73.3506 90.4716 73.3092 90.4804 73.2715 90.4974L71.4027 91.4603C71.3398 91.4947 71.3429 91.5491 71.4027 91.5864L73.1583 92.5092C73.1957 92.5268 73.2372 92.536 73.2794 92.536C73.3215 92.536 73.3631 92.5268 73.4005 92.5092L75.2693 91.5262C75.3322 91.4918 75.3291 91.4345 75.2693 91.4001L73.5138 90.4974ZM89.2759 82.2093C89.2385 82.1917 89.1969 82.1825 89.1548 82.1825C89.1127 82.1825 89.0711 82.1917 89.0337 82.2093L87.168 83.1923C87.1019 83.2267 87.1051 83.284 87.168 83.3212L88.9236 84.2441C88.9612 84.261 89.0027 84.2698 89.0447 84.2698C89.0867 84.2698 89.1281 84.261 89.1658 84.2441L91.0346 83.2611C91.0975 83.2267 91.0944 83.1722 91.0346 83.135L89.2759 82.2093ZM70.9465 91.85C70.9091 91.8324 70.8675 91.8233 70.8254 91.8233C70.7833 91.8233 70.7417 91.8324 70.7043 91.85L68.8355 92.833C68.7694 92.8674 68.7757 92.9247 68.8355 92.962L70.591 93.8848C70.6287 93.9017 70.6701 93.9105 70.7121 93.9105C70.7541 93.9105 70.7956 93.9017 70.8333 93.8848L72.7241 92.8932C72.7902 92.8588 72.787 92.8044 72.7241 92.7671L70.9465 91.85ZM94.4136 79.5068C94.3759 79.4899 94.3345 79.481 94.2924 79.481C94.2504 79.481 94.209 79.4899 94.1713 79.5068L92.3025 80.4898C92.2396 80.5242 92.2427 80.5815 92.3025 80.6187L94.0581 81.5416C94.0955 81.5591 94.137 81.5683 94.1792 81.5683C94.2213 81.5683 94.2629 81.5591 94.3003 81.5416L96.1691 80.5614C96.232 80.527 96.2289 80.4697 96.1691 80.4325L94.4136 79.5068ZM102.354 75.3312C102.317 75.3143 102.275 75.3055 102.233 75.3055C102.191 75.3055 102.15 75.3143 102.112 75.3312L100.593 76.1308C100.53 76.1652 100.533 76.2225 100.593 76.2569L102.348 77.1826C102.386 77.1995 102.427 77.2083 102.469 77.2083C102.511 77.2083 102.553 77.1995 102.59 77.1826L104.11 76.3859C104.173 76.3515 104.17 76.2942 104.11 76.2569L102.354 75.3312ZM107.215 72.1157L108.732 71.3162C108.798 71.2818 108.795 71.2273 108.732 71.1901L106.97 70.2529C106.932 70.2353 106.891 70.2262 106.849 70.2262C106.807 70.2262 106.765 70.2353 106.728 70.2529L105.208 71.0525C105.145 71.0869 105.148 71.1442 105.208 71.1786L106.964 72.1014C107.002 72.1205 107.045 72.1305 107.089 72.1305C107.133 72.1305 107.177 72.1205 107.215 72.1014V72.1157ZM97.2954 72.0584L98.815 71.2589C98.8779 71.2245 98.8748 71.1671 98.815 71.1328L97.0595 70.2099C97.022 70.1924 96.9805 70.1832 96.9383 70.1832C96.8962 70.1832 96.8547 70.1924 96.8172 70.2099L95.3008 71.0095C95.2347 71.041 95.2379 71.0984 95.3008 71.1356L97.0563 72.0584C97.0948 72.0727 97.1363 72.0788 97.1777 72.0764C97.2191 72.0739 97.2594 72.0628 97.2954 72.0441V72.0584ZM92.2616 79.9768L96.6977 77.6411C96.7606 77.6096 96.7575 77.5523 96.6977 77.515L94.9421 76.5922C94.9047 76.5746 94.8631 76.5654 94.821 76.5654C94.7789 76.5654 94.7373 76.5746 94.6999 76.5922L90.2638 78.925C90.1977 78.9594 90.204 79.0167 90.2638 79.054L92.0194 79.9768C92.0595 79.993 92.1033 80.0001 92.1471 79.9976C92.1909 79.9951 92.2334 79.9831 92.2711 79.9625L92.2616 79.9768ZM104.745 73.2764C104.783 73.294 104.824 73.3032 104.867 73.3032C104.909 73.3032 104.95 73.294 104.988 73.2764L106.504 72.4797C106.57 72.4453 106.564 72.388 106.504 72.3507L104.749 71.4279C104.711 71.411 104.67 71.4022 104.628 71.4022C104.586 71.4022 104.544 71.411 104.506 71.4279L102.987 72.2275C102.924 72.2619 102.927 72.3192 102.987 72.3536L104.745 73.2764ZM102.48 71.9639L103.997 71.1643C104.063 71.1327 104.06 71.0754 103.997 71.0382L102.241 70.1154C102.204 70.0978 102.162 70.0886 102.12 70.0886C102.078 70.0886 102.036 70.0978 101.999 70.1154L100.479 70.9149C100.416 70.9493 100.42 71.0067 100.479 71.041L102.235 71.9639C102.275 71.9813 102.32 71.9891 102.364 71.9866C102.409 71.9841 102.452 71.9713 102.49 71.9495L102.48 71.9639ZM90.0404 81.1346L91.56 80.335C91.6229 80.3006 91.6198 80.2433 91.56 80.2089L89.8045 79.2861C89.767 79.2685 89.7255 79.2593 89.6833 79.2593C89.6412 79.2593 89.5997 79.2685 89.5622 79.2861L88.0458 80.0857C87.9797 80.1172 87.986 80.1745 88.0458 80.2118L89.7982 81.1346C89.8363 81.1543 89.8791 81.1653 89.9229 81.1668C89.9667 81.1683 90.0102 81.1602 90.0499 81.1432L90.0404 81.1346ZM99.8407 75.8471C99.8781 75.8647 99.9197 75.8739 99.9618 75.8739C100.004 75.8739 100.045 75.8647 100.083 75.8471L101.603 75.0475C101.665 75.016 101.662 74.9587 101.603 74.9214L99.847 73.9986C99.8095 73.981 99.768 73.9718 99.7258 73.9718C99.6837 73.9718 99.6422 73.981 99.6047 73.9986L98.0883 74.7982C98.0222 74.8326 98.0253 74.8899 98.0883 74.9243L99.8407 75.8471ZM78.9503 86.9666L80.4667 86.1699C80.5328 86.1355 80.5265 86.0782 80.4667 86.0409L78.7018 85.1124C78.6641 85.0955 78.6226 85.0867 78.5806 85.0867C78.5386 85.0867 78.4972 85.0955 78.4595 85.1124L76.9399 85.912C76.877 85.9464 76.8801 86.0037 76.9399 86.0409L78.6955 86.9638C78.7362 86.9836 78.7818 86.9938 78.828 86.9933C78.8742 86.9928 78.9195 86.9816 78.9597 86.9609L78.9503 86.9666ZM81.1683 85.8002L82.6848 85.0035C82.7508 84.9691 82.7477 84.9118 82.6848 84.8745L80.9292 83.9517C80.8915 83.9348 80.8501 83.926 80.8081 83.926C80.7661 83.926 80.7246 83.9348 80.687 83.9517L79.1674 84.7513C79.1045 84.7857 79.1076 84.843 79.1674 84.8774L80.9229 85.8002C80.9625 85.8189 81.0066 85.8282 81.0512 85.8272C81.0957 85.8262 81.1393 85.8149 81.1778 85.7945L81.1683 85.8002ZM76.7323 88.133L78.2487 87.3363C78.3148 87.3019 78.3085 87.2446 78.2487 87.2074L76.4995 86.2788C76.462 86.2612 76.4205 86.252 76.3783 86.252C76.3362 86.252 76.2947 86.2612 76.2572 86.2788L74.7376 87.0755C74.6747 87.1099 74.6778 87.1672 74.7376 87.2045L76.4932 88.1273C76.5317 88.1452 76.5742 88.1545 76.6174 88.1545C76.6606 88.1545 76.7032 88.1452 76.7417 88.1273L76.7323 88.133ZM85.6233 83.4617L87.1428 82.6621C87.2058 82.6277 87.2026 82.5733 87.1428 82.536L85.3873 81.6132C85.3498 81.5956 85.3083 81.5864 85.2662 81.5864C85.224 81.5864 85.1825 81.5956 85.1451 81.6132L83.6286 82.4128C83.5625 82.4472 83.5657 82.5016 83.6286 82.5389L85.3842 83.4617C85.4212 83.4788 85.4622 83.4877 85.5037 83.4877C85.5452 83.4877 85.5862 83.4788 85.6233 83.4617ZM83.4052 84.6281L84.9248 83.8285C84.9877 83.797 84.9846 83.7397 84.9248 83.7024L83.1693 82.7796C83.1318 82.762 83.0903 82.7528 83.0481 82.7528C83.006 82.7528 82.9645 82.762 82.927 82.7796L81.4074 83.5792C81.3445 83.6136 81.3477 83.6709 81.4074 83.7053L83.163 84.6281C83.1992 84.6443 83.239 84.6527 83.2794 84.6527C83.3197 84.6527 83.3596 84.6443 83.3958 84.6281H83.4052ZM87.8256 82.2895L89.3451 81.49C89.4081 81.4556 89.4049 81.4011 89.3451 81.3639L87.5896 80.4411C87.5521 80.4235 87.5106 80.4143 87.4685 80.4143C87.4263 80.4143 87.3848 80.4235 87.3473 80.4411L85.8309 81.2406C85.7648 81.275 85.7711 81.3295 85.8309 81.3667L87.5864 82.2895C87.6235 82.3067 87.6645 82.3156 87.706 82.3156C87.7475 82.3156 87.7885 82.3067 87.8256 82.2895ZM97.975 65.0285C98.0125 65.0461 98.054 65.0552 98.0961 65.0552C98.1383 65.0552 98.1798 65.0461 98.2173 65.0285L99.7368 64.2289C99.7998 64.1945 99.7966 64.1401 99.7368 64.1028L99.2681 63.8563C99.2306 63.8387 99.1891 63.8296 99.1469 63.8296C99.1048 63.8296 99.0633 63.8387 99.0258 63.8563L97.5094 64.653C97.4464 64.6874 97.4496 64.7448 97.5094 64.782L97.975 65.0285ZM87.061 71.448C87.0987 71.4649 87.1402 71.4737 87.1822 71.4737C87.2242 71.4737 87.2656 71.4649 87.3033 71.448L88.8197 70.6513C88.8858 70.6169 88.8827 70.5596 88.8197 70.5223L87.0642 69.5995C87.0267 69.5819 86.9852 69.5727 86.9431 69.5727C86.9009 69.5727 86.8594 69.5819 86.8219 69.5995L85.3086 70.402C85.2457 70.4363 85.2489 70.4937 85.3086 70.5309L87.061 71.448ZM91.4971 69.1123C91.5346 69.1299 91.5761 69.1391 91.6182 69.1391C91.6604 69.1391 91.7019 69.1299 91.7394 69.1123L93.2589 68.3156C93.3219 68.2812 93.3187 68.2239 93.2589 68.1867L91.5034 67.2638C91.4657 67.2469 91.4243 67.2381 91.3823 67.2381C91.3403 67.2381 91.2988 67.2469 91.2611 67.2638L89.7416 68.0634C89.6786 68.0978 89.6818 68.1551 89.7416 68.1895L91.4971 69.1123ZM84.3648 72.8781C84.4023 72.8957 84.4438 72.9048 84.4859 72.9048C84.5281 72.9048 84.5696 72.8957 84.6071 72.8781L86.1235 72.0814C86.1896 72.047 86.1864 71.9896 86.1235 71.9524L84.3648 71.0324C84.3271 71.0155 84.2857 71.0067 84.2437 71.0067C84.2017 71.0067 84.1602 71.0155 84.1226 71.0324L82.603 71.8292C82.54 71.8635 82.5432 71.9209 82.603 71.9581L84.3648 72.8781ZM89.2917 70.2988C89.3293 70.3157 89.3708 70.3245 89.4128 70.3245C89.4548 70.3245 89.4962 70.3157 89.5339 70.2988L91.0504 69.5021C91.1164 69.4677 91.1133 69.4104 91.0504 69.3731L89.2948 68.4503C89.2574 68.4327 89.2158 68.4235 89.1737 68.4235C89.1315 68.4235 89.09 68.4327 89.0525 68.4503L87.5109 69.2356C87.448 69.2699 87.4512 69.3273 87.5109 69.3617L89.2917 70.2988ZM79.9225 75.2109C79.9601 75.2278 80.0016 75.2366 80.0436 75.2366C80.0856 75.2366 80.127 75.2278 80.1647 75.2109L81.6811 74.4113C81.7441 74.3769 81.7409 74.3196 81.6811 74.2852L79.9256 73.3624C79.8882 73.3448 79.8466 73.3356 79.8045 73.3356C79.7623 73.3356 79.7208 73.3448 79.6833 73.3624L78.1669 74.1591C78.1008 74.1935 78.1071 74.2508 78.1669 74.2881L79.9225 75.2109ZM82.1405 74.0445C82.1779 74.0621 82.2195 74.0712 82.2616 74.0712C82.3038 74.0712 82.3453 74.0621 82.3827 74.0445L83.8992 73.2478C83.9652 73.2134 83.9589 73.156 83.8992 73.1188L82.1468 72.196C82.1091 72.1791 82.0677 72.1702 82.0257 72.1702C81.9836 72.1702 81.9422 72.1791 81.9045 72.196L80.3849 72.9927C80.322 73.0271 80.3252 73.0844 80.3849 73.1217L82.1405 74.0445ZM93.5452 67.367C93.5827 67.3846 93.6242 67.3938 93.6664 67.3938C93.7085 67.3938 93.75 67.3846 93.7875 67.367L95.3039 66.5703C95.37 66.5359 95.3668 66.4786 95.3039 66.4413L94.8352 66.1949C94.7975 66.1779 94.756 66.1691 94.714 66.1691C94.672 66.1691 94.6306 66.1779 94.5929 66.1949L93.0765 66.9916C93.0104 67.026 93.0135 67.0833 93.0765 67.1206L93.5452 67.367ZM100.199 63.8678C100.237 63.8854 100.278 63.8946 100.32 63.8946C100.363 63.8946 100.404 63.8854 100.442 63.8678L101.961 63.0682C102.024 63.0338 102.021 62.9765 101.961 62.9421L101.496 62.6957C101.458 62.6787 101.416 62.6699 101.374 62.6699C101.332 62.6699 101.291 62.6787 101.253 62.6957L99.7337 63.4952C99.6708 63.5296 99.6739 63.5869 99.7337 63.6213L100.199 63.8678ZM103.887 65.808C103.924 65.8249 103.966 65.8337 104.008 65.8337C104.05 65.8337 104.091 65.8249 104.129 65.808L105.648 65.0113C105.711 64.9769 105.708 64.9196 105.648 64.8823L103.871 63.9624C103.833 63.9454 103.792 63.9366 103.75 63.9366C103.708 63.9366 103.666 63.9454 103.629 63.9624L102.112 64.7591C102.046 64.7935 102.049 64.8508 102.112 64.888L103.887 65.808ZM109.433 70.935L110.95 70.1354C111.016 70.101 111.013 70.0466 110.95 70.0093L109.194 69.0865C109.157 69.0689 109.115 69.0598 109.073 69.0598C109.031 69.0598 108.989 69.0689 108.952 69.0865L107.432 69.8861C107.369 69.9205 107.373 69.9749 107.432 70.0122L109.188 70.935C109.226 70.9531 109.268 70.9625 109.311 70.9625C109.353 70.9625 109.395 70.9531 109.433 70.935ZM95.7633 66.2006C95.8007 66.2182 95.8422 66.2274 95.8844 66.2274C95.9265 66.2274 95.9681 66.2182 96.0055 66.2006L97.5251 65.401C97.588 65.3695 97.5849 65.3122 97.5251 65.2749L97.0563 65.0285C97.0187 65.0115 96.9772 65.0027 96.9352 65.0027C96.8932 65.0027 96.8517 65.0115 96.8141 65.0285L95.2976 65.8252C95.2316 65.8596 95.2379 65.9169 95.2976 65.9541L95.7633 66.2006ZM77.7013 76.3773C77.739 76.3942 77.7804 76.403 77.8224 76.403C77.8644 76.403 77.9059 76.3942 77.9435 76.3773L79.4631 75.5777C79.526 75.5433 79.5229 75.486 79.4631 75.4516L77.7076 74.5288C77.6701 74.5112 77.6286 74.502 77.5864 74.502C77.5443 74.502 77.5028 74.5112 77.4653 74.5288L75.9489 75.3284C75.8828 75.3599 75.8891 75.4172 75.9489 75.4545L77.7013 76.3773Z" fill="#E0E0E0"/>
    <path d="M86.7227 88.9384L103.337 80.2004L113.562 85.5768L96.9476 94.3148L86.7227 88.9384Z" fill="#263238"/>
    <path d="M53.3494 37.1208L102.854 11.0845C103.823 10.5744 104.616 10.9928 104.616 12.0102V46.9393C104.599 47.1974 104.517 47.4485 104.375 47.6724C104.232 47.8963 104.035 48.0869 103.798 48.2289L53.0348 74.9244C52.5849 75.1594 52.2168 74.9674 52.2168 74.4945V38.9063C52.2394 38.5488 52.3536 38.2011 52.5503 37.891C52.747 37.581 53.0207 37.3172 53.3494 37.1208Z" fill="#F5F5F5"/>
    <path d="M104.616 12.847V12.0102C104.616 10.9928 103.823 10.5772 102.854 11.0845L53.3494 37.1208C53.0207 37.3172 52.747 37.5809 52.5503 37.891C52.3536 38.201 52.2394 38.5487 52.2168 38.9062V40.4051L104.616 12.847Z" fill="#263238"/>
    <path d="M97.2325 14.7527C97.0581 14.8589 96.9132 15.0008 96.8094 15.1669C96.7057 15.3331 96.6459 15.519 96.6348 15.7099C96.6348 16.0624 96.9085 16.2057 97.242 16.028C97.419 15.9242 97.5664 15.7835 97.672 15.6176C97.7775 15.4516 97.8383 15.2652 97.8492 15.0737C97.8397 14.7212 97.5786 14.5779 97.2325 14.7527Z" fill="#E0E0E0"/>
    <path d="M99.6385 13.489C99.4614 13.5934 99.314 13.7346 99.2084 13.901C99.1029 14.0674 99.0422 14.2542 99.0312 14.4462C99.0312 14.7987 99.3018 14.9391 99.6385 14.7643C99.8167 14.6582 99.9647 14.5151 100.07 14.3466C100.176 14.1782 100.236 13.9894 100.246 13.7956C100.246 13.4546 99.9719 13.3113 99.6385 13.489Z" fill="#E0E0E0"/>
    <path d="M102.049 12.2222C101.872 12.327 101.725 12.4683 101.619 12.6346C101.514 12.8009 101.453 12.9876 101.441 13.1794C101.441 13.5319 101.712 13.6752 102.049 13.4975C102.225 13.393 102.371 13.2521 102.476 13.0863C102.581 12.9204 102.642 12.7343 102.653 12.5432C102.653 12.1907 102.382 12.0474 102.049 12.2222Z" fill="#E0E0E0"/>
    <path d="M52.2168 40.405V47.1255L104.613 19.5702V12.8469L52.2168 40.405Z" fill="#BA68C8"/>
    <path opacity="0.1" d="M52.2168 40.405V47.1255L104.613 19.5702V12.8469L52.2168 40.405Z" fill="black"/>
    <path d="M55.6518 43.6235C55.4756 43.6734 55.2885 43.6823 55.1075 43.6493V40.3565C55.1061 40.2939 55.1212 40.232 55.1516 40.1759C55.189 40.1219 55.241 40.0775 55.3026 40.047L55.5731 39.9065C55.6424 39.8693 55.6927 39.8578 55.7242 39.875C55.7556 39.8922 55.7714 39.9352 55.7714 40.0068V41.0901C55.8717 40.9482 55.9923 40.8191 56.13 40.7061C56.2762 40.5853 56.4366 40.4796 56.6082 40.3909C57.0046 40.1817 57.3098 40.1329 57.5269 40.2504C57.744 40.3679 57.8415 40.6345 57.8415 41.0471C57.8468 41.5103 57.7165 41.9663 57.464 42.3683C57.195 42.7826 56.8094 43.1239 56.3471 43.357C56.13 43.4747 55.896 43.5644 55.6518 43.6235ZM56.9669 42.2795C57.1149 42.0134 57.1873 41.7178 57.1777 41.4197C57.1777 41.1331 57.121 40.9612 57.0109 40.8895C56.9008 40.8179 56.7152 40.8465 56.4541 40.9841C56.2678 41.0839 56.1061 41.2175 55.979 41.3767C55.8484 41.5249 55.7754 41.7083 55.7714 41.8983V43.0274C55.8545 43.0356 55.9386 43.0258 56.0167 42.9988C56.1259 42.9664 56.2313 42.9241 56.3314 42.8727C56.6007 42.7339 56.8212 42.5282 56.9669 42.2795Z" fill="#FAFAFA"/>
    <path d="M58.4106 42.1419C58.3792 42.1218 58.3634 42.0817 58.3634 42.0129V38.6427C58.3615 38.5798 58.3778 38.5175 58.4106 38.4621C58.4464 38.4069 58.4988 38.3621 58.5616 38.3332L58.8416 38.187C58.9109 38.1497 58.9612 38.1411 58.9927 38.1555C59.0241 38.1698 59.0399 38.2157 59.0399 38.2873V41.6576C59.0429 41.7184 59.0276 41.7788 58.9958 41.8324C58.959 41.8886 58.9056 41.9343 58.8416 41.9642L58.5616 42.1104C58.4893 42.1476 58.4389 42.1677 58.4106 42.1419Z" fill="#FAFAFA"/>
    <path d="M59.9299 41.093C59.6813 40.9698 59.5586 40.6918 59.5586 40.2533C59.5573 39.8195 59.6856 39.3935 59.9299 39.021C60.1867 38.6292 60.5524 38.3059 60.9901 38.0839C61.4463 37.8431 61.8018 37.7973 62.0503 37.9062C62.2989 38.0151 62.4247 38.3074 62.4247 38.7459C62.4266 39.1802 62.297 39.6065 62.0503 39.9782C61.7937 40.3709 61.428 40.6951 60.9901 40.9182C60.5308 41.1589 60.1784 41.2162 59.9299 41.093ZM61.5595 39.8664C61.6939 39.6292 61.761 39.3652 61.7546 39.0984C61.7546 38.8118 61.6885 38.6255 61.5595 38.5424C61.4306 38.4593 61.2449 38.4851 60.9901 38.6169C60.7502 38.7398 60.5529 38.9215 60.4206 39.1414C60.2846 39.376 60.2163 39.6384 60.2224 39.9037C60.2224 40.1941 60.2885 40.3851 60.4206 40.4769C60.5528 40.5628 60.7353 40.5399 60.9901 40.4109C61.2319 40.2852 61.4295 40.0993 61.5595 39.875V39.8664Z" fill="#FAFAFA"/>
    <path d="M63.6918 40.2962C63.5511 40.3485 63.3999 40.3729 63.2482 40.3679C63.1915 40.3679 63.179 40.3306 63.2073 40.2676L63.3583 39.9151C63.3702 39.8809 63.3919 39.8502 63.4212 39.8262C63.4387 39.822 63.4572 39.822 63.4747 39.8262C63.7447 39.8075 64.0047 39.7255 64.2298 39.5884C64.4432 39.4825 64.6222 39.3273 64.7489 39.1384C64.8677 38.9259 64.924 38.6889 64.9125 38.4506C64.8194 38.6078 64.703 38.7524 64.5664 38.8805C64.4244 39.0107 64.2635 39.1226 64.0882 39.2129C63.6949 39.4193 63.3866 39.468 63.1664 39.3591C62.9461 39.2502 62.836 38.9865 62.836 38.5767C62.8319 38.1245 62.9683 37.6804 63.2293 37.2957C63.5129 36.8888 63.9047 36.5537 64.3682 36.3213C64.5782 36.2076 64.8049 36.1218 65.0415 36.0662C65.2149 36.0193 65.3977 36.0085 65.5763 36.0347V37.9405C65.5813 38.3646 65.4733 38.7835 65.2617 39.1614C65.0386 39.5399 64.6998 39.8515 64.2864 40.0584C64.0962 40.1533 63.8972 40.2329 63.6918 40.2962ZM64.7269 38.1755C64.8469 38.0128 64.9115 37.8213 64.9125 37.6253V36.6365C64.8247 36.6431 64.7387 36.6625 64.6576 36.6939C64.5487 36.7326 64.4434 36.7796 64.343 36.8343C64.0797 36.9616 63.8615 37.1545 63.7139 37.3904C63.5663 37.6264 63.4954 37.8956 63.5093 38.1669C63.5093 38.4535 63.5691 38.614 63.6918 38.6856C63.8145 38.7573 64.0064 38.7229 64.2518 38.5939C64.4445 38.4909 64.6073 38.3474 64.7269 38.1755Z" fill="#FAFAFA"/>
    <path d="M61.1622 45.3946V69.0179C61.1622 69.1755 61.2534 69.2558 61.3635 69.1956C61.4273 69.1469 61.4785 69.0859 61.5133 69.017C61.5481 68.9481 61.5657 68.8731 61.5649 68.7972V45.174C61.5649 45.0163 61.4736 44.939 61.3635 44.9992C61.2995 45.047 61.2481 45.1074 61.2132 45.1759C61.1783 45.2444 61.1609 45.3192 61.1622 45.3946Z" fill="#BA68C8"/>
    <path d="M45.828 54.5595L57.2453 48.5412C57.6229 48.3435 57.928 48.5039 57.928 48.8994V56.216C57.9152 56.4314 57.8467 56.6411 57.728 56.8278C57.6094 57.0146 57.444 57.1732 57.2453 57.2907L45.828 63.2946C45.4536 63.4924 45.1484 63.3319 45.1484 62.9364V55.6371C45.1609 55.4215 45.229 55.2116 45.347 55.0244C45.4651 54.8372 45.6298 54.6779 45.828 54.5595Z" fill="#E0E0E0"/>
    <path d="M57.9378 48.9109C57.9378 48.5154 57.6232 48.3549 57.2551 48.5527L45.8378 54.571C45.64 54.6891 45.4755 54.8479 45.3575 55.0346C45.2394 55.2213 45.1712 55.4306 45.1582 55.6457V56.2676L57.9378 49.5357V48.9109Z" fill="#BA68C8"/>
    <path d="M46.4219 56.9299L51.5343 54.2417V58.9474L46.4219 61.6356V56.9299Z" fill="#FAFAFA"/>
    <path d="M46.4277 60.9506L48.3437 57.8497C48.3588 57.8185 48.3827 57.7913 48.4129 57.7711C48.4431 57.7508 48.4787 57.7382 48.516 57.7344C48.5533 57.7306 48.591 57.7359 48.6254 57.7496C48.6598 57.7633 48.6896 57.785 48.7118 57.8125L49.382 58.4831L50.5366 56.0557C50.6058 55.9096 50.7411 55.8952 50.8512 56.0271L51.5496 56.9298V58.9359L46.4372 61.6241L46.4277 60.9506Z" fill="#BA68C8"/>
    <path d="M52.8164 53.9751C52.8164 54.1986 52.9989 54.2846 53.2254 54.1642L56.2394 52.5794C56.3562 52.5121 56.454 52.4208 56.5251 52.3128C56.5961 52.2048 56.6383 52.0831 56.6484 51.9575C56.6484 51.734 56.4659 51.648 56.2426 51.7684L53.2191 53.3446C53.1017 53.4129 53.0039 53.5057 52.9339 53.6154C52.8638 53.7251 52.8236 53.8484 52.8164 53.9751Z" fill="#FAFAFA"/>
    <path d="M52.8164 55.9238C52.8164 56.1473 52.9989 56.2333 53.2254 56.1158L56.2394 54.5281C56.3565 54.4611 56.4546 54.3699 56.5257 54.2618C56.5968 54.1537 56.6388 54.0319 56.6484 53.9062C56.6484 53.6827 56.4659 53.5996 56.2426 53.7171L53.2191 55.2875C53.1014 55.3569 53.0035 55.4508 52.9335 55.5614C52.8635 55.672 52.8233 55.7962 52.8164 55.9238Z" fill="#FAFAFA"/>
    <path d="M52.8164 57.8438C52.8164 58.0702 52.9989 58.1533 53.2254 58.0358L56.2394 56.4481C56.3565 56.3818 56.4546 56.291 56.5257 56.1834C56.5968 56.0758 56.6388 55.9544 56.6484 55.8291C56.6484 55.6055 56.4659 55.5196 56.2426 55.6371L53.2191 57.2248C53.1032 57.2918 53.0063 57.3827 52.9363 57.4903C52.8664 57.5979 52.8253 57.719 52.8164 57.8438Z" fill="#FAFAFA"/>
    <path d="M45.828 65.8855L57.2453 59.8815C57.6229 59.6838 57.928 59.8443 57.928 60.2398V67.5563C57.9152 67.7718 57.8467 67.9814 57.728 68.1682C57.6094 68.3549 57.444 68.5135 57.2453 68.631L45.828 74.6493C45.4536 74.847 45.1484 74.6866 45.1484 74.2911V66.9602C45.1614 66.7451 45.2297 66.5358 45.3477 66.3491C45.4658 66.1625 45.6303 66.0037 45.828 65.8855Z" fill="#E0E0E0"/>
    <path d="M57.9378 60.2398C57.9378 59.8443 57.6232 59.6838 57.2551 59.8815L45.8378 65.8855C45.64 66.0037 45.4755 66.1625 45.3575 66.3491C45.2394 66.5358 45.1712 66.7451 45.1582 66.9602V67.585L57.9378 60.8617V60.2398Z" fill="#BA68C8"/>
    <path d="M46.4219 68.2556L51.5343 65.5674V70.2731L46.4219 72.9613V68.2556Z" fill="#FAFAFA"/>
    <path d="M46.4277 72.2792L48.3437 69.1784C48.3588 69.1471 48.3827 69.1199 48.4129 69.0997C48.4431 69.0795 48.4787 69.0668 48.516 69.063C48.5533 69.0593 48.591 69.0645 48.6254 69.0782C48.6598 69.0919 48.6896 69.1136 48.7118 69.1411L49.382 69.8089L50.5366 67.3815C50.6058 67.2353 50.7411 67.2239 50.8512 67.3528L51.5496 68.2556V70.2617L46.4372 72.9498L46.4277 72.2792Z" fill="#BA68C8"/>
    <path d="M52.8164 65.3009C52.8164 65.5244 52.9989 65.6104 53.2254 65.4929L56.2394 63.9052C56.3565 63.8389 56.4546 63.7481 56.5257 63.6405C56.5968 63.5329 56.6388 63.4115 56.6484 63.2862C56.6484 63.0598 56.4659 62.9767 56.2426 63.0942L53.2191 64.6819C53.1032 64.7489 53.0063 64.8398 52.9363 64.9474C52.8664 65.055 52.8253 65.1761 52.8164 65.3009Z" fill="#FAFAFA"/>
    <path d="M52.8164 67.2351C52.8164 67.4615 52.9989 67.5447 53.2254 67.4272L56.2394 65.8395C56.3565 65.7731 56.4546 65.6824 56.5257 65.5748C56.5968 65.4672 56.6388 65.3457 56.6484 65.2204C56.6484 64.9969 56.4659 64.9109 56.2426 65.0284L53.2191 66.6161C53.1032 66.6831 53.0063 66.7741 52.9363 66.8817C52.8664 66.9892 52.8253 67.1104 52.8164 67.2351Z" fill="#FAFAFA"/>
    <path d="M52.8164 69.1726C52.8164 69.3961 52.9989 69.4821 53.2254 69.3617L56.2394 67.7769C56.3562 67.7096 56.454 67.6183 56.5251 67.5103C56.5961 67.4023 56.6383 67.2806 56.6484 67.155C56.6484 66.9315 56.4659 66.8455 56.2426 66.9659L53.2191 68.5507C53.1035 68.6187 53.0069 68.7103 52.937 68.8182C52.867 68.9262 52.8258 69.0475 52.8164 69.1726Z" fill="#FAFAFA"/>
    <path d="M65.6808 54.8317L100.782 36.3727C101.157 36.175 101.462 36.3355 101.462 36.731V46.0536C101.449 46.2687 101.381 46.478 101.263 46.6647C101.144 46.8514 100.98 47.0102 100.782 47.1283L65.6808 65.5873C65.3032 65.7851 64.998 65.6246 64.998 65.2291V55.9064C65.0109 55.691 65.0794 55.4813 65.1981 55.2946C65.3167 55.1078 65.4821 54.9492 65.6808 54.8317Z" fill="#E0E0E0"/>
    <path d="M101.462 36.7224C101.462 36.3269 101.147 36.1665 100.782 36.3642L65.6808 54.8318C65.4821 54.9493 65.3167 55.1079 65.1981 55.2946C65.0794 55.4814 65.0109 55.691 64.998 55.9065V56.5284L101.462 37.3529V36.7224Z" fill="#BA68C8"/>
    <path d="M73.6689 52.8944L66.5775 56.62C66.4195 56.7148 66.2881 56.8419 66.1936 56.9912C66.0991 57.1405 66.0442 57.3078 66.0332 57.4798V63.8649C66.0332 64.1802 66.2786 64.3091 66.5775 64.1515L73.6689 60.4259C73.827 60.3313 73.9586 60.2042 74.0531 60.0549C74.1477 59.9056 74.2025 59.7382 74.2132 59.5661V53.181C74.2006 52.8658 73.9552 52.7368 73.6689 52.8944Z" fill="#FAFAFA"/>
    <path d="M68.5207 58.503L66.0195 63.3749V63.8793C66.0195 64.1946 66.2649 64.3235 66.5638 64.1659L73.6552 60.4403C73.8133 60.3457 73.9449 60.2186 74.0395 60.0693C74.134 59.92 74.1888 59.7526 74.1995 59.5805V57.3566L72.8592 55.0639C72.6956 54.7774 72.4534 54.7917 72.3181 55.084L70.207 59.5977L69.1814 58.4342C69.1473 58.377 69.0952 58.3304 69.0318 58.3004C68.9684 58.2704 68.8966 58.2583 68.8256 58.2657C68.7546 58.2731 68.6877 58.2996 68.6333 58.3419C68.579 58.3842 68.5398 58.4403 68.5207 58.503Z" fill="#BA68C8"/>
    <path d="M66.8407 58.4457C67.197 58.234 67.4935 57.9488 67.7062 57.6132C67.9188 57.2776 68.0416 56.901 68.0645 56.5141C68.0734 56.3096 68.0082 56.1082 67.8789 55.9409L66.5638 56.6316C66.4058 56.7264 66.2744 56.8535 66.1799 57.0027C66.0854 57.152 66.0305 57.3193 66.0195 57.4913V58.5546C66.1559 58.5983 66.3018 58.6112 66.4448 58.5922C66.5878 58.5733 66.7237 58.523 66.8407 58.4457Z" fill="#BA68C8"/>
    <path d="M77.4017 51.4901L99.3177 39.9665C99.6323 39.806 99.862 39.9149 99.862 40.2072C99.8475 40.3722 99.7907 40.5318 99.6962 40.6732C99.6017 40.8146 99.472 40.9337 99.3177 41.0211L77.4017 52.5476C77.0871 52.7052 76.8574 52.5963 76.8574 52.304C76.8719 52.139 76.9287 51.9794 77.0232 51.838C77.1177 51.6967 77.2474 51.5775 77.4017 51.4901Z" fill="#FAFAFA"/>
    <path d="M77.4017 54.1783L99.3177 42.6547C99.6323 42.4942 99.862 42.6031 99.862 42.8955C99.8475 43.0605 99.7907 43.2201 99.6962 43.3614C99.6017 43.5028 99.472 43.622 99.3177 43.7094L77.4017 55.2358C77.0871 55.3935 76.8574 55.2846 76.8574 54.9922C76.8721 54.8273 76.929 54.6678 77.0235 54.5265C77.118 54.3851 77.2475 54.2659 77.4017 54.1783Z" fill="#FAFAFA"/>
    <path d="M77.4017 56.8666L99.3177 45.343C99.6323 45.1825 99.862 45.2914 99.862 45.5837C99.8473 45.749 99.7904 45.9089 99.6959 46.0507C99.6014 46.1925 99.4719 46.3123 99.3177 46.4005L77.4017 57.9298C77.0871 58.0874 76.8574 57.9785 76.8574 57.6862C76.8712 57.5203 76.9276 57.3596 77.0222 57.2172C77.1167 57.0748 77.2467 56.9547 77.4017 56.8666Z" fill="#FAFAFA"/>
    <path d="M113.261 25.3479L110.045 25.259C109.828 25.0928 106.449 21.6194 106.449 21.6194C106.449 21.6194 105.235 23.3647 105.213 23.4765C105.191 23.5882 108.526 26.8352 108.856 27.162C108.949 27.2717 109.068 27.3606 109.204 27.4217C109.341 27.4828 109.491 27.5146 109.643 27.5145C110.514 27.5603 113.777 27.7695 113.777 27.7695L113.261 25.3479Z" fill="#F28F8F"/>
    <path d="M117.831 28.7153L112.873 28.38L113.257 25.3479L118.376 25.4511L117.831 28.7153Z" fill="#BA68C8"/>
    <path opacity="0.2" d="M117.831 28.7153L112.873 28.38L113.257 25.3479L118.376 25.4511L117.831 28.7153Z" fill="black"/>
    <path d="M72.0714 38.6884L107.173 20.2294C107.547 20.0317 107.852 20.1922 107.852 20.5877V29.9132C107.839 30.1282 107.771 30.3374 107.653 30.5241C107.535 30.7107 107.37 30.8696 107.173 30.9879L72.0714 49.4469C71.6938 49.6446 71.3887 49.4841 71.3887 49.0886V39.7631C71.4015 39.5477 71.47 39.338 71.5887 39.1513C71.7073 38.9645 71.8727 38.8059 72.0714 38.6884Z" fill="#E0E0E0"/>
    <path d="M107.852 20.5877C107.852 20.1922 107.538 20.0317 107.173 20.2294L72.0714 38.6884C71.8727 38.8059 71.7073 38.9645 71.5887 39.1513C71.47 39.338 71.4015 39.5477 71.3887 39.7631V40.385L107.852 21.2096V20.5877Z" fill="#BA68C8"/>
    <path d="M80.0458 36.7541L72.9544 40.4797C72.796 40.574 72.6641 40.701 72.5695 40.8503C72.475 40.9997 72.4204 41.1673 72.4102 41.3394V47.7217C72.4102 48.037 72.6556 48.1659 72.9544 48.0083L80.0458 44.2827C80.2043 44.1884 80.3362 44.0614 80.4307 43.912C80.5253 43.7627 80.5799 43.5951 80.5901 43.4229V37.0407C80.5901 36.7225 80.3447 36.5964 80.0458 36.7541Z" fill="#FAFAFA"/>
    <path d="M74.9271 42.3624L72.4102 47.22V47.7244C72.4102 48.0397 72.6556 48.1686 72.9544 48.011L80.0458 44.2854C80.2043 44.1911 80.3362 44.0641 80.4307 43.9148C80.5253 43.7654 80.5799 43.5978 80.5901 43.4256V41.2218L79.2499 38.9291C79.0863 38.6425 78.844 38.6597 78.7087 38.9492L76.5977 43.4715L75.572 42.3051C75.5363 42.2531 75.4854 42.2111 75.425 42.184C75.3646 42.1569 75.2971 42.1457 75.23 42.1517C75.163 42.1577 75.0992 42.1805 75.0456 42.2178C74.9921 42.255 74.951 42.3051 74.9271 42.3624Z" fill="#BA68C8"/>
    <path d="M73.2313 42.3052C73.5875 42.0928 73.8839 41.807 74.0965 41.471C74.3091 41.1349 74.432 40.7581 74.4551 40.3708C74.4636 40.1664 74.3984 39.9651 74.2695 39.7976L72.9544 40.4911C72.796 40.5854 72.6641 40.7124 72.5695 40.8618C72.475 41.0112 72.4204 41.1787 72.4102 41.3509V42.4113C72.5462 42.4554 72.692 42.4688 72.8351 42.4503C72.9781 42.4318 73.114 42.3821 73.2313 42.3052Z" fill="#BA68C8"/>
    <path d="M83.7943 35.3471L105.71 23.8263C106.025 23.6687 106.255 23.7747 106.255 24.067C106.24 24.2324 106.183 24.3924 106.089 24.5343C105.994 24.6761 105.865 24.7958 105.71 24.8838L83.7943 36.4074C83.4954 36.565 83.25 36.459 83.25 36.1667C83.2642 36.0009 83.3209 35.8404 83.4154 35.6981C83.5099 35.5557 83.6396 35.4355 83.7943 35.3471Z" fill="#FAFAFA"/>
    <path d="M83.7943 38.0349L105.71 26.5113C106.025 26.3537 106.255 26.4626 106.255 26.752C106.24 26.9174 106.183 27.0774 106.089 27.2192C105.994 27.361 105.865 27.4808 105.71 27.5688L83.7943 39.0924C83.4954 39.2529 83.25 39.144 83.25 38.8517C83.2647 38.6863 83.3216 38.5264 83.4161 38.3846C83.5106 38.2428 83.6401 38.1231 83.7943 38.0349Z" fill="#FAFAFA"/>
    <path d="M83.7943 40.7346L105.71 29.211C106.025 29.0534 106.255 29.1623 106.255 29.4517C106.24 29.6171 106.183 29.7771 106.089 29.9189C105.994 30.0608 105.865 30.1805 105.71 30.2685L83.7943 41.7921C83.4954 41.9526 83.25 41.8437 83.25 41.5514C83.2647 41.386 83.3216 41.2261 83.4161 41.0843C83.5106 40.9425 83.6401 40.8228 83.7943 40.7346Z" fill="#FAFAFA"/>
    <path d="M124.159 60.7727H122.303V63.2373H124.159V60.7727Z" fill="#FFA8A7"/>
    <path d="M117.632 57.7168L115.777 57.7749L115.87 60.2381L117.725 60.18L117.632 57.7168Z" fill="#FFA8A7"/>
    <path d="M124.042 36.7227C124.042 38.339 124.272 48.7908 124.272 48.7908C124.316 49.2551 124.794 50.1119 124.691 51.9002C124.552 54.3248 124.319 61.071 124.319 61.071C124.319 61.071 123.451 61.8591 122.221 61.071C122.221 61.071 120.538 51.9146 120.204 49.513C119.915 47.4152 119.421 41.715 119.421 41.715L117.716 48.4096C117.716 48.4096 118.03 49.9858 118.087 51.1007C118.143 52.2155 117.772 58.6407 117.772 58.6407C117.472 58.8127 117.127 58.9098 116.773 58.9224C116.418 58.935 116.066 58.8625 115.752 58.7124C115.752 58.7124 114.255 49.3324 114.05 48.3122C113.846 47.2919 115.356 36.7227 115.356 36.7227H124.042Z" fill="#455A64"/>
    <path d="M121.246 25.5256H121.271C122.066 25.7108 122.77 26.1326 123.272 26.724C123.774 27.3154 124.044 28.0425 124.04 28.7898V36.7225C122.737 38.0122 118.198 38.614 115.354 36.7225V28.4689C115.354 27.1506 116.156 25.5657 118.377 25.4482L121.246 25.5256Z" fill="#BA68C8"/>
    <path d="M121.869 18.7795C122.019 18.7636 122.171 18.7791 122.313 18.8248C122.455 18.8705 122.584 18.9452 122.69 19.0432C123.043 19.3699 122.967 19.9774 122.69 21.3702L121.602 21.4849L121.869 18.7795Z" fill="#263238"/>
    <path d="M115.534 18.3809C115.3 18.3818 115.067 18.3389 114.852 18.2548C114.518 18.08 114.433 18.4239 114.616 18.7248C114.736 18.9398 114.915 19.1231 115.135 19.2571C115.356 19.391 115.61 19.4712 115.874 19.49C115.874 19.447 116.189 18.7163 116.189 18.7163L115.534 18.3809Z" fill="#263238"/>
    <path d="M121.361 21.4848C121.524 21.5736 121.754 21.2899 121.952 21.1007C122.031 21.0195 122.131 20.9577 122.243 20.9212C122.355 20.8848 122.475 20.8748 122.592 20.8923C122.71 20.9097 122.82 20.9541 122.913 21.0211C123.007 21.0882 123.08 21.1757 123.126 21.2755C123.456 21.9089 122.707 22.7515 122.267 23.0295C122.144 23.127 121.989 23.1864 121.827 23.1993C121.664 23.2122 121.501 23.1778 121.361 23.1011L121.248 25.5256C121.053 25.9145 120.755 26.2532 120.381 26.5114C120.007 26.7695 119.569 26.9389 119.105 27.0044C117.532 27.1993 117.866 26.0243 118.378 25.4482V24.5684C117.988 24.6441 117.586 24.6625 117.189 24.6229C116.985 24.5741 116.5 24.0497 116.185 23.4393C115.833 22.7658 115.242 21.5707 115.767 19.5503C116.33 17.3808 118.888 17.3149 120.414 18.189C121.94 19.0631 121.361 21.4848 121.361 21.4848Z" fill="#FFA8A7"/>
    <path d="M121.361 21.7342C121.442 21.7342 121.751 21.2671 121.952 21.1009C122.232 20.8716 121.87 18.7795 121.87 18.7795C121.87 18.7795 122.153 17.9885 121.603 17.4612C121.052 16.9339 119.986 17.0256 119.205 16.9998C118.248 17.0051 117.299 16.8315 116.418 16.4897C115.314 16.0512 115.229 15.9996 115.093 16.4439C114.958 16.8881 114.902 17.8997 115.565 18.5818L116.113 18.7652C116.113 18.7652 116.412 19.0059 117.444 19.1206C118.397 19.1861 119.354 19.1688 120.303 19.069C120.602 19.0317 120.668 19.2982 120.829 20.0204C120.97 20.6538 121.09 21.7285 121.361 21.7342Z" fill="#263238"/>
    <path d="M121.87 18.9542L122.845 18.4785C122.814 18.4185 122.77 18.3649 122.716 18.3208C122.661 18.2768 122.597 18.2434 122.528 18.2225C122.459 18.2016 122.386 18.1937 122.313 18.1993C122.241 18.2048 122.17 18.2238 122.106 18.255C121.975 18.3216 121.878 18.4314 121.834 18.5616C121.79 18.6918 121.803 18.8324 121.87 18.9542Z" fill="#263238"/>
    <path d="M118.381 24.5683C119.048 24.4524 119.702 24.2817 120.335 24.0582C120.631 23.9154 120.874 23.696 121.033 23.4277C120.957 23.6968 120.822 23.9493 120.637 24.17C120.265 24.5999 118.381 24.918 118.381 24.918V24.5683Z" fill="#F28F8F"/>
    <path d="M118.727 21.155C118.731 21.2068 118.752 21.2563 118.787 21.2975C118.822 21.3387 118.869 21.3697 118.923 21.3867C118.977 21.4038 119.035 21.4061 119.09 21.3934C119.145 21.3807 119.195 21.3536 119.234 21.3154C119.273 21.2772 119.298 21.2295 119.307 21.1782C119.317 21.127 119.309 21.0744 119.286 21.0269C119.263 20.9794 119.225 20.9391 119.177 20.9109C119.129 20.8828 119.073 20.868 119.016 20.8684C118.977 20.8695 118.937 20.8778 118.901 20.8929C118.865 20.908 118.832 20.9295 118.805 20.9562C118.779 20.9829 118.758 21.0143 118.744 21.0484C118.731 21.0825 118.725 21.1188 118.727 21.155Z" fill="#263238"/>
    <path d="M119.035 20.1578L119.664 20.4702C119.709 20.3927 119.718 20.3026 119.692 20.2187C119.665 20.1348 119.603 20.0637 119.52 20.0203C119.478 19.9982 119.433 19.9843 119.385 19.9793C119.338 19.9744 119.29 19.9785 119.244 19.9914C119.198 20.0044 119.156 20.0259 119.12 20.0545C119.084 20.0832 119.055 20.1184 119.035 20.1578Z" fill="#263238"/>
    <path d="M118.751 22.9463L118.09 23.1727C118.104 23.2134 118.127 23.251 118.158 23.2833C118.188 23.3156 118.226 23.342 118.268 23.3607C118.31 23.3795 118.356 23.3903 118.402 23.3924C118.449 23.3946 118.496 23.3881 118.54 23.3733C118.629 23.3406 118.7 23.278 118.739 23.1984C118.779 23.1188 118.783 23.0285 118.751 22.9463Z" fill="#F28F8F"/>
    <path d="M116.14 20.3871L116.707 19.9888C116.68 19.9541 116.646 19.9246 116.607 19.9023C116.567 19.8799 116.523 19.865 116.478 19.8586C116.432 19.8522 116.385 19.8543 116.34 19.8649C116.295 19.8754 116.253 19.8942 116.216 19.92C116.14 19.9744 116.09 20.053 116.076 20.1399C116.062 20.2268 116.085 20.3152 116.14 20.3871Z" fill="#263238"/>
    <path d="M116.318 21.0806C116.314 21.1171 116.319 21.1539 116.332 21.1885C116.345 21.2232 116.366 21.2551 116.393 21.282C116.42 21.309 116.453 21.3305 116.49 21.3452C116.527 21.3598 116.567 21.3673 116.607 21.3672C116.67 21.3718 116.733 21.359 116.788 21.3304C116.843 21.3019 116.887 21.2589 116.915 21.2071C116.943 21.1553 116.953 21.0971 116.943 21.0401C116.934 20.9831 116.906 20.9299 116.864 20.8875C116.821 20.8451 116.765 20.8154 116.703 20.8024C116.642 20.7893 116.577 20.7935 116.518 20.8143C116.459 20.8352 116.409 20.8718 116.373 20.9192C116.337 20.9667 116.318 21.023 116.318 21.0806Z" fill="#263238"/>
    <path d="M117.792 20.8313L117.698 22.419L116.785 22.1381L117.792 20.8313Z" fill="#F28F8F"/>
    <path d="M122.308 62.4006V62.1599C122.129 62.2287 121.717 63.6645 120.981 64.2778C120.392 64.765 119.593 65.3267 119.392 65.8683C119.191 66.41 120.506 66.7453 121.053 66.6564C121.682 66.5533 122.79 66.1463 123.067 65.768C123.343 65.3897 123.472 64.8309 123.696 64.5558C123.919 64.2806 124.438 63.9683 124.561 63.6473C124.597 63.3476 124.561 63.0442 124.454 62.7589C124.369 62.4723 124.275 62.1312 124.161 62.1599V62.3433C123.898 62.5338 123.57 62.6351 123.233 62.6299C122.966 62.6385 122.305 62.5898 122.308 62.4006Z" fill="#263238"/>
    <path d="M115.849 59.721C115.752 59.721 115.673 59.8471 115.459 60.0592C115.142 60.3519 114.773 60.594 114.368 60.7756C113.672 61.0909 112.32 61.5781 111.709 61.8303C111.325 61.9879 111.344 62.4034 111.737 62.647C112.131 62.8906 113.087 63.0597 114.236 62.8247C114.865 62.6986 115.67 62.1742 116.186 62.2028C116.702 62.2315 117.662 62.2229 118.042 61.985C118.423 61.7471 118.269 61.237 118.162 60.8014C118.045 60.3228 117.876 59.592 117.703 59.6264V59.7754C117.479 60.0821 116.343 60.2082 115.853 59.9818L115.849 59.721Z" fill="#263238"/>
    <path d="M121.806 26.6262L116.067 32.0312C116.067 32.0312 111.033 31.2116 109.316 30.7617C108.774 30.6212 108.397 29.8847 108.277 29.3287C108.247 29.0392 108.092 28.7725 107.846 28.5865C107.846 28.5865 107.846 28.9906 107.733 29.6526C107.689 29.9392 107.192 29.9764 106.748 30.0653C105.175 30.3748 104.64 30.0653 103.989 30.4779C103.486 30.8046 104.118 31.6529 105.267 31.9395C106.881 32.3436 107.783 32.143 108.752 32.3637C109.511 32.5356 114.966 34.0832 116.004 34.301C116.552 34.4099 116.97 34.3755 117.577 33.8396C118.185 33.3037 122.702 29.7042 122.702 29.7042L121.806 26.6262Z" fill="#FFA8A7"/>
    <path d="M122.061 26.4398C121.432 26.2936 117.594 30.452 117.594 30.452L119.708 32.0683C119.708 32.0683 122.445 30.4348 122.93 29.7757C123.414 29.1165 123.965 26.8725 122.061 26.4398Z" fill="#BA68C8"/>
    <path d="M119.845 32.1774C119.845 32.1774 122.733 29.8217 122.868 29.5637C123.129 29.068 123.057 28.1509 123.057 28.1509C123.057 28.1509 123.371 28.9476 123.057 29.5637C122.742 30.1799 120.54 31.9309 120.191 32.2319L119.835 32.5356L119.492 32.2491L119.697 32.0857L119.845 32.1774Z" fill="#BA68C8"/>
    <path opacity="0.2" d="M119.845 32.1774C119.845 32.1774 122.733 29.8217 122.868 29.5637C123.129 29.068 123.057 28.1509 123.057 28.1509C123.057 28.1509 123.371 28.9476 123.057 29.5637C122.742 30.1799 120.54 31.9309 120.191 32.2319L119.835 32.5356L119.492 32.2491L119.697 32.0857L119.845 32.1774Z" fill="black"/>
    <path d="M105.337 20.5132C105.966 20.4502 106.384 20.026 106.526 20.1435C106.583 20.1992 106.617 20.2718 106.62 20.3481C106.624 20.4244 106.597 20.4993 106.545 20.5591C106.255 20.76 105.937 20.9266 105.601 21.0549C105.522 21.3461 105.466 21.642 105.431 21.9404C105.378 22.3789 105.397 23.2558 104.984 23.5539C103.754 24.4538 103.44 23.0266 103.487 22.0694C103.559 20.88 104.679 20.5791 105.337 20.5132Z" fill="#F28F8F"/>
    </svg>
    SVG
            ,
            200
        )
            ->header('Content-Type', 'image/svg+xml');
    }

    //Get nhat ky thao tac
    public function getNhatKyThaoTac(Request $request)
    {
        $chuoiThamChieuID = $request->input('chuoiThamChieuID')
            ?? $request->input('ChuoiThamChieuId');
        $chuoiBangThamChieu = $request->input('chuoiBangThamChieu')
            ?? $request->input('ChuoiBangThamChieu');
        $kyTuPhanCach = $request->input('kyTuPhanCach', ',');

        return response()->json([
            'Result' => NhatKyThaoTac::getAll_TheoThamChieu(
                $chuoiThamChieuID,
                $chuoiBangThamChieu,
                $kyTuPhanCach
            )
        ])->header('Content-Type', 'application/json');
    }

    public function getTruongDuLieuList()
    {
        try {
            $list = TruongDuLieu::get([
                'TenTruong',
                'KieuDuLieu',
                'KieuDuLieu_MoTa',
                'MoTa',
                'ChieuDaiMin',
                'ChieuDaiMax',
            ]);
            return response()->json([
                'Err' => false,
                'Result' => $list,
            ]);
        } catch (Exception $e) {
            $code = $this->dungChungDb->insertMaCodeLoi($e);
            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }

    /**
     * Lấy cấu hình TruongDuLieu cho các model cụ thể.
     *
     * Payload:
     * {
     *   "Models": [
     *     "QuanLy\\BangDiem",
     *     "QuanLy\\BangDiem_ChiTiet",
     *     // ...
     *   ]
     * }
     *
     * Trả về tất cả các TruongDuLieu có TenTruong nằm trong
     * danh sách getExportFieldMap() của từng model.
     */
    public function getTruongDuLieuListTheoModel(Request $request)
    {
        try {
            // 1) grab raw input (could be string or array)
            $raw = $request->input('Models');

            // 2) normalize into a flat array of class names
            $models = [];
            if (is_string($raw)) {
                // single CSV string
                $models = array_filter(array_map('trim', explode(',', $raw)));
            } elseif (is_array($raw)) {
                // could be ['A,B','C'] or ['A','B']
                foreach ($raw as $item) {
                    if (!is_string($item)) {
                        continue;
                    }
                    foreach (explode(',', $item) as $part) {
                        $part = trim($part);
                        if ($part !== '') {
                            $models[] = $part;
                        }
                    }
                }
            }

            if (empty($models)) {
                return response()->json([
                    'Err' => true,
                    'Msg' => 'Payload phải chứa ít nhất một model trong “Models”.',
                ], 400);
            }

            // 3) resolve each model to full class and collect their field‑maps
            $fields = [];
            foreach ($models as $name) {
                // allow both "App\\Models\\…" or shorthand "QuanLy\\…"
                $class = strpos($name, 'App\\') === 0
                    ? $name
                    : 'App\\Models\\' . ltrim($name, '\\');

                if (!class_exists($class) || !method_exists($class, 'getExportFieldMap')) {
                    continue;
                }

                $fields = array_merge($fields, array_keys($class::getExportFieldMap()));
            }

            $fields = array_unique($fields);

            // 4) fetch only those definitions
            $list = TruongDuLieu::whereIn('TenTruong', $fields)
                ->get([
                    'TenTruong',
                    'KieuDuLieu',
                    'KieuDuLieu_MoTa',
                    'MoTa',
                    'ChieuDaiMin',
                    'ChieuDaiMax',
                ]);

            return response()->json([
                'Err' => false,
                'Result' => $list,
            ]);
        } catch (\Exception $e) {
            $code = $this->dungChungDb->insertMaCodeLoi($e);
            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }

    public function ocrInternal(Request $request)
    {
        $file = $request->file('file');
        if (!$file || !$file->isValid()) {
            return response()->json(['error' => 'Invalid file'], 400);
        }
        $apiUrl = env('APP_API_URL', 'http://localhost:8000');
        $apiKey = env('FASTAPI_KEY');

        $client = new Client();

        try {
            $multipart = [
                [
                    'name' => 'file',
                    'contents' => fopen($file->getPathname(), 'r'),
                    'filename' => $file->getClientOriginalName(),
                ],
            ];

            if ($request->has('boxes')) {
                $multipart[] = [
                    'name' => 'boxes',
                    'contents' => $request->input('boxes'), // DO NOT json_encode again
                ];
            }
            if ($request->has('readParagraph')) {
                $multipart[] = [
                    'name' => 'readParagraph',
                    'contents' => $request->input('readParagraph') ? 'true' : 'false',
                ];
            }

            $response = $client->post($apiUrl . '/ocr/vietocr_auto', [
                'headers' => [
                    'X-API-Key' => $apiKey,
                ],
                'multipart' => $multipart,
            ]);

            $data = json_decode($response->getBody()->getContents(), true);
            return response()->json($data);

        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'OCR service error: ' . $e->getMessage(),
            ]);
        }
    }

    public function store(Request $request)
    {
        $payload = $request->validate([
            'training_data' => 'required|array',
            'training_data.*.text' => 'required|string',
            'training_data.*.crop_image_base64' => 'required|string',
        ]);

        ProcessOCRTrainingData::dispatch($payload['training_data']);

        return response()->json(['status' => 'queued']);
    }

    public function getOCRFile($file)
    {
        // 1) Authorization
        if (Gate::denies('download-ocr-training')) {
            abort(403, 'Unauthorized');
        }

        // 2) Build the path
        $path = "ocr_training/{$file}";

        // 3) Check existence
        if (!Storage::disk('local')->exists($path)) {
            abort(404);
        }

        // 4) Stream it back (with correct mime‑type)
        return Storage::disk('local')
            ->response($path);
    }
}
