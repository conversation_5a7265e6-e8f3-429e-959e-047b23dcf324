<?php

namespace App\Http\Controllers\DungChung;

use App\Http\Controllers\Controller;
use App\Services\CurrentUserService;
use App\Models\DanhMuc\NhanVien;
use MongoDB\BSON\ObjectId;

class GetCurrentUserController extends Controller
{
    protected CurrentUserService $currentUser;

    public function __construct(CurrentUserService $currentUser)
    {
        $this->currentUser = $currentUser;
    }

    public function getCurrentUserInfo()
    {
        try {
            $userModel = $this->currentUser->user();

            if (!$userModel) {
                return [
                    'status' => false,
                    'code' => 404,
                    'msg' => 'Không tìm thấy thông tin người dùng.',
                    'data' => null
                ];
            }

            $userInfo = $this->currentUser->user();

            $nhanVien = '';
            $chucVu = '';

            if (!empty($userModel->NhanVienID)) {
                $nhanVienId = $userModel->NhanVienID;

                if (is_string($nhanVienId) && $this->isMongoId($nhanVienId)) {
                    $nhanVienId = new ObjectId($nhanVienId);
                }

                $nhanVien = NhanVien::with('chucVu')->where('_id', $nhanVienId)->first();

                if ($nhanVien && $nhanVien->chucVu) {
                    $chucVu = [
                        'id' => (string) $nhanVien->chucVu->_id,
                        'maChucVu' => $nhanVien->chucVu->MaChucVu ?? null,
                        'tenChucVu' => $nhanVien->chucVu->TenChucVu ?? null,
                    ];
                }
            }

            return [
                'status' => true,
                'code' => 200,
                'msg' => 'Lấy thông tin người dùng thành công.',
                'data' => [
                    'userInfo' => $userInfo,
                    'nhanVien' => $nhanVien,
                ]
            ];
        } catch (\Exception $e) {
            return [
                'status' => false,
                'code' => 500,
                'msg' => 'Đã xảy ra lỗi khi lấy thông tin người dùng: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    private function isMongoId($id): bool
    {
        return preg_match('/^[a-f\d]{24}$/i', $id);
    }
}
