<?php

namespace App\Http\Controllers\QuanLy;

use Str;
use Storage;
use DateTime;
use Carbon\Carbon;
use App\Models\User;
use MongoDB\BSON\Regex;
use App\Services\ThongBao;
use App\Models\QuanLy\SoGoc;
use Illuminate\Http\Request;
use App\Models\DanhMuc\DonVi;
use App\Models\DanhMuc\KyThi;
use App\Models\DanhMuc\LopHoc;
use App\Services\DungChungDb;
use App\Models\DanhMuc\CapHoc;
use App\Models\DanhMuc\ChucVu;
use App\Models\DanhMuc\DanToc;
use App\Models\QuanLy\SoGocCT;
use App\Models\DanhMuc\KhoaThi;
use App\Models\DanhMuc\XepLoai;
use App\Models\QuanLy\DoiTuong;
use App\Services\DungChungNoDb;
use App\Models\DanhMuc\GioiTinh;
use App\Models\DanhMuc\NhanVien;
use App\Models\QuanLy\QuyetDinh;
use App\Models\DanhMuc\TrangThai;
use Illuminate\Support\Facades\DB;
use App\Models\DanhMuc\LoaiChungTu;
use App\Http\Controllers\Controller;
use App\Services\CurrentUserService;
use Illuminate\Support\Facades\Auth;
use App\Models\DanhMuc\HinhThucDaoTao;
use App\Services\OfficeConvertService;
use App\Models\DanhMuc\DiaBanHanhChinh;
use App\Models\QuanLy\CapBangTotNghiep;
use App\Models\QuanLy\TiepNhanPhoiVBCC;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpWord\TemplateProcessor;
use App\Models\QuanLy\CapBangTotNghiepCT;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use App\Models\DanhMuc\LoaiPhoiVanBangChungChi;
use MongoDB\BSON\ObjectId; // from mongodb/mongodb

class SoGocBanSaoController extends Controller
{
    protected DungChungDb $dungChungDb;
    protected DungChungNoDb $logService;
    protected CurrentUserService $currentUser;

    public function __construct(DungChungDb $dungChungDb,CurrentUserService $currentUser, DungChungNoDb $logService)
    {
        $this->dungChungDb = $dungChungDb;
        $this->logService  = $logService;
        $this->currentUser  = $currentUser;
    }
    function safeObjectId($id) {
        return is_string($id) && preg_match('/^[a-f\d]{24}$/i', $id) ? new ObjectId($id) : null;
    }

    public function index()
    {
        return view('quanly.SoGocBanSao.index' );
    }
    /**
     * Thêm mới hoặc cập nhật thông tin đia bàn
     */
    public function luuthongtin(Request $request)
    {
        try {
            $SoSoGoc =$request->input('SoSoGoc');
            $NgayKy = $request->filled('NgayKy')
            ? Carbon::createFromFormat('d/m/Y', $request->input('NgayKy'))
            : null;
            $NhanVienID_NguoiKy = $this->safeObjectId($request->input('NhanVienID_NguoiKy'));
            $ChucVuID_NguoiKy = $this->safeObjectId($request->input('ChucVuID_NguoiKy'));
            $HinhThucDaoTaoID = $this->safeObjectId($request->input('HinhThucDaoTaoID'));
            $CoQuanBanHanh =$request->input('CoQuanBanHanh');
            $QuyetDinhID = $this->safeObjectId($request->input('QuyetDinhID'));
            $NamTotNghiep = $request->input('NamTotNghiep');
            $DonViID_TruongHoc = $this->safeObjectId($request->input('DonViID_TruongHoc'));
            $SoGocID = $this->safeObjectId($request->input('SoGocID'));
            $TrichYeu = $request->input('TrichYeu');
            $DuongDanFileVB = $request->input('DuongDanFileVB') ? $request->input('DuongDanFileVB') : '';
        
            $userId = auth()->id() ? new ObjectId(auth()->id()) : null;
            $now = now();
        
            if ($request->isMethod('post')) {
               
            $paths =$this->logService->saveFiles($DuongDanFileVB,"SoGoc");

            $soGoc = SoGoc::create([
                    'SoSoGoc' => $SoSoGoc,
                    'NgayKy' => $NgayKy,
                    'NhanVienID_NguoiKy' => $NhanVienID_NguoiKy,
                    'ChucVuID_NguoiKy' => $ChucVuID_NguoiKy,
                    'HinhThucDaoTaoID' => $HinhThucDaoTaoID,
                    'CoQuanBanHanh' => $CoQuanBanHanh,

                    'QuyetDinhID' => $QuyetDinhID,
                    'NamTotNghiep' => $NamTotNghiep,
                    'DonViID_TruongHoc' => $DonViID_TruongHoc,
                    'DinhKem' => $paths,
                    'TrichYeu' => $TrichYeu,
                    'TrangThai_Giao' => "37",
                    'LaBanSao' => true,

                    'UserID_ThaoTac'   => $userId,
                    'UserID_CapNhat'   => $userId,
                    'NgayThaoTac'   => $now,
                    'NgayCapNhat'   => $now,
                ]);
                $this->logService->ghiLogs($request->all(), 'them', auth()->id(), null);
                return response()->json([
                    'Err' => false,
                    'Msg' => 'Thêm dữ liệu thành công!',
                    'Result'  => $this->formatSoGocs(collect([$soGoc]))->first()
                ]);
            }

            if ($request->isMethod('put') || $request->isMethod('patch')) {
                // Update
                $SoGoc = SoGoc::find($SoGocID);
                if (!$SoGoc) {
                    return response()->json([
                        'Err' => true,
                        'canhbao' => true,
                        'Msg' => 'Không tìm thấy dữ liệu cần cập nhật!'
                    ]);
                }       
                // ✅ Xóa các file đính kèm nếu có
                if (!empty($SoGoc->DinhKem)) {
                    $paths = explode('|', $SoGoc->DinhKem);
                    foreach ($paths as $path) {
                        // Xử lý path: bỏ /storage/ để về path thật trong public
                        $relativePath = str_replace('/storage/', 'public/', $path);
                        if (Storage::exists($relativePath)) {
                            Storage::delete($relativePath);
                        }
                    }
                }
                $paths = $this->logService->saveFiles($DuongDanFileVB,"SoGoc");

                $SoGoc->update([
                    'SoSoGoc' => $SoSoGoc,
                    'NgayKy' => $NgayKy,
                    'NhanVienID_NguoiKy' => $NhanVienID_NguoiKy,
                    'ChucVuID_NguoiKy' => $ChucVuID_NguoiKy,
                    'HinhThucDaoTaoID' => $HinhThucDaoTaoID,
                    'CoQuanBanHanh' => $CoQuanBanHanh,

                    'QuyetDinhID' => $QuyetDinhID,
                    'NamTotNghiep' => $NamTotNghiep,
                    'DonViID_TruongHoc' => $DonViID_TruongHoc,
                    'DinhKem' => $paths,
                    'TrichYeu' => $TrichYeu,
                    'LaBanSao' => true,
                    
                    'UserID_CapNhat'   => $userId,
                    'NgayCapNhat'   => $now,
                ]);
                $this->logService->ghiLogs($request->all(), 'sua', auth()->id(), null);
                return response()->json([
                    'Err' => false,
                    'Msg' => 'Cập nhật dữ liệu thành công!',
                    'Result'  =>$this->formatSoGocs(collect([$SoGoc]))->first()//$SoGoc->_id
                ]);
            }

            // Nếu không phải POST hoặc PUT
            return response()->json([
                'Err' => true,
                'canhbao' => true,
                'Msg' => 'Phương thức HTTP không hợp lệ!'
            ]);

        } catch (\Exception $e) {
            $this->logService->ghiLogs($request->all(), $request->isMethod('put') ? 'sua' : 'them', auth()->id(), null);
            $code = $this->dungChungDb->insertMaCodeLoi($e);
            return response()->json([
                'Err' => true,
                'Msg' => 'Có lỗi xảy ra!',
                'debug' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Lấy toàn bộ đia bàn dưới dạng JSON
     */
    public function getAll(Request $request)
    {
        try {
            $query = SoGoc::query()->where('LaBanSao', true);

            if ($request->filled('QuyetDinhID')) {
                $query->where('QuyetDinhID', new ObjectId($request->input('QuyetDinhID')));
            }
            if ($request->filled('NamTotNghiep')) {
                $query->where('NamTotNghiep', $request->input('NamTotNghiep'));
            }
            if ($request->filled('CapHocID')) {
                $capHocID = $request->input('CapHocID');
                $kyThiIDs = KyThi::where('CapHocID', $capHocID)->get()
                    ->pluck('_id')
                    ->filter(fn($id) => !empty($id)) // lọc bỏ null, rỗng
                    ->map(fn($id) => $id instanceof ObjectId ? $id : new ObjectId((string) $id));
                // Gắn vào query chính
                $query->whereIn('KyThiID', $kyThiIDs);
            }
            if ($request->filled('TrangThai')) {
                $query->where('TrangThai_Giao', $request->input('TrangThai'));
            }
            $searchKey = null;
            if ($request->filled('SearchKey')) {
                $searchKey = mb_strtolower($request->input('SearchKey')); // chuẩn hóa để so sánh không phân biệt hoa thường
            }
            // Sắp xếp theo field được chọn (nếu có), mặc định là 'MaSoGoc'
            $sortField = $request->input('CbSapXep', 'NamTotNghiep');
            $query->orderBy($sortField, 'asc');

                // Lấy dữ liệu DonViID
                $arrDonViID = $this->logService->LayMaDonViTheoUserID();
                // Lấy danh sách _id của User thuộc các đơn vị
                $userIds = User::whereIn('DonViID', $arrDonViID)
                ->pluck('id')
                ->map(fn($id) => new ObjectId($id)) // đảm bảo là ObjectId nếu cần
                ->toArray();
                // Truy tiếp theo UserID_ThaoTac nằm trong danh sách đó
                $result = $query->whereIn('UserID_ThaoTac', $userIds)->get();

            $result = $this->formatSoGocs($result);
            if ($searchKey) {
                $result = $result->filter(function ($item) use ($searchKey) {
                    $fieldsToSearch = [
                        $item->TenQuyetDinh ?? '',
                        $item->TenKyThi ?? '',
                        $item->TenCapHoc ?? '',
                        $item->TenTrangThai_Giao ?? '',
                        $item->TenDonVi ?? '',
                        $item->TenHinhThuc ?? '',
                        $item->TenChucVu ?? '',
                        $item->NguoiKy ?? '',
                        $item->TenChucVuGiao ?? '',
                        $item->TenNhanVienGiao ?? '',
                        $item->SoSoGoc ?? '',
                    ];

                    foreach ($fieldsToSearch as $value) {
                        if (mb_stripos($value, $searchKey) !== false) {
                            return true;
                        }
                    }
                    return false;
                })->values(); // reset key để không bị lỗ hổng index
            }
        // Map lại từng dòng để thêm Tên Cha
            return response()->json([
                'Err'    => false,
                'result' => $result,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err'   => true,
                'Msg'   => 'Không thể lấy danh sách!',
                'debug' => $e->getMessage(),
            ]);
        }
    }
    /**
     * Tải dữ liệu cho form sửa
     */
    public function loadDuLieuSua(Request $request)
    {
        $id = $request->input('id');
        $SoGoc = SoGoc::find($id);
        if (!$SoGoc) {
            return response()->json([
                'Err' => true,
                'canhbao' => true,
                'Msg' => 'Không tìm thấy dữ liệu!'
            ]);
        }
        $SoGoc = $this->formatSoGocs(collect([$SoGoc]))->first(); // Map 1 item rồi lấy lại object
        return response()->json([
            'Err'    => false,
            'Result' => $SoGoc
        ]);
    }
    /**
     * Tải dữ liệu cho form sửa
     */
    public function loadDuLieuSuact(Request $request)
    {
        $id = $request->input('id');
        $SoGoc = SoGocCT::find($id);

        if (!$SoGoc) {
            return response()->json([
                'Err' => true,
                'canhbao' => true,
                'Msg' => 'Không tìm thấy dữ liệu!'
            ]);
        }

        // Lấy thông tin học sinh
        $hocSinh = DoiTuong::find($SoGoc->DoiTuongID_HocSinh);
        $tenHocSinh = $hocSinh->Hovaten ?? 'Không rõ';
        $tenTruong = optional(DonVi::find($hocSinh->DonViID_Hoc))->TenDonVi ?? 'Không rõ';

        // Gắn thêm vào model (nếu muốn bạn có thể trả riêng)
        $SoGoc->TenHocSinh = $tenHocSinh;
        $SoGoc->TenTruong = $tenTruong;

        return response()->json([
            'Err'    => false,
            'Result' => $SoGoc
        ]);
    }
   /**
     * Xóa
     */
    public function xoa(Request $request)
    {
        try {
            $id = $request->input('ma');
            $model = SoGoc::whereKey($id)->firstOrFail();
            // ✅ Xoá các bản ghi chi tiết liên quan (SoGocCT)
            SoGocCT::where('SoGocID', $model->_id)->delete(); // hoặc (string)$model->_id nếu cần
            // ✅ Xóa file đính kèm nếu có
            if (!empty($model->DinhKem)) {
                $paths = [];
    
                // Xử lý chuỗi đường dẫn file (dùng | hoặc , làm dấu ngăn cách)
                if (is_string($model->DinhKem)) {
                    $paths = preg_split('/[|,]/', $model->DinhKem);
                }
    
                foreach ($paths as $path) {
                    $cleanPath = str_replace('\\', '/', trim($path));
    
                    // Loại bỏ '/storage/' => còn lại: uploads/...
                    $relativePath = str_replace('/storage/', '', $cleanPath);
    
                    // Laravel file system path: storage/app/public/...
                    $fullStoragePath = 'public/' . $relativePath;
                    if (Storage::disk('public')->exists($relativePath)) {
                        Storage::disk('public')->delete($relativePath);
                    } else {
                        Log::warning("Không tìm thấy file để xoá: $fullStoragePath");
                    }
                }
            }
    
            // ✅ Xoá dữ liệu gốc
            $model->delete();
    
            return response()->json([
                'Err' => false,
                'Msg' => 'Xóa dữ liệu và file đính kèm thành công!'
            ]);
    
        } catch (\Exception $e) {
            $code = $this->dungChungDb->insertMaCodeLoi($e);
    
            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }

    function parseNgayKy($input): ?DateTime {
        if ($input instanceof \MongoDB\BSON\UTCDateTime) {
            return $input->toDateTime();
        }
        if ($input instanceof \DateTimeInterface) {
            return $input;
        }
        if (is_string($input)) {
            $dt = DateTime::createFromFormat('d/m/Y', $input);
            if ($dt) return $dt;
    
            try {
                return new DateTime($input);
            } catch (Exception $e) {
                return null;
            }
        }
        return null;
    }
    public function formatSoGocs($collection)
    {
        return $collection->map(function ($item) {
            // 1. Lấy quyết định
        $qd = QuyetDinh::with(['hinhThucDaoTao'])
        ->find($item->QuyetDinhID);
            if ($qd && !empty($qd->NgayKy)) {
                $date = $this->parseNgayKy($qd->NgayKy);
                $ngay = $date->format('d');
                $thang = $date->format('m');
                $nam = $date->format('Y');

                $item->TenQuyetDinh = "Quyết định công nhận tốt nghiệp số {$qd->SoQuyetDinh} ngày {$ngay} tháng {$thang} năm {$nam}";
                $item->TenKyThi = optional(KyThi::find($qd->KyThiID))->TenKyThi ?? '';
                $item->TenCapHoc = optional(CapHoc::find(optional(KyThi::find($qd->KyThiID))->CapHocID))->tenCapHoc ?? '';
            } elseif ($qd) {
                $item->TenQuyetDinh = "Quyết định công nhận tốt nghiệp số {$qd->SoQuyetDinh} ngày ... tháng ... năm ...";
                $item->TenKyThi = optional(KyThi::find($qd->KyThiID))->TenKyThi ?? '';
                $item->TenCapHoc = optional(CapHoc::find(optional(KyThi::find($qd->KyThiID))->CapHocID))->tenCapHoc ?? '';

            } else {
                $item->TenKyThi ='';
                $item->TenCapHoc ='';
                $item->TenQuyetDinh = '';
            }      
             // Lấy thông tin trạng thái cấp bằng
           
            $trangThai_Giao = TrangThai::where("MaTrangThai", $item->TrangThai_Giao)->first();
            $item->TenTrangThai_Giao = optional($trangThai_Giao)->TenTrangThai ?? 'Chưa xác định';
            $item->MauSac_Giao = optional($trangThai_Giao)->MauSac ?? '#ffffff';
            $item->Ma_Giao = optional($trangThai_Giao)->MaTrangThai ?? '';

            $item->txtNgayKy = optional($item->NgayKy)->format('d/m/Y') ?? '';
            $item->txtNgayGiao = optional($item->NgayGiao)->format('d/m/Y') ?? '';
            $item->TenDonVi = optional(DonVi::find($item->DonViID_TruongHoc))->TenDonVi ?? '';
            $item->TenHinhThuc = optional(HinhThucDaoTao::find($item->HinhThucDaoTaoID))->tenHinhThucDaoTao ?? '';
            $item->TenChucVu = optional(ChucVu::find($item->ChucVuID_NguoiKy))->tenChucVu ?? '';
            $item->NguoiKy = optional(NhanVien::find($item->NhanVienID_NguoiKy))->tenNhanVien ?? '';
            $item->TenChucVuGiao = optional(ChucVu::find($item->ChucVuID_Giao))->tenChucVu ?? '';
            $item->TenNhanVienGiao = optional(NhanVien::find($item->NhanVienID_Giao))->tenNhanVien ?? '';
            return $item;
        });
    }

        /**
     * Thêm mới hoặc cập nhật thông tin đia bàn
     */
    public function luuthongtinct(Request $request)
    {
        try {
            $DoiTuongID_HocSinh = $this->safeObjectId($request->input('DoiTuongID_HocSinh'));
            $XepLoaiID = $this->safeObjectId($request->input('XepLoaiID'));
            $DiemThi = $request->input('DiemThi');
            $SoVaoSoGocBanSao = $request->input('SoVaoSoGocBanSao');
            //$SoHieuVanBang = $request->input('SoHieuVanBang');
            $GhiChu = $request->input('GhiChu');
            $SoGocID = $this->safeObjectId($request->input('SoGocID'));

            $SoGocCTID = $this->safeObjectId($request->input('SoGocCTID'));
        
            $MaHocSinh = $request->input('MaHocSinh');
            $TenHocSinh = $request->input('TenHocSinh');
            $CCCD = $request->input('CCCD');
            $NgaySinh = $request->filled('NgaySinh')
            ? Carbon::createFromFormat('d/m/Y', $request->input('NgaySinh'))
            : null;;
            $GioiTinhID = $this->safeObjectId($request->input('GioiTinhID'));
            $DanTocID = $this->safeObjectId($request->input('DanTocID'));
            $NoiSinh = $request->input('NoiSinh');
            $LopHocID = $this->safeObjectId($request->input('LopHocID'));
            $DiaChi = $request->input('DiaChi');
            $DiaBanHCID_Tinh = $this->safeObjectId($request->input('DiaBanHCID_Tinh'));

            $userId = auth()->id() ? new ObjectId(auth()->id()) : null;
            $now = now();
        
            if ($request->isMethod('post')) {           

            SoGocCT::create([
                    'SoGocID' => $SoGocID,
                    'DoiTuongID_HocSinh' => $DoiTuongID_HocSinh,
                    'XepLoaiID' => $XepLoaiID,
                    'DiemThi' => $DiemThi,
                    'SoVaoSoGocBanSao' => $SoVaoSoGocBanSao,
                    //'SoHieuVanBang' => $SoHieuVanBang,
                    'GhiChu' => $GhiChu,

                    'MaHocSinh' => $MaHocSinh,
                    'TenHocSinh' => $TenHocSinh,
                    'CCCD' => $CCCD,
                    'NgaySinh' => $NgaySinh,
                    'GioiTinhID' => $GioiTinhID,
                    'DanTocID' => $DanTocID,
                    'NoiSinh' => $NoiSinh,
                    'LopHocID' => $LopHocID,
                    'DiaChi' => $DiaChi,
                    'DiaBanHCID_Tinh' => $DiaBanHCID_Tinh,

                    'UserID_ThaoTac'   => $userId,
                    'UserID_CapNhat'   => $userId,
                    'NgayThaoTac'   => $now,
                    'NgayCapNhat'   => $now,
                ]);
                $this->logService->ghiLogs($request->all(), 'them', auth()->id(), null);
                return response()->json([
                    'Err' => false,
                    'Msg' => 'Thêm dữ liệu thành công!'
                ]);
            }

            if ($request->isMethod('put') || $request->isMethod('patch')) {
                // Update
                $SoGocCT = SoGocCT::find($SoGocCTID);
                if (!$SoGocCT) {
                    return response()->json([
                        'Err' => true,
                        'canhbao' => true,
                        'Msg' => 'Không tìm thấy dữ liệu cần cập nhật!'
                    ]);
                }       
               
                $SoGocCT->update([
                    'SoGocID' => $SoGocID,
                    'DoiTuongID_HocSinh' => $DoiTuongID_HocSinh,
                    'XepLoaiID' => $XepLoaiID,
                    'DiemThi' => $DiemThi,
                    'SoVaoSoGocBanSao' => $SoVaoSoGocBanSao,
                    //'SoHieuVanBang' => $SoHieuVanBang,
                    'GhiChu' => $GhiChu,

                    'MaHocSinh' => $MaHocSinh,
                    'TenHocSinh' => $TenHocSinh,
                    'CCCD' => $CCCD,
                    'NgaySinh' => $NgaySinh,
                    'GioiTinhID' => $GioiTinhID,
                    'DanTocID' => $DanTocID,
                    'NoiSinh' => $NoiSinh,
                    'LopHocID' => $LopHocID,
                    'DiaChi' => $DiaChi,
                    'DiaBanHCID_Tinh' => $DiaBanHCID_Tinh,

                    'UserID_CapNhat'   => $userId,
                    'NgayCapNhat'   => $now,
                ]);
                $this->logService->ghiLogs($request->all(), 'sua', auth()->id(), null);
                return response()->json([
                    'Err' => false,
                    'Msg' => 'Cập nhật dữ liệu thành công!'
                ]);
            }

            // Nếu không phải POST hoặc PUT
            return response()->json([
                'Err' => true,
                'canhbao' => true,
                'Msg' => 'Phương thức HTTP không hợp lệ!'
            ]);

        } catch (\Exception $e) {
            dd($e);
            $this->logService->ghiLogs($request->all(), $request->isMethod('put') ? 'sua' : 'them', auth()->id(), null);
            $code = $this->dungChungDb->insertMaCodeLoi($e);
            return response()->json([
                'Err' => true,
                'Msg' => 'Có lỗi xảy ra!',
                'debug' => $e->getMessage(),
            ]);
        }
    }
    /**
     * Lấy toàn bộ đia bàn dưới dạng JSON
     */
    public function getallct(Request $request)
    {
        try {
            // 1. Truy vấn SoGocCT theo SoGocID
            $query = SoGocCT::query();
            $query->where('SoGocID', new ObjectId($request->input('SoGocID')));
            $result = $query->get();
            $result = $result->values()->map(function ($item, $index) {
                            $item['STT'] = $index + 1;
                            $item['txtNgaySinh'] = optional($item->NgaySinh)->format('d/m/Y') ?? '';
                            $item['TenGioiTinh'] = optional(GioiTinh::find($item->GioiTinhID))->TenGioiTinh ?? '';
                            $item['TenDanToc'] = optional(DanToc::find($item->DanTocID))->tenDanToc ?? '';
                            $item['TenLop'] = optional(LopHoc::find($item->LopHocID))->TenLopHoc ?? '';
                            $item['TenXepLoai'] = optional(XepLoai::find($item->XepLoaiID))->tenXepLoai ?? '';
                            return $item;
                        });

            return response()->json([
                'Err'    => false,
                'result' => $result,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err'   => true,
                'Msg'   => 'Không thể lấy danh sách!',
                'debug' => $e->getMessage(),
            ]);
        }
    }


    public function formatDoiTuongs($collection)
    {
        return $collection->map(function ($item) {
            // 2. Format ngày sinh
            $item->txtNgaysinh = optional($item->Ngaysinh)->format('d/m/Y') ?? '';

            // 3. Format ngày cấp
            $item->txtNgayCap = optional($item->NgayCap)->format('d/m/Y') ?? '';

            // 4. DonViID_Hoc
            $item->TenDonVi = optional(DonVi::find($item->DonViID_Hoc))->TenDonVi ?? '';

            // 5. DiaBanHCID_NoiCap
            $item->TenNoiCap = optional(DiaBanHanhChinh::find($item->DiaBanHCID_NoiCap))->TenDiaBan ?? '';

            // 6. DanTocID
            $item->TenDanToc = optional(DanToc::find($item->DanTocID))->tenDanToc ?? '';
            // 6. DanTocID
            $item->TenGioiTinh = optional(GioiTinh::find($item->Gioitinh))->TenGioiTinh ?? '';
            // 7. DiaBanHCID_Tinh
            $item->Tinh = optional(DiaBanHanhChinh::find($item->DiaBanHCID_Tinh))->TenDiaBan ?? '';
           
            // 8. DiaBanHCID_Xa
            $item->Xa = optional(DiaBanHanhChinh::find($item->DiaBanHCID_Xa))->TenDiaBan ?? '';

            // 9. DiaBanHCID_Thon
            $item->Thon = optional(DiaBanHanhChinh::find($item->DiaBanHCID_Thon))->TenDiaBan ?? '';

            return $item;
        });
    }
    public function xoact(Request $request)
    {
        try {
            $id = $request->input('ma');
            $model = SoGocCT::whereKey($id)->firstOrFail();
            
            // ✅ Xoá dữ liệu gốc
            $model->delete();
    
            return response()->json([
                'Err' => false,
                'Msg' => 'Xóa dữ liệu và file đính kèm thành công!'
            ]);
    
        } catch (\Exception $e) {
            $code = $this->dungChungDb->insertMaCodeLoi($e);
    
            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }
    public function LayThongTinThongKeXepLoai(Request $request)
    {
        $soGocID = new ObjectId($request->input('SoGocID'));

        // Bước 1: Thống kê số lượng theo XepLoaiID
        $thongKe = SoGocCT::raw(function ($collection) use ($soGocID) {
            return $collection->aggregate([
                ['$match' => ['SoGocID' => $soGocID]],
                ['$group' => [
                    '_id' => '$XepLoaiID',
                    'SoLuong' => ['$sum' => 1],
                ]],
            ]);
        });

        // Tổng cộng tất cả số lượng
        $tongCong = collect($thongKe)->sum('SoLuong');

        // Đưa thống kê về map: [XepLoaiID => SoLuong]
        $thongKeMap = collect($thongKe)->mapWithKeys(function ($item) {
            return [(string) $item['_id'] => $item['SoLuong']];
        });

        // Bước 2: Lấy toàn bộ danh sách XepLoai
        $xepLoaiList = XepLoai::all();

        // Bước 3: Gộp dữ liệu và thêm cột TongCong
        $ketQua = $xepLoaiList->map(function ($xepLoai) use ($thongKeMap, $tongCong) {
            $idStr = (string) $xepLoai->_id;
            return [
                'XepLoaiID'   => $idStr,
                'TenXepLoai'  => $xepLoai->tenXepLoai ?? '',
                'MaXepLoai'   => $xepLoai->maXepLoai ?? '',
                'SoLuong'     => $thongKeMap->get($idStr, 0),
                'TongCong'    => $tongCong,
            ];
        })->sortBy('MaXepLoai')->values();

        return response()->json($ketQua);
    }
    public function LayMaTuTang(Request $request) {
        try {
            $kyhieuLoaiPhieu=$request->input('kyhieuLoaiPhieu');
            $bangDuLieu=$request->input('bangDuLieu');
            $cotDuLieu=$request->input('cotDuLieu');
            // 1) Tra cứu loại chứng từ
            $loai = LoaiChungTu::where('DangSD', true)
                ->where('MaLoaiChungTu', $kyhieuLoaiPhieu)
                ->first();
            // 2) Xây dựng tiền tố/hậu tố
            $user    = Auth::user();
            $donVi   = DonVi::find($user->DonViID);
            $maDonVi = $donVi->MaDonVi ?? '';
            $nienDo  = $request->session()->get('nien_do', '');

            $prefix = str_replace('MaDonVi', $maDonVi, $loai->KyHieuPhiaTruoc);
            $suffix = str_replace('NienDo',   $nienDo,  $loai->KyHieuPhiaSau);

            // 3) Chuẩn bị regex an toàn với delimiter tùy chỉnh (#) và tùy chọn UTF-8
            $delim   = '#';
            $pattern = '^'
                . preg_quote($prefix, $delim)
                . '.*'
                . preg_quote($suffix, $delim)
                . '$';

            // 4) Truy vấn Mongo để tìm các mục đã tồn tại khớp với tiền tố…hậu tố
            $client     = DB::connection('mongodb')->getMongoClient();
            $database   = config('database.connections.mongodb.database');
            $collection = $client->selectCollection($database, $bangDuLieu);

            $cursor = $collection->find([
                $cotDuLieu => [
                    '$regex'   => $pattern,
                    '$options' => 'u',
                ]
            ], [
                'projection' => [$cotDuLieu => 1]
            ]);

            // 5) Trích xuất phần số và theo dõi giá trị lớn nhất
            $maxNum = 0;
            foreach ($cursor as $doc) {
                $val = $doc[$cotDuLieu] ?? '';
                // sử dụng mb_strlen / mb_substr để ký tự đa byte không làm hỏng offset
                $lenVal   = mb_strlen($val);
                $lenPre   = mb_strlen($prefix);
                $lenSuf   = mb_strlen($suffix);
                $inner    = mb_substr($val, $lenPre, $lenVal - $lenPre - $lenSuf);

                if (ctype_digit($inner)) {
                    $maxNum = max($maxNum, (int) $inner);
                }
            }

            // 6) Tính toán số thứ tự tiếp theo, thêm số 0 để đạt độ dài cấu hình
            $next    = $maxNum + 1;
            $padLen  = max($loai->ChieuDaiChuoiTT, mb_strlen((string)$next));
            $numPart = str_pad((string)$next, $padLen, '0', STR_PAD_LEFT);

            // 7) Trả về số chứng từ đã được lắp ráp đầy đủ
            return response()->json([
                'SoChungTu' => $prefix . $numPart . $suffix,
                'KyTuDau' => $prefix,
                'SoTuTang' => $numPart,
            ]);

            return response()->json(['Err' => true]);
        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }
    public function Update_SoVaoSoGocBanSao(Request $request)
    {
        $id = new ObjectId($request->input('id'));
        $kyTuDau = trim($request->input('KyTuDauTien') ?? '');
        $soTuTang = intval($request->input('SoTuTang'));
    
        if (!$kyTuDau || $soTuTang <= 0) {
            return response()->json(['error' => 'Dữ liệu không hợp lệ.'], 400);
        }
    
        $record = SoGocCT::find($id);
        if (!$record) {
            return response()->json(['error' => 'Không tìm thấy bản ghi.'], 404);
        }
    
        // Lặp để tìm SoVaoSoGoc chưa trùng
        do {
            $maSo = $kyTuDau . str_pad($soTuTang, 4, '0', STR_PAD_LEFT); // Ví dụ: ABC0001
            $tontai = SoGocCT::where('SoVaoSoGocBanSao', $maSo)->exists();
    
            if (!$tontai) {
                $record->SoVaoSoGocBanSao = $maSo;
                $record->save();
                return response()->json(['success' => true, 'SoVaoSoGocBanSao' => $maSo, 'SoTuTangKeTiep' => $soTuTang + 1]);
            }
    
            $soTuTang++;
        } while (true); // Dừng khi không trùng và đã update
    }  
    public function Update_GiaoThuHoi(Request $request)
    {
        try {
            $TrangThai =$request->input('TrangThai');
            $Ngay = $request->filled('Ngay') ? Carbon::createFromFormat('d/m/Y', $request->input('Ngay')): null;
            $NhanVienID = $this->safeObjectId($request->input('NhanVienID'));
            $ChucVuID = $this->safeObjectId($request->input('ChucVuID'));
            $NoiDung =$request->input('NoiDung');
            $SoGocID = $this->safeObjectId($request->input('SoGocID'));
        
                // Update
                $SoGoc = SoGoc::find($SoGocID);
                if (!$SoGoc) {
                    return response()->json([
                        'Err' => true,
                        'canhbao' => true,
                        'Msg' => 'Không tìm thấy dữ liệu cần cập nhật!'
                    ]);
                }       
                if($TrangThai=="37"){
                    $SoGoc->update([
                        'TrangThai_Giao' => $TrangThai,
                        'NgayThuHoi' => $Ngay,
                        'NhanVienID_ThuHoi' => $NhanVienID,
                        'ChucVuID_ThuHoi' => $ChucVuID,
                        'NoiDung_ThuHoi' => $NoiDung,
                        'NgayGiao' => null,
                        'NhanVienID_Giao' => "",
                        'ChucVuID_Giao' => "",
                        'NoiDung_Giao' => "",
                    ]);
                    $this->logService->ghiLogs($request->all(), 'sua', auth()->id(), null);
                    return response()->json([
                        'Err' => false,
                        'Msg' => 'Thực hiện giao sổ gốc thành công!',
                    ]);
                }
                else
                {
                    $SoGoc->update([
                        'TrangThai_Giao' => $TrangThai,
                        'NgayGiao' => $Ngay,
                        'NhanVienID_Giao' => $NhanVienID,
                        'ChucVuID_Giao' => $ChucVuID,
                        'NoiDung_Giao' => $NoiDung,

                        'NgayThuHoi' => null,
                        'NhanVienID_ThuHoi' => "",
                        'ChucVuID_ThuHoi' => "",
                        'NoiDung_ThuHoi' => "",
                    ]);
                    $this->logService->ghiLogs($request->all(), 'sua', auth()->id(), null);
                    return response()->json([
                        'Err' => false,
                        'Msg' => 'Thực hiện giao sổ gốc thành công!',
                    ]);
                };
        } catch (\Exception $e) {
            $this->logService->ghiLogs($request->all(), $request->isMethod('put') ? 'sua' : 'them', auth()->id(), null);
            $code = $this->dungChungDb->insertMaCodeLoi($e);
            return response()->json([
                'Err' => true,
                'Msg' => 'Có lỗi xảy ra!',
                'debug' => $e->getMessage(),
            ]);
        }
    }
    public function XuatExcelSoGoc(Request $request)
    {
        try {
            $soGocID = new ObjectId($request->SoGocID);
            $username = auth()->user()->username ?? 'unknown';
            $folder = 'SoGocBanSao';
            $fileName = "DanhSach_SoGocBanSao_{$username}.xlsx";
            $exportFolder = "XuatExcel/{$folder}/{$username}";

            // Đường dẫn thư mục vật lý để lưu file Excel
            $storageExcelPath = storage_path("app/public/{$exportFolder}");

            // Tạo thư mục nếu chưa tồn tại
            if (!file_exists($storageExcelPath)) {
                mkdir($storageExcelPath, 0775, true);
            }

            // Đường dẫn file mẫu Excel
            $templatePath = public_path('ExcelMau/MauSoGocBanSao.xlsx');
            if (!file_exists($templatePath)) {
                return response()->json(['Err' => true, 'Msg' => 'Không tìm thấy file mẫu Excel.']);
            }

            // Load file mẫu
            $spreadsheet = IOFactory::load($templatePath);
            $sheet = $spreadsheet->getActiveSheet();

            // Lấy dữ liệu
            $ds = SoGoc::find($soGocID);
            $qd = QuyetDinh::find($ds->QuyetDinhID);
            $kt = KyThi::find($qd->KyThiID);
            $dsCT = SoGocCT::where('SoGocID', $soGocID)->get();
            $dsHocSinh = DoiTuong::whereIn('_id', $dsCT->pluck('DoiTuongID_HocSinh')->filter())->get()->keyBy('_id');

            $sheet->getPageSetup()->setPrintArea("A1:J500");
            $sheet->getStyle("A1:J500")->getFont()->setName('Times New Roman')->setSize(13);
            // Ghi dữ liệu
            $sheet->setCellValue("A1", mb_strtoupper($ds->CoQuanBanHanh ?? "", 'UTF-8'));
            $sheet->setCellValue("A4", mb_strtoupper("SỔ GỐC CẤP BẢN SAO BẰNG TỐT NGHIỆP " . (optional(CapHoc::find($kt->CapHocID))->tenCapHoc ?? ''), 'UTF-8'));
            $sheet->setCellValue("B6", "Quyết định công nhận tốt nghiệp số {$qd->SoQuyetDinh}, ngày {$qd->NgayKy}");
            $sheet->setCellValue("B7", "Học sinh trường: " . (optional(DonVi::find($ds->DonViID_TruongHoc))->TenDonVi ?? ''));
            $sheet->setCellValue("H6", "Năm tốt nghiệp: " . $ds->NamTotNghiep);
            $sheet->setCellValue("H7", "Hình thức đào tạo: " . (optional(HinhThucDaoTao::find($qd->HinhThucID))->tenHinhThucDaoTao ?? ''));

            $startRow = 10;
            $stt = 1;
            foreach ($dsCT as $ct) {
                $hocSinh = $dsHocSinh[(string)$ct->DoiTuongID_HocSinh] ?? null;

                $sheet->setCellValue("A{$startRow}", $stt);
                $sheet->setCellValue("B{$startRow}", $hocSinh->Hovaten ?? '');
                $sheet->setCellValue("C{$startRow}", optional($hocSinh->Ngaysinh)->format('d/m/Y') ?? '');
                $sheet->setCellValue("D{$startRow}", $hocSinh->Noisinh ?? '');
                $sheet->setCellValue("E{$startRow}", optional(GioiTinh::find($hocSinh->Gioitinh))->TenGioiTinh ?? '');
                $sheet->setCellValue("F{$startRow}", optional(DanToc::find($hocSinh->DanTocID))->tenDanToc ?? '');
                $sheet->setCellValue("G{$startRow}", optional(XepLoai::find($ct->XepLoaiID))->tenXepLoai ?? '');
                $sheet->setCellValue("H{$startRow}", $ct->SoVaoSoGoc);
                $sheet->setCellValue("I{$startRow}", $ct->SoVaoSoGocBanSao);
                $sheet->setCellValue("J{$startRow}", $ct->GhiChu);
                // 👉 THÊM VIỀN KẺ SAU MỖI DÒNG
                $sheet->getStyle("A{$startRow}:J{$startRow}")->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

                $startRow++;
                $stt++;
            }

            // Căn giữa dòng cuối (optional)
            $footerRow = $startRow + 5;
            $sheet->getStyle("A{$footerRow}:E{$footerRow}")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

            // Ghi file Excel
            $filePath = "{$storageExcelPath}/{$fileName}";
            $writer = IOFactory::createWriter($spreadsheet, 'Xlsx');
            $writer->save($filePath);

            // Gọi hàm chuyển sang PDF
            $relativeExcelPath = "storage/{$exportFolder}/{$fileName}"; // dùng public_path()
            $pdfSuccess = \App\Services\OfficeConvertService::XlsxToPdf($relativeExcelPath, 'L');

            $returnPath = $pdfSuccess
                ? str_replace('.xlsx', '.pdf', $relativeExcelPath)
                : $relativeExcelPath;

            return response()->json([
                'Err' => false,
                'Result' => $returnPath,
                'canhbao' => !$pdfSuccess,
                'Msg' => $pdfSuccess ? null : 'Không thể chuyển sang PDF, trả lại file Excel.',
            ]);
        } catch (\Exception $e) {
            Log::error('XuatExcelSoGocBanSao error: ' . $e->getMessage());
            return response()->json([
                'Err' => true,
                'Msg' => 'Có lỗi xảy ra!',
                'debug' => $e->getMessage(),
            ]);
        }
    }
    public function GetListDonViCoHocSinh(Request $request)
    {
        try {
            $QuyetDinhID = $request->input('QuyetDinhID');

            // Bước 1: Lấy danh sách HocSinhID từ bảng HocSinhTN (theo QuyetDinhID)
            $quyetDinhID = new ObjectId($request->input('QuyetDinhID'));
            $capBangIDs = CapBangTotNghiep::where('QuyetDinhID', $quyetDinhID)->where('BanSao', true)
                ->pluck('_id')
                ->toArray();
                $hocSinhIDs = CapBangTotNghiepCT::whereIn('CapBangTotNghiepID', $capBangIDs)
                ->raw(function ($collection) {
                    return $collection->distinct('HocSinhID', [
                        'HocSinhID' => ['$ne' => null]
                    ]);
                });

            // Bước 2: Nếu có SoGocID, loại bỏ các học sinh đã có trong SoGocCT
            if ($request->filled('SoGocID')) {

                $soGocID = new ObjectId($request->SoGocID);

                $hocSinhDaCo = SoGocCT::where('SoGocID', $soGocID)
                    ->pluck('DoiTuongID_HocSinh')
                    ->toArray();

                // Loại bỏ những học sinh đã có
                //$hocSinhIDs = array_values(array_diff($hocSinhIDs, $hocSinhDaCo));
            }

            // Bước 3: Truy vấn DoiTuong theo danh sách HocSinhID sau khi loại trừ
            $doiTuongList = DoiTuong::whereIn('_id', $hocSinhIDs)->get();

            $grouped = $doiTuongList->groupBy(fn($item) => (string) $item->DonViID_Hoc);    

            // // Bước 4: Lấy danh sách DonViID_Hoc từ DoiTuong
            // $donViIDs = $doiTuongList
            //     ->pluck('DonViID_Hoc')
            //     ->filter(fn($id) => !empty($id))
            //     ->unique()
            //     ->values();

            // // Nếu không có đơn vị nào thì trả về rỗng
            // if ($donViIDs->isEmpty()) {
            //     return response()->json([
            //         'Err' => false,
            //         'CanhBao' => true,
            //         'Result' => [],
            //         'Msg' => 'Không có trường học nào có học sinh tốt nghiệp.',
            //     ]);
            // }

            // Bước 3: Truy vấn DonVi từ danh sách DonViID_Hoc
            $ds = DonVi::whereIn('_id', SoGoc::where('_id', $soGocID)
                    ->pluck('DonViID_TruongHoc')
                    ->toArray())->orderBy('TenDonVi')->get();

            // Bước 4: Map kết quả
           $result = $ds->map(function ($dv) use ($grouped, $hocSinhDaCo) {
                 $dsHocSinh = $grouped->get((string) $dv->_id, collect());

                $tong = $dsHocSinh->count();
                $daChon = $dsHocSinh->filter(fn($hs) => in_array((string) $hs->_id, $hocSinhDaCo))->count();
                return [
                    'id' => (string) $dv->_id,
                    'code' => $dv->MaDonVi ?? '',
                    'name' => $dv->TenDonVi ?? 'Chưa đặt tên',
                    'count' => $tong,
                    'daChonCount' => $daChon,
                ];
            });

            return response()->json([
                'Err' => false,
                'Result' => $result,
            ]);
        } catch (\Throwable $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Đã xảy ra lỗi khi lấy danh sách đơn vị.',
                'Debug' => $e->getMessage(),
            ], 500);
        }
    }
    /**
     * Lấy danh sách học sinh thuộc một trường, và đã có trong bảng HocSinhTN
     */
        public function GetListHocSinhByDonVi(Request $request)
    {
        try {
            $QuyetDinhID = $request->input('QuyetDinhID');
            $donViID = $request->input('DonViID');

            // if (empty($donViID)) {
            //     return response()->json([
            //         'Err' => true,
            //         'Msg' => 'Thiếu DonViID.',
            //     ]);
            // }

            // Bước 1: Lấy danh sách CapBangTotNghiep theo Quyết định
            $capBangIDs = CapBangTotNghiep::where('QuyetDinhID', new ObjectId($QuyetDinhID))->where('BanSao', true)
                ->pluck('_id')
                ->toArray();

            // Bước 2: Lấy danh sách HocSinhID từ CapBangTotNghiepCT
            $hocSinhIDs = CapBangTotNghiepCT::whereIn('CapBangTotNghiepID', $capBangIDs)
                ->raw(function ($collection) {
                    return $collection->distinct('HocSinhID', [
                        'HocSinhID' => ['$ne' => null]
                    ]);
                });
            $soGocID = new ObjectId($request->SoGocID);
            $hocSinhDaCo = SoGocCT::where('SoGocID', $soGocID)
                    ->pluck('DoiTuongID_HocSinh')
                    ->toArray();

            // Loại bỏ những học sinh đã có
            $hocSinhIDs = array_values(array_diff($hocSinhIDs, $hocSinhDaCo));
            // Bước 3: Truy vấn DoiTuong
            $query = DoiTuong::where('DonViID_Hoc', new ObjectId($donViID))
                ->whereIn('_id', $hocSinhIDs);

            $sortField = $request->input('CbSapXep', 'MaDoiTuong');
            $query->orderBy($sortField, 'asc');

            $result = $query->get();

            // Nếu cần map thêm gì từ CapBangTotNghiepCT thì cần load lại danh sách chi tiết
            $capBangCTMap = CapBangTotNghiepCT::whereIn('HocSinhID', $hocSinhIDs)->get()->keyBy('HocSinhID');
            $result = $result->map(function ($item) use ($capBangCTMap) {
                $item->KetQuaTN = $capBangCTMap[$item->_id]->XepLoaiID ?? null;
                 $item->SoHieu = $capBangCTMap[$item->_id]->SoHieu ?? null;
                 $item->SoVaoSo = $capBangCTMap[$item->_id]->SoVaoSo ?? null;
                return $item;
            });
            //$QuyetDinhCTMap = HocSinhTN::whereIn('HocSinhID', $hocSinhIDs)->get()->keyBy('HocSinhID');
            //$result = $result->map(function ($item) use ($QuyetDinhCTMap) {
                //$item->KetQuaTN = $QuyetDinhCTMap[$item->_id]->XepLoaiID ?? null;
                //return $item;
            //});
            // Format lại nếu bạn dùng hàm formatDoiTuongs
            $result = $this->formatDoiTuongs($result);

            return response()->json([
                'Err' => false,
                'result' => $result,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Không thể lấy danh sách!',
                'debug' => $e->getMessage(),
            ]);
        }
    }
}