<?php

namespace App\Http\Controllers\QuanLy;

use Str;
use Storage;
use DateTime;
use Carbon\Carbon;
use MongoDB\BSON\Regex;
use App\Services\ThongBao;
use Illuminate\Http\Request;
use App\Models\DanhMuc\DonVi;
use App\Services\DungChungDb;
use App\Models\DanhMuc\CapHoc;
use App\Models\DanhMuc\DanToc;
use App\Models\QuanLy\SoGocCT;
use PhpOffice\PhpWord\PhpWord;
use App\Models\DanhMuc\TieuChi;
use App\Models\DanhMuc\XepLoai;
use App\Models\QuanLy\DoiTuong;
use App\Services\DungChungNoDb;
use App\Models\DanhMuc\GioiTinh;
use App\Models\DanhMuc\NhanVien;
use App\Models\QuanLy\QuyetDinh;
use PhpOffice\PhpWord\Style\Font;
use PhpOffice\PhpWord\Shared\Html;
use App\Http\Controllers\Controller;
use App\Services\CurrentUserService;
use Illuminate\Support\Facades\Auth;
use PhpOffice\PhpWord\SimpleType\Jc;
use App\Services\OfficeConvertService;
use Illuminate\Support\Facades\DB;

use App\Models\DanhMuc\TrangThai;
use Illuminate\Support\Facades\Log;
use App\Models\DanhMuc\DiaBanHanhChinh;
use App\Models\HeThong\ThietLapHeThong;
use App\Models\QuanLy\CapBangTotNghiep;
use App\Models\QuanLy\TiepNhanPhoiVBCC;
use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\TemplateProcessor;
use App\Models\QuanLy\CapBangTotNghiepCT;
use App\Models\QuanLy\QuyetDinhChinhSuaVanBang;
use PhpOffice\PhpWord\SimpleType\JcTable;
use App\Models\DanhMuc\MauVanBangChungChi;
use App\Models\DanhMuc\MauVanBangChungChiCT;
use App\Models\DanhMuc\LoaiPhoiVanBangChungChi;
use MongoDB\BSON\ObjectId;
use App\Models\DanhMuc\ChucVu;
use App\Models\QuanLy\NhatKyThaoTac;
use App\Models\quanly\ThaoTac;
use App\Models\quanly\YeuCauCSNDVBCC;
use App\Models\DanhMuc\HinhThucNhanPhoi;
use App\Models\DanhMuc\DonViTinh;

class QuyetDinhChinhSuaVanBangController extends Controller
{
    //
    protected DungChungDb $dungChungDb;
    protected DungChungNoDb $logService;
    protected CurrentUserService $currentUser;

    public function __construct(DungChungDb $dungChungDb,CurrentUserService $currentUser, DungChungNoDb $logService)
    {
        $this->dungChungDb = $dungChungDb;
        $this->logService  = $logService;
        $this->currentUser  = $currentUser;
    }
    function safeObjectId($id) {
        return is_string($id) && preg_match('/^[a-f\d]{24}$/i', $id) ? new ObjectId($id) : null;
    }

    protected function safeObjectId_P($id)
    {
        if (is_array($id)) {
            return array_map(function ($value) {
                return is_string($value) && preg_match('/^[a-f\d]{24}$/i', $value) ? new ObjectId($value) : null;
            }, $id);
        }
        return is_string($id) && preg_match('/^[a-f\d]{24}$/i', $id) ? new ObjectId($id) : null;
    }

    public function index()
    {
        return view('quanly.QuyetDinhChinhSuaVanBang.index' );
    }

    public function getDanhSachTruongHoc_DemSoLuong(Request $request)
    {
        // 1) Extract BangCha / BangCon from JSON payload
        $bangCha = $request->input('BangCha', []);
        $bangCon = $request->input('BangCon', []);

        // Validation: Chỉ kiểm tra BangCon nếu có BangCha['ID'] hợp lệ
        if (!empty($bangCha['ID']) && (
            empty($bangCon['TenCollection']) ||
            empty($bangCon['TenCotReferenceCha']) ||
            empty($bangCon['TenCotReferenceDoiTuong'])
        )) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Missing BangCon parameters when BangCha ID is provided',
            ], 400);
        }

        try {
            $chaId = !empty($bangCha['ID']) && $bangCha['ID'] !== "" ? new ObjectId($bangCha['ID']) : null;

            $conColl = $bangCon['TenCollection'] ?? null;
            $conChaRef = $bangCon['TenCotReferenceCha'] ?? null;
            $conObjRef = $bangCon['TenCotReferenceDoiTuong'] ?? null;

            // 2) Nếu không có BangCha['ID'] hoặc là chuỗi rỗng, lấy tất cả DonVi
            if (empty($bangCha['ID']) || $bangCha['ID'] === "") {
                $donViList = DonVi::where('TrangThai', true)
                    ->orderBy('MaDonVi')
                    ->get()
                    ->map(fn($dv) => [
                        'id' => (string) $dv->_id,
                        'code' => $dv->MaDonVi,
                        'name' => $dv->TenDonVi,
                        'count' => 0, // Mặc định 0 nếu không lọc học sinh
                        'daChonCount' => 0,
                    ]);

                return response()->json([
                    'Err' => false,
                    'Result' => $donViList,
                ]);
            }

            // 3) Nếu có BangCha['ID'], lấy DonVi dựa trên ID cha
            // Tìm tất cả DonViID_Hoc liên quan đến đơn vị cha
            $donViIds = DoiTuong::where('TrangThai', true)
                ->where('DonViID_Hoc', $chaId) // Lọc theo DonViID_Hoc khớp với chaId
                ->pluck('DonViID_Hoc')
                ->filter()
                ->map(fn($v) => new ObjectId($v))
                ->values()
                ->unique();

            if ($donViIds->isEmpty()) {
                return response()->json(['Err' => false, 'Result' => []]);
            }

            // 4) Find which students have already been chosen in BangCon
            $chosenIds = DB::connection('mongodb')
                ->table($conColl)
                ->where($conChaRef, $chaId)
                ->pluck($conObjRef)
                ->map(fn($id) => new ObjectId($id))
                ->toArray();

            // 5) Aggregate: total count and chosen count per DonViID_Hoc
            $agg = DoiTuong::raw(function ($collection) use ($donViIds, $chosenIds) {
                return $collection->aggregate([
                    [
                        '$match' => [
                            'TrangThai' => true,
                            'DonViID_Hoc' => ['$in' => $donViIds->toArray()],
                        ]
                    ],
                    [
                        '$project' => [
                            'DonViID_Hoc' => 1,
                            'isChosen' => [
                                '$cond' => [
                                    ['$in' => ['$_id', $chosenIds]],
                                    1,
                                    0
                                ]
                            ],
                        ],
                    ],
                    [
                        '$group' => [
                            '_id' => '$DonViID_Hoc',
                            'count' => ['$sum' => 1],
                            'daChonCount' => ['$sum' => '$isChosen'],
                        ],
                    ],
                ]);
            });

            // 6) Turn cursor into a lookup map
            $stats = collect(iterator_to_array($agg))
                ->mapWithKeys(fn($item) => [
                    (string) $item->_id => [
                        'count' => $item->count,
                        'daChonCount' => $item->daChonCount,
                    ]
                ]);

            // 7) Load DonVi and merge in our stats
            $donViList = DonVi::whereIn('_id', $donViIds)
                ->where('TrangThai', true)
                ->orderBy('MaDonVi')
                ->get()
                ->map(fn($dv) => [
                    'id' => (string) $dv->_id,
                    'code' => $dv->MaDonVi,
                    'name' => $dv->TenDonVi,
                    'count' => $stats->get((string) $dv->_id, [])['count'] ?? 0,
                    'daChonCount' => $stats->get((string) $dv->_id, [])['daChonCount'] ?? 0,
                ]);

            return response()->json([
                'Err' => false,
                'Result' => $donViList,
            ]);

        } catch (Exception $e) {
            $code = $this->dungChungDb->insertMaCodeLoi($e);
            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }
    /**
     * Thêm mới hoặc cập nhật thông tin đia bàn
     */
    public function luuthongtin(Request $request)
{
    $msg = '';
    $QuyetDinhChinhSuaVanBang = null;
    $didLogAction = false;
    $thaoTac = '';
    $now = now();
    try {
        // Lấy input
        $data = [
            'SoQuyetDinh' => $request->input('SoQuyetDinh'),
            'NgayKy' => $request->filled('NgayKy') ? Carbon::createFromFormat('d/m/Y', $request->input('NgayKy')) : null,
            'NgayCap' => $request->filled('NgayCap') ? Carbon::createFromFormat('d/m/Y', $request->input('NgayCap')) : null,
            'NguoiKyID' => $this->safeObjectId($request->input('NguoiKyID')),
            'ChucVuID_NK' => $this->safeObjectId($request->input('ChucVuID')),
            'HocSinhID' => $this->safeObjectId($request->input('HocSinhID')),
            'SoHieu' => $request->input('SoHieu'),
            'SoVaoSo' => $request->input('SoVaoSo'),
            'NoiDungChinhSua' => $request->input('NoiDungChinhSua'),
            'CoQuanBanHanh' => $request->input('CoQuanBanHanh'),
            'LyDoChinhSua' => $request->input('LyDoChinhSua'),
            'TrichYeu' => $request->input('TrichYeu'),
            'QuyetDinhChinhSuaVanBangID' => $this->safeObjectId($request->input('QuyetDinhChinhSuaVanBangID')),
            'DonYeuCauCSVBCCID' => $this->safeObjectId($request->input('DonYeuCauCSVBCCID')),
            'TrangThai' => $request->input('TrangThai'),
            'UserID_ThaoTac' => $this->safeObjectId(auth()->id()),
            'UserID_CapNhat' => $this->safeObjectId(auth()->id()),
            'DonViID_CapNhat' => $this->safeObjectId($this->currentUser->donViId()),
            'DonViID_ThaoTac' => $this->safeObjectId($this->currentUser->donViId()),
            'NgayThaoTac' => $now,
            'NgayCapNhat' => $now,
        ];

        $txtDuongDanFileVB = $request->input('txtDuongDanFileVB') ?? '';
        $paths = $this->saveFiles($txtDuongDanFileVB);
        $data['DinhKem'] = $paths;

        // Xử lý POST (create)
        if ($request->isMethod('post')) {
            $didLogAction = true;
            $thaoTac = 'Tạo mới Quyết định chỉnh sửa văn bằng';

            $QuyetDinhChinhSuaVanBang = QuyetDinhChinhSuaVanBang::create($data);
            $QuyetDinhChinhSuaVanBang = QuyetDinhChinhSuaVanBang::find($QuyetDinhChinhSuaVanBang->_id);

            $this->logService->ghiLogs($request->all(), 'them', auth()->id(), null);

            return response()->json(['Err' => false, 'Msg' => 'Thêm dữ liệu thành công!']);
        }

        // Xử lý PUT (update)
        if ($request->isMethod('put') || $request->isMethod('patch')) {
            $thaoTac = 'Cập nhật Quyết định chỉnh sửa văn bằng';
            $QuyetDinhChinhSuaVanBang = QuyetDinhChinhSuaVanBang::find($data['QuyetDinhChinhSuaVanBangID']);

            if (!$QuyetDinhChinhSuaVanBang) {
                return response()->json([
                    'Err' => true,
                    'canhbao' => true,
                    'Msg' => 'Không tìm thấy dữ liệu cần cập nhật!'
                ]);
            }

            // Xoá file cũ nếu có
            if (!empty($QuyetDinhChinhSuaVanBang->DinhKem)) {
                foreach (explode('|', $QuyetDinhChinhSuaVanBang->DinhKem) as $path) {
                    $relativePath = str_replace('/storage/', 'public/', $path);
                    if (Storage::exists($relativePath)) {
                        Storage::delete($relativePath);
                    }
                }
            }

            $QuyetDinhChinhSuaVanBang->update($data);
            $didLogAction = count($QuyetDinhChinhSuaVanBang->getChanges()) > 0;

            $this->logService->ghiLogs($request->all(), 'sua', auth()->id(), null);

            return response()->json(['Err' => false, 'Msg' => 'Cập nhật dữ liệu thành công!']);
        }

        // Nếu không phải POST hoặc PUT
        return response()->json(['Err' => true, 'canhbao' => true, 'Msg' => 'Phương thức HTTP không hợp lệ!']);

    } catch (\Exception $e) {
        $this->logService->ghiLogs($request->all(), $request->isMethod('put') ? 'sua' : 'them', auth()->id(), null);
        $this->dungChungDb->insertMaCodeLoi($e);

        return response()->json(['Err' => true, 'Msg' => 'Có lỗi xảy ra!', 'debug' => $e->getMessage()]);
    } finally {
        // Ghi nhật ký thao tác
        if ($didLogAction && $QuyetDinhChinhSuaVanBang) {
            try {
                $NguoiKyID = $this->safeObjectId($request->input('NguoiKyID'));
                $ChucVuID = $this->safeObjectId($request->input('ChucVuID'));
                $TenNhanVien = NhanVien::where('_id', $NguoiKyID)->value('tenNhanVien') ?? '';
                $TenChucVu = ChucVu::where('_id', $ChucVuID)->value('tenChucVu') ?? '';
                $method = $request->isMethod('post') ? 'Thêm' : 'Sửa';

                $noiDungChiTiet = sprintf(
                    '%s quyết định chỉnh sửa văn bằng: %s',
                    $thaoTac,
                    $data['SoQuyetDinh'] ?? ''
                );

                $duLieuThaoTac = ThaoTac::getFirstByThaoTac($method, (string) $this->currentUser->id());
                NhatKyThaoTac::luuNhatKyThaoTac(
                    $duLieuThaoTac['MaThaoTac'],
                    $duLieuThaoTac['ThaoTac'],
                    $duLieuThaoTac['MauSac'],
                    $thaoTac,
                    $noiDungChiTiet,
                    (string) $QuyetDinhChinhSuaVanBang->_id, // ép ObjectId thành string
                    $QuyetDinhChinhSuaVanBang->getTable(),
                    url()->current(),
                    $TenNhanVien,
                    $TenChucVu,
                    (string) $this->currentUser->id(),
                    (string) $this->currentUser->donViId()
                );
            } catch (\Throwable $t) {
                \Log::error('Ghi NhatKyThaoTac thất bại: ' . $t->getMessage());
            }
        }
    }
}



    protected function saveFiles(string $paths): string
    {
        $donViid  = $this->currentUser->donViId();
        $username = $this->currentUser->username();
        $disk     = Storage::disk('public');

        $permanentBase = "uploads/{$donViid}/{$username}/files/QuyetDinhChinhSuaVanBang";
        $results       = [];

        foreach (explode('|', $paths) as $p) {
            $p = trim($p);
            if (!$p) {
                continue;
            }

            // grab only the path portion (e.g. "/storage/uploads/…")
            $urlPath = parse_url($p, PHP_URL_PATH);

            // if it already lives under our permanent folder, keep it
            if (Str::startsWith($urlPath, "/storage/{$permanentBase}/")) {
                $results[] = $urlPath;
                continue;
            }

            // otherwise treat it as a "temp" file under /storage/…
            // strip leading "/" and "storage/" to get the disk key
            $diskKey = preg_replace('#^/storage/#', '', $urlPath);

            // build a new filename in the permanent folder
            $name     = pathinfo($diskKey, PATHINFO_FILENAME);
            $ext      = pathinfo($diskKey, PATHINFO_EXTENSION);
            $slug     = Str::slug($name);
            $newName  = "{$slug}-" . time() . ".{$ext}";
            $newKey   = "{$permanentBase}/{$newName}";

            // move on the 'public' disk (move = copy + delete original)
            $disk->move($diskKey, $newKey);

            // push the new public URL
            $results[] = '/storage/' . $newKey;
        }

        // re‐implode with '|'
        return implode('|', $results);
    }

    /**
     * Tải dữ liệu cho form sửa
     */
    public function loadDuLieuSua(Request $request)
    {
        $id = $request->input('id');
        $QuyetDinhChinhSuaVanBang = QuyetDinhChinhSuaVanBang::find($id);
        if (!$QuyetDinhChinhSuaVanBang) {
            return response()->json([
                'Err' => true,
                'canhbao' => true,
                'Msg' => 'Không tìm thấy dữ liệu!'
            ]);
        }
        $QuyetDinhChinhSuaVanBang = $this->formatQuyetDinhChinhSuaVanBangs(collect([$QuyetDinhChinhSuaVanBang]))->first(); // Map 1 item rồi lấy lại object
        return response()->json([
            'Err'    => false,
            'Result' => $QuyetDinhChinhSuaVanBang
        ]);
    }


// public function formatQuyetDinhChinhSuaVanBangs($collection)
// {
//     return $collection->map(function ($item) {
//         $doiTuong = DoiTuong::find($item->HocSinhID); // Lưu kết quả DoiTuong
//         $trangThai = TrangThai::where('MaTrangThai', $item->TrangThai)->first(); // Tìm theo MaTrangThai
//         $item->txtNgayCap = optional($item->NgayCap)->format('d/m/Y') ?? '';
//         $item->txtNgayKy = optional($item->NgayKy)->format('d/m/Y') ?? '';
//         $item->txtNgayBanHanh = optional($item->NgayBanHanh)->format('d/m/Y') ?? '';
//         $item->TenNguoiKy = optional(NhanVien::find($item->NguoiKyID))->tenNhanVien ?? '';
//         $item->TenNguoiBanHanh = optional(NhanVien::find($item->NguoiBanHanhID))->tenNhanVien ?? '';
//         $item->TenChucVu = optional(ChucVu::find($item->ChucVuID_NK))->tenChucVu ?? '';
//         $item->TenChucVuNBH = optional(ChucVu::find($item->ChucVuID_NBH))->tenChucVu ?? '';
//         $item->TenHocSinh = optional($doiTuong)->Hovaten ?? '';
//         $item->MaDoiTuong = optional($doiTuong)->MaDoiTuong ?? '';
//         $item->MauSacTT = optional($trangThai)->MauSac ?? '';
//         $item->TenTrangThai = optional($trangThai)->TenTrangThai ?? '';
//         $item->txtNgaySinh = optional($doiTuong)->Ngaysinh->format('d/m/Y') ?? '';
//         $item->TenTruong = optional(DonVi::find($doiTuong ? $doiTuong->DonViID_Hoc : null))->TenDonVi ?? '';

//         return $item;
//     });
// }
public function formatQuyetDinhChinhSuaVanBangs($collection)
{
    return $collection->map(function ($item) {
        \Log::info('Processing item:', ['item' => $item->toArray()]);
        $doiTuong = DoiTuong::find($item->HocSinhID);
        \Log::info('DoiTuong result:', ['HocSinhID' => $item->HocSinhID, 'doiTuong' => $doiTuong]);
        $trangThai = TrangThai::where('MaTrangThai', $item->TrangThai)->first();
        \Log::info('TrangThai result:', ['MaTrangThai' => $item->TrangThai, 'trangThai' => $trangThai]);

        $item->txtNgayCap = optional($item->NgayCap)->format('d/m/Y') ?? '';
        $item->txtNgayKy = optional($item->NgayKy)->format('d/m/Y') ?? '';
        $item->txtNgayBanHanh = optional($item->NgayBanHanh)->format('d/m/Y') ?? '';
        $item->TenNguoiKy = optional(NhanVien::find($item->NguoiKyID))->tenNhanVien ?? '';
        $item->TenNguoiBanHanh = optional(NhanVien::find($item->NguoiBanHanhID))->tenNhanVien ?? '';
        $item->TenChucVu = optional(ChucVu::find($item->ChucVuID_NK))->tenChucVu ?? '';
        $item->TenChucVuNBH = optional(ChucVu::find($item->ChucVuID_NBH))->tenChucVu ?? '';
        $item->TenHocSinh = optional($doiTuong)->Hovaten ?? '';
        $item->MaDoiTuong = optional($doiTuong)->MaDoiTuong ?? '';
        $item->MauSacTT = optional($trangThai)->MauSac ?? '';
        $item->TenTrangThai = optional($trangThai)->TenTrangThai ?? '';
        $item->txtNgaySinh = optional($doiTuong)->Ngaysinh->format('d/m/Y') ?? '';
        $item->TenTruong = optional(DonVi::find($doiTuong ? $doiTuong->DonViID_Hoc : null))->TenDonVi ?? '';

        \Log::info('Formatted item:', ['item' => $item->toArray()]);
        return $item;
    });
}
        /**
     * Xóa
     */
    public function xoa(Request $request)
    {
        try {
            $id = $request->input('ma');
            $model = QuyetDinhChinhSuaVanBang::whereKey($id)->firstOrFail();

            // ✅ Xóa file đính kèm nếu có
            if (!empty($model->DinhKem)) {
                $paths = [];

                // Xử lý chuỗi đường dẫn file (dùng | hoặc , làm dấu ngăn cách)
                if (is_string($model->DinhKem)) {
                    $paths = preg_split('/[|,]/', $model->DinhKem);
                }

                foreach ($paths as $path) {
                    $cleanPath = str_replace('\\', '/', trim($path));

                    // Loại bỏ '/storage/' => còn lại: uploads/...
                    $relativePath = str_replace('/storage/', '', $cleanPath);

                    // Laravel file system path: storage/app/public/...
                    $fullStoragePath = 'public/' . $relativePath;
                    if (Storage::disk('public')->exists($relativePath)) {
                        Storage::disk('public')->delete($relativePath);
                    }
                    else {
                        Log::warning("Không tìm thấy file để xoá: $fullStoragePath");
                    }
                }
            }

            // ✅ Xoá dữ liệu gốc
            $model->delete();

            return response()->json([
                'Err' => false,
                'Msg' => 'Xóa dữ liệu và file đính kèm thành công!'
            ]);

        } catch (\Exception $e) {
            $code = $this->dungChungDb->insertMaCodeLoi($e);

            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }


public function getAll(Request $request)
{
    try {
        $query = QuyetDinhChinhSuaVanBang::query();

        // Lọc theo ngày
        if ($request->filled('TuNgay_Loc')) {
            $tuNgay = Carbon::createFromFormat('d/m/Y', $request->input('TuNgay_Loc'))->startOfDay();
            $query->where('NgayCap', '>=', $tuNgay);
        }

        if ($request->filled('DenNgay_Loc')) {
            $denNgay = Carbon::createFromFormat('d/m/Y', $request->input('DenNgay_Loc'))->endOfDay();
            $query->where('NgayCap', '<=', $denNgay);
        }

        if ($request->filled('TrangThaiID_Loc')) {
            $query->where('TrangThai', (string)$request->input('TrangThaiID_Loc'));
        }

         if ($request->filled('LoaiPhoiID_Loc')) {
            $loaiPhoiId = $this->safeObjectId($request->input('LoaiPhoiID_Loc'));
            \Log::info('loaiPhoiId: ' . (string)$loaiPhoiId);

            if ($loaiPhoiId) {
                $donYeuCauIds = YeuCauCSNDVBCC::where('LoaiVBCCID', $loaiPhoiId)
                    ->pluck('id')
                    ->toArray();
                \Log::info('donYeuCauIds (raw): ' . json_encode($donYeuCauIds));

                $donYeuCauIds = array_map(function ($id) {
                    return new \MongoDB\BSON\ObjectId($id);
                }, $donYeuCauIds);
                \Log::info('donYeuCauIds (ObjectId): ' . json_encode(array_map('strval', $donYeuCauIds)));

                if (!empty($donYeuCauIds)) {
                    $query->whereIn('DonYeuCauCSVBCCID', $donYeuCauIds);
                    \Log::info('Applied whereIn with DonYeuCauCSVBCCID: ' . json_encode(array_map('strval', $donYeuCauIds)));
                } else {
                    \Log::info('donYeuCauIds is empty after filter, returning empty result');
                    return response()->json([
                        'Err'    => false,
                        'result' => [],
                    ]);
                }
            } else {
                \Log::info('loaiPhoiId is null or invalid');
            }
        }

        // Tìm kiếm theo SearchKey (chỉ trong bảng chính)
        if ($request->filled('SearchKey')) {
            $keyword = $request->input('SearchKey');
            $regex = new \MongoDB\BSON\Regex($keyword, 'i'); // không phân biệt hoa thường

            $query->where(function ($q) use ($regex) {
                $q->orWhere('SoQuyetDinh', $regex);
                $q->orWhere('TenHocSinh', $regex);
                $q->orWhere('TenTruong', $regex);
                $q->orWhere('SoVaoSo', $regex);
                $q->orWhere('SoHieu', $regex);
            });
        }
        // Sắp xếp
        $sortField = $request->input('CbSapXep', 'SoQuyetDinh');
        $query->orderBy($sortField, 'asc');

        // Lấy dữ liệu
        $result = $query->get();
        $result = $this->formatQuyetDinhChinhSuaVanBangs($result);

        return response()->json([
            'Err'    => false,
            'result' => $result,
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'Err'   => true,
            'Msg'   => 'Không thể lấy danh sách!',
            'debug' => $e->getMessage(),
        ]);
    }
}

    public function gettrangthai(Request $request)
    {
        try {
            $query = TrangThai::select('_id', 'MaTrangThai', 'TenTrangThai')
                ->where('TrangThai', true)
                ->whereIn('MaTrangThai', ['20', '22', '21'])
                ->orderBy('MaTrangThai', 'asc');
            $result = $query->get();

            $payload = [
                'Err' => false,
                'Result' => $result,
                'Msg' => '',
            ];
            $json = json_encode($payload);
            // return it as a JSON response
            return response($json, 200)
                ->header('Content-Type', 'application/json');
        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Không thể lấy danh sách!',
                'debug' => $e->getMessage(),
            ]);
        }
    }
    /**
     * Ban hành quyết định
     */

public function BanHanhQD(Request $request)
{
    try {
        $NoiDungBanHanh = $request->input('NoiDungBanHanh');
        $ChucVuID_NBH = $this->safeObjectId($request->input('ChucVuID_NBH'));
        $NgayBanHanh = $request->filled('NgayBanHanh')
            ? Carbon::createFromFormat('d/m/Y', $request->input('NgayBanHanh'))
            : null;
        $NguoiBanHanhID = $this->safeObjectId($request->input('NguoiBanHanhID'));
        $TrangThai = $request->input('TrangThai');
        $QuyetDinhChinhSuaVanBangID = $this->safeObjectId($request->input('QuyetDinhChinhSuaVanBangID'));
        $donViId = $this->safeObjectId($this->currentUser->donViId());
        $userId = $this->safeObjectId(auth()->id());
        $now = now();

        if ($request->isMethod('put') || $request->isMethod('patch')) {
            // Update
            $QuyetDinhChinhSuaVanBang = QuyetDinhChinhSuaVanBang::find($QuyetDinhChinhSuaVanBangID);
            if (!$QuyetDinhChinhSuaVanBang) {
                return response()->json([
                    'Err' => true,
                    'canhbao' => true,
                    'Msg' => 'Không tìm thấy dữ liệu cần cập nhật!'
                ], 404);
            }

            $QuyetDinhChinhSuaVanBang->update([
                'NoiDungBanHanh' => $NoiDungBanHanh,
                'ChucVuID_NBH' => $ChucVuID_NBH,
                'NgayBanHanh' => $NgayBanHanh,
                'NguoiBanHanhID' => $NguoiBanHanhID,
                'TrangThai' => $TrangThai,
                'UserID_ThaoTac' => $userId,
                'UserID_CapNhat' => $userId,
                'NgayThaoTac' => $now,
                'NgayCapNhat' => $now,
                'DonViID_CapNhat' => $donViId,
                'DonViID_ThaoTac' => $donViId,
            ]);

            $this->logService->ghiLogs($request->all(), 'sua', auth()->id(), null);

            // === Ghi nhật ký thao tác ===
            try {
                $TenNhanVien = NhanVien::where('_id', $NguoiBanHanhID)->value('tenNhanVien') ?? '';
                $TenChucVu = ChucVu::where('_id', $ChucVuID_NBH)->value('tenChucVu') ?? '';
                $thaoTac = 'Ban hành quyết định chỉnh sửa văn bằng';
                $noiDungChiTiet = sprintf('%s: %s', $thaoTac, $QuyetDinhChinhSuaVanBang->SoQuyetDinh ?? '');

                $duLieuThaoTac = ThaoTac::getFirstByThaoTac('Sửa', (string) $this->currentUser->id());
                NhatKyThaoTac::luuNhatKyThaoTac(
                    $duLieuThaoTac['MaThaoTac'],
                    $duLieuThaoTac['ThaoTac'],
                    $duLieuThaoTac['MauSac'],
                    $thaoTac,
                    $noiDungChiTiet,
                    (string) $QuyetDinhChinhSuaVanBang->_id,
                    $QuyetDinhChinhSuaVanBang->getTable(),
                    url()->current(),
                    $TenNhanVien,
                    $TenChucVu,
                    (string) $this->currentUser->id(),
                    (string) $this->currentUser->donViId()
                );
            } catch (\Throwable $t) {
                \Log::error('Ghi NhatKyThaoTac thất bại: ' . $t->getMessage());
            }

            return response()->json([
                'Err' => false,
                'Msg' => 'Cập nhật dữ liệu thành công!'
            ], 200);
        }

        return response()->json([
            'Err' => true,
            'canhbao' => true,
            'Msg' => 'Phương thức HTTP không hợp lệ!'
        ], 405);

    } catch (\Exception $e) {
        $this->logService->ghiLogs($request->all(), $request->isMethod('put') ? 'sua' : 'them', auth()->id(), null);
        $code = $this->dungChungDb->insertMaCodeLoi($e);
        return response()->json([
            'Err' => true,
            'Msg' => 'Có lỗi xảy ra!',
            'debug' => config('app.debug') ? $e->getMessage() : null,
        ], 500);
    }
}

public function ThuHoiQD(Request $request)
{
    try {
        $NoiDungThuHoiBanHanh = $request->input('NoiDungThuHoiBanHanh');
        $ChucVuID_NTH = $this->safeObjectId($request->input('ChucVuID_NTH'));
        $NgayThuHoiBanHanh = $request->filled('NgayThuHoiBanHanh')
            ? Carbon::createFromFormat('d/m/Y', $request->input('NgayThuHoiBanHanh'))
            : null;
        $NguoiThuHoiBanHanhID = $this->safeObjectId($request->input('NguoiThuHoiBanHanhID'));
        $TrangThai = $request->input('TrangThai');
        $QuyetDinhChinhSuaVanBangID = $this->safeObjectId($request->input('QuyetDinhChinhSuaVanBangID'));
        $donViId = $this->safeObjectId($this->currentUser->donViId());
        $userId = $this->safeObjectId(auth()->id());
        $now = now();

        if ($request->isMethod('put') || $request->isMethod('patch')) {
            // Update
            $QuyetDinhChinhSuaVanBang = QuyetDinhChinhSuaVanBang::find($QuyetDinhChinhSuaVanBangID);
            if (!$QuyetDinhChinhSuaVanBang) {
                return response()->json([
                    'Err' => true,
                    'canhbao' => true,
                    'Msg' => 'Không tìm thấy dữ liệu cần cập nhật!'
                ], 404);
            }

            $QuyetDinhChinhSuaVanBang->update([
                'NoiDungThuHoiBanHanh' => $NoiDungThuHoiBanHanh,
                'ChucVuID_NTH' => $ChucVuID_NTH,
                'NgayThuHoiBanHanh' => $NgayThuHoiBanHanh,
                'NguoiThuHoiBanHanhID' => $NguoiThuHoiBanHanhID,
                'TrangThai' => $TrangThai,
                'UserID_ThaoTac' => $userId,
                'UserID_CapNhat' => $userId,
                'NgayThaoTac' => $now,
                'NgayCapNhat' => $now,
                'DonViID_CapNhat' => $donViId,
                'DonViID_ThaoTac' => $donViId,
            ]);

            $this->logService->ghiLogs($request->all(), 'sua', auth()->id(), null);

            // === Ghi nhật ký thao tác ===
            try {
                $TenNhanVien = NhanVien::where('_id', $NguoiThuHoiBanHanhID)->value('tenNhanVien') ?? '';
                $TenChucVu = ChucVu::where('_id', $ChucVuID_NTH)->value('tenChucVu') ?? '';
                $thaoTac = 'Thu hồi quyết định chỉnh sửa văn bằng';
                $noiDungChiTiet = sprintf('%s: %s', $thaoTac, $QuyetDinhChinhSuaVanBang->SoQuyetDinh ?? '');

                $duLieuThaoTac = ThaoTac::getFirstByThaoTac('Sửa', (string) $this->currentUser->id());
                NhatKyThaoTac::luuNhatKyThaoTac(
                    $duLieuThaoTac['MaThaoTac'],
                    $duLieuThaoTac['ThaoTac'],
                    $duLieuThaoTac['MauSac'],
                    $thaoTac,
                    $noiDungChiTiet,
                    (string) $QuyetDinhChinhSuaVanBang->_id,
                    $QuyetDinhChinhSuaVanBang->getTable(),
                    url()->current(),
                    $TenNhanVien,
                    $TenChucVu,
                    (string) $this->currentUser->id(),
                    (string) $this->currentUser->donViId()
                );
            } catch (\Throwable $t) {
                \Log::error('Ghi NhatKyThaoTac thất bại: ' . $t->getMessage());
            }

            return response()->json([
                'Err' => false,
                'Msg' => 'Cập nhật dữ liệu thành công!'
            ], 200);
        }

        return response()->json([
            'Err' => true,
            'canhbao' => true,
            'Msg' => 'Phương thức HTTP không hợp lệ!'
        ], 405);

    } catch (\Exception $e) {
        $this->logService->ghiLogs($request->all(), $request->isMethod('put') ? 'sua' : 'them', auth()->id(), null);
        $code = $this->dungChungDb->insertMaCodeLoi($e);
        return response()->json([
            'Err' => true,
            'Msg' => 'Có lỗi xảy ra!',
            'debug' => config('app.debug') ? $e->getMessage() : null,
        ], 500);
    }
}



public function getYeuCauCSNDVBCC(Request $request)
{
    try {
        // Lấy danh sách ID đã có trong bảng quyết định (giữ ObjectId gốc)
        $excludedIDs = QuyetDinhChinhSuaVanBang::whereNotNull('DonYeuCauCSVBCCID')
            ->pluck('DonYeuCauCSVBCCID')
            ->toArray();

        // Truy vấn collection gốc
        $collection = DB::connection('mongodb')->getCollection('yeu_cau_c_s_n_d_v_b_c_c_s');

        $match = [
            'TrangThaiXuLyID' => '32',
        ];
        if (!empty($excludedIDs)) {
            $match['_id'] = ['$nin' => $excludedIDs];
        }

        // Lấy dữ liệu (convert sang mảng luôn)
        $result = $collection->aggregate([
            ['$match' => $match],
        ])->toArray();

        // Convert BSONDocument -> array để dễ xử lý
        $result = array_map(function ($doc) {
            return $doc instanceof \MongoDB\Model\BSONDocument ? $doc->getArrayCopy() : (array)$doc;
        }, $result);

        // Sắp xếp
        $sortField = $request->input('CbSapXep', '_id'); // Đặt biến trước khi sort
        $result = collect($result);
        if ($sortField === 'NgayLap') {
            $result = $result->sortBy('NgayLap')->values();
        } elseif ($sortField === 'TrangThaiXuLyID') {
            $order = ['40' => 0, '41' => 1, '42' => 2];
            $result = $result->sortBy(function ($item) use ($order) {
                return $order[$item['TrangThaiXuLyID']] ?? 999;
            })->values();
        } else {
            $result = $result->sortBy($sortField)->values();
        }

        // Gọi format với dữ liệu dạng mảng
        $result = $this->formatDonYeuCaus($result);

        return response()->json([
            'Err' => false,
            'result' => $result,
        ]);
    } catch (\Exception $e) {
        \Log::error('Error in getYeuCauCSNDVBCC: ' . $e->getMessage());
        return response()->json([
            'Err' => true,
            'Msg' => 'Không thể lấy danh sách!',
            'debug' => $e->getMessage(),
        ]);
    }
}


public function formatDonYeuCaus($collection)
{
    return $collection->map(function ($item) {
        // Nếu là BSONDocument thì chuyển sang mảng
        if ($item instanceof \MongoDB\Model\BSONDocument) {
            $item = $item->getArrayCopy();
        }

        // Đệ quy convert toàn bộ ObjectId thành string
        $convertIds = function (&$data) use (&$convertIds) {
            if (is_array($data)) {
                foreach ($data as $key => &$value) {
                    if ($value instanceof \MongoDB\BSON\ObjectId) {
                        $data[$key] = (string) $value;
                    } elseif ($value instanceof \MongoDB\Model\BSONDocument) {
                        $nested = $value->getArrayCopy();
                        $convertIds($nested);
                        $data[$key] = $nested;
                    } elseif (is_array($value)) {
                        $convertIds($value);
                    }
                }
            }
        };
        $convertIds($item);

        // Đổi _id thành id
        if (isset($item['_id'])) {
            $item['id'] = $item['_id'];
            unset($item['_id']);
        }

        // ---- Các xử lý format dữ liệu như cũ ----
        $hocSinhID = $item['HocSinhID'] ?? null;
        $doiTuong = $hocSinhID ? DoiTuong::find($hocSinhID) : null;

        $item['TenTruong'] = optional(DonVi::find($doiTuong ? $doiTuong->DonViID_Hoc : null))->TenDonVi ?? '';

        $item['txtNgayLap'] = !empty($item['NgayLap']) && $item['NgayLap'] instanceof \MongoDB\BSON\UTCDateTime
            ? $item['NgayLap']->toDateTime()->format('d/m/Y')
            : '';

        $item['TenNguoiLap'] = optional(NhanVien::find($item['NguoiLapID'] ?? null))->tenNhanVien ?? '';
        $item['TenChucVuNguoiLap'] = optional(ChucVu::find($item['ChucVuNguoiLapID'] ?? null))->tenChucVu ?? '';

        $item['TenHocSinh'] = optional(DoiTuong::find($hocSinhID))->Hovaten ?? '';
        $item['MaDoiTuong'] = optional(DoiTuong::find($hocSinhID))->MaDoiTuong ?? '';

        $item['TenLoaiPhoi'] = optional(LoaiPhoiVanBangChungChi::find($item['LoaiVBCCID'] ?? null))->TenLoaiPhoiVanBangChungChi ?? '';

        $donViNhan = DonVi::find($item['DonViNhanID'] ?? null);
        $item['TenDonViNhan'] = optional($donViNhan)->TenDonVi ?? '';
        $item['MaDonViNhan'] = optional($donViNhan)->MaDonVi ?? '';

        $donViGui = DonVi::find($item['DonViGuiID'] ?? null);
        $item['TenDonViGui'] = optional($donViGui)->TenDonVi ?? '';
        $item['MaDonViGui'] = optional($donViGui)->MaDonVi ?? '';

        $item['TenTepDinhKem'] = basename($item['DinhKem'] ?? '');
        $item['LinkDinhKem'] = !empty($item['DinhKem']) ? asset($item['DinhKem']) : null;

        $item['TenChucVuNguoiXuLy'] = optional(ChucVu::find($item['ChucVuNguoiXuLyID'] ?? null))->tenChucVu ?? '';
        $item['txtNgayXuLy'] = !empty($item['NgayXuLy']) && $item['NgayXuLy'] instanceof \MongoDB\BSON\UTCDateTime
            ? $item['NgayXuLy']->toDateTime()->format('d/m/Y')
            : '';
        $item['TenNguoiXuLy'] = optional(NhanVien::find($item['NguoiXuLyID'] ?? null))->tenNhanVien ?? '';

        $item['TenDonViTiepNhanXuLy'] = optional(DonVi::find($item['DonViTiepNhanXuLyID'] ?? null))->TenDonVi ?? '';
        $item['TenNguoiTiepNhanXuLy'] = optional(NhanVien::find($item['NguoiTiepNhanXuLyID'] ?? null))->tenNhanVien ?? '';
        $item['txtNgayTiepNhanXuLy'] = !empty($item['NgayTiepNhanXuLy']) && $item['NgayTiepNhanXuLy'] instanceof \MongoDB\BSON\UTCDateTime
            ? $item['NgayTiepNhanXuLy']->toDateTime()->format('d/m/Y')
            : '';
        $item['TenChucVuNguoiTiepNhanXuLy'] = optional(ChucVu::find($item['ChucVuNguoiTiepNhanXuLyID'] ?? null))->tenChucVu ?? '';

        $trangThaiXuLy = TrangThai::where("MaTrangThai", $item['TrangThaiXuLyID'] ?? null)->first();
        $item['TenTrangThaiXuLy'] = optional($trangThaiXuLy)->TenTrangThai ?? 'Chưa xác định';
        $item['MauSacTrangThaiXuLy'] = optional($trangThaiXuLy)->MauSac ?? '#ffffff';

        $item['TenHinhThucNhanPhoi'] = optional(HinhThucNhanPhoi::find($item['HinhThucNhanPhoiID'] ?? null))->tenHinhThucNhanPhoi ?? '';

        if (!empty($item['PhoiVBCC']) && is_array($item['PhoiVBCC'])) {
            $item['PhoiVBCC'] = array_map(function ($phoi) {
                $loaiPhoi = LoaiPhoiVanBangChungChi::find($phoi['LoaiPhoiVBCCID'] ?? '');
                $donViTinh = DonViTinh::find($phoi['DonViTinhID'] ?? '');
                return array_merge($phoi, [
                    'TenLoaiPhoiVanBangChungChi' => optional($loaiPhoi)->TenLoaiPhoiVanBangChungChi ?? '',
                    'TenDonViTinh' => optional($donViTinh)->TenDonViTinh ?? '',
                ]);
            }, $item['PhoiVBCC']);
        }

        return $item;
    });
}

    public function getDonYeuCauByID(Request $request)
    {
        $id = $request->input('id');
        $YeuCauCSNDVBCCID = YeuCauCSNDVBCC::find($id);
        if (!$YeuCauCSNDVBCCID) {
            return response()->json([
                'Err' => true,
                'canhbao' => true,
                'Msg' => 'Không tìm thấy đơn yêu cầu!'
            ]);
        }
        $YeuCauCSNDVBCCID = $this->formatDonYeuCaus(collect([$YeuCauCSNDVBCCID]))->first();
        return response()->json([
            'Err' => false,
            'Result' => $YeuCauCSNDVBCCID
        ]);
    }

}
