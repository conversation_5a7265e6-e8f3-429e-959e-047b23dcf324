<?php

namespace App\Http\Controllers\QuanLy;

use Str;
use Storage;
use DateTime;
use Carbon\Carbon;
use App\Models\User;
use MongoDB\BSON\Regex;
use App\Models\DienUuTien;
use App\Services\ThongBao;
use MongoDB\BSON\ObjectId;
use Illuminate\Http\Request;
use App\Models\DanhMuc\DonVi;
use App\Services\DungChungDb;
use App\Models\DanhMuc\CapHoc;
use App\Models\DanhMuc\ChucVu;
use App\Models\DanhMuc\DanToc;
use App\Models\DanhMuc\LopHoc;
use App\Models\QuanLy\SoGocCT;
use PhpOffice\PhpWord\PhpWord;
use App\Models\DanhMuc\TieuChi;
use App\Models\DanhMuc\XepLoai;
use App\Models\QuanLy\DoiTuong;
use App\Services\DungChungNoDb;
use App\Models\DanhMuc\GioiTinh;
use App\Models\DanhMuc\NhanVien;
use App\Models\QuanLy\HocSinhTN;
use App\Models\QuanLy\QuyetDinh;
use App\Models\DanhMuc\TrangThai;

use PhpOffice\PhpWord\Style\Font;
use PhpOffice\PhpWord\Shared\Html;
use App\Http\Controllers\Controller;
use App\Services\CurrentUserService;
use Illuminate\Support\Facades\Auth;
use PhpOffice\PhpWord\SimpleType\Jc;
use App\Services\OfficeConvertService;
use App\Models\DanhMuc\DiaBanHanhChinh;
use App\Models\HeThong\ThietLapHeThong;
use App\Models\QuanLy\CapBangTotNghiep;
use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\TemplateProcessor;
use App\Models\QuanLy\CapBangTotNghiepCT;
use PhpOffice\PhpWord\SimpleType\JcTable;
use App\Models\DanhMuc\MauVanBangChungChi;
use App\Models\DanhMuc\MauVanBangChungChiCT;
use App\Models\DanhMuc\LoaiPhoiVanBangChungChi;

use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Arr;
use Exception;
use PhpOffice\PhpSpreadsheet\IOFactory AS IOFactoryNhanEx;

class CapBangTotNghiepController extends Controller
{
    protected DungChungDb $dungChungDb;
    protected DungChungNoDb $logService;
    protected CurrentUserService $currentUser;

    public function __construct(DungChungDb $dungChungDb,CurrentUserService $currentUser, DungChungNoDb $logService)
    {
        $this->dungChungDb = $dungChungDb;
        $this->logService  = $logService;
        $this->currentUser  = $currentUser;
    }
    function safeObjectId($id) {
        return is_string($id) && preg_match('/^[a-f\d]{24}$/i', $id) ? new ObjectId($id) : null;
    }

    public function index()
    {
        return view('quanly.CapBangTotNghiep.index' );
    }
    /**
     * Thêm mới hoặc cập nhật thông tin đia bàn
     */
    public function luuthongtin(Request $request)
    {
        try {
            $NgayKy = $request->filled('NgayKy')
            ? Carbon::createFromFormat('d/m/Y', $request->input('NgayKy'))
            : null;
            $NhanVienID_NguoiKy =  $this->safeObjectId($request->input('NhanVienID_NguoiKy'));
            $ChucVuID_NguoiKy =  $this->safeObjectId($request->input('ChucVuID_NguoiKy'));
            $DonViID_In =  $this->safeObjectId($request->input('DonViID_In'));
            $QuyetDinhID = $this->safeObjectId($request->input('QuyetDinhID'));
            $MauVanBangChungChiID = $this->safeObjectId($request->input('MauVanBangChungChiID'));
            $CapBangTotNghiepID = $this->safeObjectId($request->input('CapBangTotNghiepID'));

            $GhiChu = $request->input('GhiChu');
            $DuongDanFileVB = $request->input('DuongDanFileVB') ? $request->input('DuongDanFileVB') : '';

            $userId = auth()->id() ? new ObjectId(auth()->id()) : null;
            $donViid = $this->currentUser->donViId();
            $now = now();

            if ($request->isMethod('post')) {

            $paths =$this->logService->saveFiles($DuongDanFileVB,"CapBangTotNghiep");

            $abcd = CapBangTotNghiep::create([
                    'NgayKy' => $NgayKy,
                    'NhanVienID_NguoiKy' => $NhanVienID_NguoiKy,
                    'ChucVuID_NguoiKy' => $ChucVuID_NguoiKy,
                    'DonViID_In' => $DonViID_In,
                    'QuyetDinhID' => $QuyetDinhID,
                    'MauVanBangChungChiID' => $MauVanBangChungChiID,
                    'DinhKem' => $paths,
                    'GhiChu' => $GhiChu,

                    'BanSao'   => false,
                    'TrangThai'   => "01",
                    'UserID_ThaoTac'   => $userId,
                    'DonViID_ThaoTac'   => $donViid,
                    'UserID_CapNhat'   => $userId,
                    'NgayThaoTac'   => $now,
                    'NgayCapNhat'   => $now,
                ]);
                $this->logService->ghiLogs($request->all(), 'them', auth()->id(), null);
                return response()->json([
                    'Err' => false,
                    'Msg' => 'Thêm dữ liệu thành công!',
                    'id' => (string) $abcd->_id
                ]);
            }

            if ($request->isMethod('put') || $request->isMethod('patch')) {
                // Update
                $CapBangTotNghiep = CapBangTotNghiep::find($CapBangTotNghiepID);
                if (!$CapBangTotNghiep) {
                    return response()->json([
                        'Err' => true,
                        'canhbao' => true,
                        'Msg' => 'Không tìm thấy dữ liệu cần cập nhật!'
                    ]);
                }
                // ✅ Xóa các file đính kèm nếu có
                if (!empty($CapBangTotNghiep->DinhKem)) {
                    $paths = explode('|', $CapBangTotNghiep->DinhKem);
                    foreach ($paths as $path) {
                        // Xử lý path: bỏ /storage/ để về path thật trong public
                        $relativePath = str_replace('/storage/', 'public/', $path);
                        if (Storage::exists($relativePath)) {
                            Storage::delete($relativePath);
                        }
                    }
                }
                $paths = $this->logService->saveFiles($DuongDanFileVB,"CapBangTotNghiep");

                $CapBangTotNghiep->update([
                    'NgayKy' => $NgayKy,
                    'NhanVienID_NguoiKy' => $NhanVienID_NguoiKy,
                    'ChucVuID_NguoiKy' => $ChucVuID_NguoiKy,
                    'DonViID_In' => $DonViID_In,
                    'QuyetDinhID' => $QuyetDinhID,
                    'MauVanBangChungChiID' => $MauVanBangChungChiID,
                    'DinhKem' => $paths,
                    'GhiChu' => $GhiChu,
                    'UserID_CapNhat'   => $userId,
                    'NgayCapNhat'   => $now,
                    'BanSao'   => false,
                ]);
                $this->logService->ghiLogs($request->all(), 'sua', auth()->id(), null);
                return response()->json([
                    'Err' => false,
                    'Msg' => 'Cập nhật dữ liệu thành công!',
                    'id' => (string) $CapBangTotNghiep->_id
                ]);
            }

            // Nếu không phải POST hoặc PUT
            return response()->json([
                'Err' => true,
                'canhbao' => true,
                'Msg' => 'Phương thức HTTP không hợp lệ!'
            ]);

        } catch (\Exception $e) {
            $this->logService->ghiLogs($request->all(), $request->isMethod('put') ? 'sua' : 'them', auth()->id(), null);
            $code = $this->dungChungDb->insertMaCodeLoi($e);
            return response()->json([
                'Err' => true,
                'Msg' => 'Có lỗi xảy ra!',
                'debug' => $e->getMessage(),
            ]);
        }
    }
    /**
    * Thêm mới hoặc cập nhật thông tin đia bàn
    */
    public function luuthongtinct(Request $request)
    {
        try {
            $CapBangTotNghiepID = $this->safeObjectId($request->input('CapBangTotNghiepID'));
            $HocSinhID = $this->safeObjectId($request->input('HocSinhID'));

            $MaHocSinh =$request->input('MaHocSinh');
            $TenHocSinh = $request->input('TenHocSinh');
            $CCCD = $request->input('CCCD');
            $NgaySinh = $request->filled('NgaySinh')
            ? Carbon::createFromFormat('d/m/Y', $request->input('NgaySinh'))
            : null;
            $GioiTinhID = $this->safeObjectId($request->input('GioiTinhID'));
            $DanTocID = $this->safeObjectId($request->input('DanTocID'));
            $NoiSinh = $request->input('NoiSinh');
            $LopHocID = $this->safeObjectId($request->input('LopHocID'));
            $DiaChi = $request->input('DiaChi');
            $DiaBanHCID_Tinh = $this->safeObjectId($request->input('DiaBanHCID_Tinh'));
            $DonViID_TruongHoc = $this->safeObjectId($request->input('DonViID_TruongHoc'));
            $GhiChu = $request->input('GhiChu');
            $XepLoaiID = $this->safeObjectId($request->input('XepLoaiID'));
            $SoHieu = $request->input('SoHieu');
            $SoVaoSo = $request->input('SoVaoSo');

            if ($request->isMethod('post')) {
            $abcd = CapBangTotNghiepCT::create([
                    'CapBangTotNghiepID'   => $CapBangTotNghiepID,
                    'HocSinhID'   => $HocSinhID,

                    'MaHocSinh'   => $MaHocSinh,
                    'TenHocSinh'   => $TenHocSinh,
                    'CCCD'   => $CCCD,
                    'NgaySinh'   => $NgaySinh,
                    'GioiTinhID'   => $GioiTinhID,
                    'DanTocID'   => $DanTocID,
                    'NoiSinh'   => $NoiSinh,
                    'LopHocID'   => $LopHocID,
                    'DiaChi'   => $DiaChi,
                    'DiaBanHCID_Tinh'   => $DiaBanHCID_Tinh,
                    'DonViID_TruongHoc'   => $DonViID_TruongHoc,
                    'GhiChu'   => $GhiChu,
                    'XepLoaiID'   => $XepLoaiID,
                    'SoHieu'   => "",
                    'SoVaoSo'   => "",
                ]);
                $this->logService->ghiLogs($request->all(), 'them', auth()->id(), null);
                return response()->json([
                    'Err' => false,
                    'Msg' => 'Thêm dữ liệu thành công!',
                    'id' => (string) $abcd->_id
                ]);
            }

            if ($request->isMethod('put') || $request->isMethod('patch')) {
                // Update
                $CapBangTotNghiepCT = CapBangTotNghiepCT::find($CapBangTotNghiepCTID);
                if (!$CapBangTotNghiepCT) {
                    return response()->json([
                        'Err' => true,
                        'canhbao' => true,
                        'Msg' => 'Không tìm thấy dữ liệu cần cập nhật!'
                    ]);
                }

                $CapBangTotNghiepCT->update([
                    'CapBangTotNghiepID'   => $CapBangTotNghiepID,
                    'HocSinhID'   => $HocSinhID,

                    'MaHocSinh'   => $MaHocSinh,
                    'TenHocSinh'   => $TenHocSinh,
                    'CCCD'   => $CCCD,
                    'NgaySinh'   => $NgaySinh,
                    'GioiTinhID'   => $GioiTinhID,
                    'DanTocID'   => $DanTocID,
                    'NoiSinh'   => $NoiSinh,
                    'LopHocID'   => $LopHocID,
                    'DiaChi'   => $DiaChi,
                    'DiaBanHCID_Tinh'   => $DiaBanHCID_Tinh,
                    'DonViID_TruongHoc'   => $DonViID_TruongHoc,
                    'GhiChu'   => $GhiChu,
                    'XepLoaiID'   => $XepLoaiID,
                    'SoHieu'   => "",
                    'SoVaoSo'   => "",
                ]);

                $this->logService->ghiLogs($request->all(), 'sua', auth()->id(), null);
                return response()->json([
                    'Err' => false,
                    'Msg' => 'Cập nhật dữ liệu thành công!',
                    'id' => (string) $CapBangTotNghiepCT->_id
                ]);
            }

            // Nếu không phải POST hoặc PUT
            return response()->json([
                'Err' => true,
                'canhbao' => true,
                'Msg' => 'Phương thức HTTP không hợp lệ!'
            ]);

        } catch (\Exception $e) {
            $this->logService->ghiLogs($request->all(), $request->isMethod('put') ? 'sua' : 'them', auth()->id(), null);
            $code = $this->dungChungDb->insertMaCodeLoi($e);
            return response()->json([
                'Err' => true,
                'Msg' => 'Có lỗi xảy ra!',
                'debug' => $e->getMessage(),
            ]);
        }
    }
    /**
     * Lấy toàn bộ đia bàn dưới dạng JSON
     */
    public function getallct(Request $request)
    {
        try {
            // 1. Truy vấn CapBangTotNghiepCT theo MauVanBangChungChiID
            $query = CapBangTotNghiepCT::query();
            $query->where('CapBangTotNghiepID', new ObjectId($request->input('CapBangTotNghiepID')));
            $result = $query->get();
            $result = $result->values()->map(function ($item, $index) {
                $item['STT'] = $index + 1;
                $item['txtNgaySinh'] = optional($item->NgaySinh)->format('d/m/Y') ?? '';
                $item['TenGioiTinh'] = optional(GioiTinh::find($item->GioiTinhID))->TenGioiTinh ?? '';
                $item['TenDanToc'] = optional(DanToc::find($item->DanTocID))->tenDanToc ?? '';
                $item['TenLop'] = optional(LopHoc::find($item->LopHocID))->TenLopHoc ?? '';
                $item['TenXepLoai'] = optional(XepLoai::find($item->XepLoaiID))->tenXepLoai ?? '';
                return $item;
            });
            return response()->json([
                'Err'    => false,
                'result' => $result,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err'   => true,
                'Msg'   => 'Không thể lấy danh sách!',
                'debug' => $e->getMessage(),
            ]);
        }
    }
     /**
     * Lấy toàn bộ đia bàn dưới dạng JSON
     */
    public function getAll(Request $request)
    {
        try {
            $query = CapBangTotNghiep::query();

            if ($request->filled('TuNgay_Loc')) {
                $tuNgay = Carbon::createFromFormat('d/m/Y', $request->input('TuNgay_Loc'))->startOfDay();
                $query->where('NgayKy', '>=', $tuNgay);
            }

            if ($request->filled('DenNgay_Loc')) {
                $denNgay = Carbon::createFromFormat('d/m/Y', $request->input('DenNgay_Loc'))->endOfDay();
                $query->where('NgayKy', '<=', $denNgay);
            }

            if ($request->filled('QuyetDinhID_Loc')) {
                $query->where('QuyetDinhID', new ObjectId($request->input('QuyetDinhID_Loc')));
            }
            
            if ($request->filled('MauVanBangChungChiID_Loc')) {
                $query->where('MauVanBangChungChiID', new ObjectId($request->input('MauVanBangChungChiID_Loc')));
            }

            $searchKey = null;
            if ($request->filled('SearchKey')) {
                $searchKey = mb_strtolower($request->input('SearchKey')); // chuẩn hóa để so sánh không phân biệt hoa thường
            }
            
            // Sắp xếp theo field được chọn (nếu có), mặc định là 'MaSoGoc'
            $sortField = $request->input('CbSapXep', 'NgayKy');
            $query->orderBy($sortField, 'asc');

             // Lấy dữ liệu DonViID
            $arrDonViID = $this->logService->LayMaDonViTheoUserID();
            // Lấy danh sách _id của User thuộc các đơn vị
            $userIds = User::whereIn('DonViID', $arrDonViID)
               ->pluck('id')
               ->map(fn($id) => new ObjectId($id)) // đảm bảo là ObjectId nếu cần
               ->toArray();
            // Truy tiếp theo UserID_ThaoTac nằm trong danh sách đó
            $result = $query->whereIn('UserID_ThaoTac', $userIds)->get();
    
            if ($sortField === 'MauVanBangChungChiID') {
                // Lấy toàn bộ Mẫu VB để map tên
                $maus = MauVanBangChungChi::pluck('TenMauVanBangChungChi', '_id');
                $result = $result->sortBy(function ($item) use ($maus) {
                    return $maus[(string)$item->MauVanBangChungChiID] ?? '';
                })->values(); // nhớ dùng ->values() để reset index nếu cần
            } else {
                $result = $result->sortBy($sortField)->values();
            }
            if ($sortField === 'NhanVienID_NguoiKy') {
                // Lấy toàn bộ Mẫu VB để map tên
                $maus = NhanVien::pluck('tenNhanVien', '_id');

                $result = $result->sortBy(function ($item) use ($maus) {
                    return $maus[(string)$item->NhanVienID_NguoiKy] ?? '';
                })->values(); // nhớ dùng ->values() để reset index nếu cần
            } else {
                $result = $result->sortBy($sortField)->values();
            }
            $result = $this->formatCapBangTotNghieps($result);

            if ($searchKey) {
                $result = $result->filter(function ($item) use ($searchKey) {
                    $fieldsToSearch = [
                        $item->txtNgayKy ?? '',
                        $item->txtNguoiKy ?? '',
                        $item->txtCoQuanCapBang ?? '',
                        $item->TenQuyetDinh ?? '',
                        $item->TenHinhThuc ?? '',
                        $item->txtPhoiBang ?? '',
                        $item->GhiChu ?? '',
                    ];
                    foreach ($fieldsToSearch as $value) {
                        if (mb_stripos($value, $searchKey) !== false) {
                            return true;
                        }
                    }
                    return false;
                })->values(); // reset key để không bị lỗ hổng index
            }
            // Map lại từng dòng để thêm Tên Cha
            return response()->json([
                'Err'    => false,
                'result' => $result,
            ]);
        } catch (\Exception $e) {
            dd($e);
            return response()->json([
                'Err'   => true,
                'Msg'   => 'Không thể lấy danh sách!',
                'debug' => $e->getMessage(),
            ]);
        }
    }

    public function xoact(Request $request)
    {
        try {
            $id = $request->input('ma');
            $model = CapBangTotNghiepCT::whereKey($id)->firstOrFail();

            // ✅ Xoá dữ liệu gốc
            $model->delete();

            return response()->json([
                'Err' => false,
                'Msg' => 'Xóa dữ liệu và file đính kèm thành công!'
            ]);

        } catch (\Exception $e) {
            $code = $this->dungChungDb->insertMaCodeLoi($e);

            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }
    /**
     * Tải dữ liệu cho form sửa
     */
    public function loadDuLieuSua(Request $request)
    {
        $id = $request->input('id');
        $CapBangTotNghiep = CapBangTotNghiep::find($id);
        if (!$CapBangTotNghiep) {
            return response()->json([
                'Err' => true,
                'canhbao' => true,
                'Msg' => 'Không tìm thấy dữ liệu!'
            ]);
        }
        $CapBangTotNghiep = $this->formatCapBangTotNghieps(collect([$CapBangTotNghiep]))->first(); // Map 1 item rồi lấy lại object
        return response()->json([
            'Err'    => false,
            'Result' => $CapBangTotNghiep
        ]);
    }

    /**
     * Xóa
     */
    public function xoa(Request $request)
    {
        try {
            $id = $request->input('ma');
            $model = CapBangTotNghiep::whereKey($id)->firstOrFail();
            // ✅ Xoá các bản ghi chi tiết liên quan (CapBangTotNghiepCT)
            CapBangTotNghiepCT::where('CapBangTotNghiepID', $model->_id)->delete(); // hoặc (string)$model->_id nếu cần
            // ✅ Xóa file đính kèm nếu có
            if (!empty($model->DinhKem)) {
                $paths = [];

                // Xử lý chuỗi đường dẫn file (dùng | hoặc , làm dấu ngăn cách)
                if (is_string($model->DinhKem)) {
                    $paths = preg_split('/[|,]/', $model->DinhKem);
                }

                foreach ($paths as $path) {
                    $cleanPath = str_replace('\\', '/', trim($path));

                    // Loại bỏ '/storage/' => còn lại: uploads/...
                    $relativePath = str_replace('/storage/', '', $cleanPath);

                    // Laravel file system path: storage/app/public/...
                    $fullStoragePath = 'public/' . $relativePath;
                    if (Storage::disk('public')->exists($relativePath)) {
                        Storage::disk('public')->delete($relativePath);
                    } else {
                        Log::warning("Không tìm thấy file để xoá: $fullStoragePath");
                    }
                }
            }

            // ✅ Xoá dữ liệu gốc
            $model->delete();

            return response()->json([
                'Err' => false,
                'Msg' => 'Xóa dữ liệu và file đính kèm thành công!'
            ]);

        } catch (\Exception $e) {
            $code = $this->dungChungDb->insertMaCodeLoi($e);

            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }
    public function formatCapBangTotNghieps($collection)
    {
        return $collection->map(function ($item) {
            // 1. Lấy quyết định
            $qd = QuyetDinh::with(['hinhThucDaoTao'])
            ->find($item->QuyetDinhID);
            if ($qd && !empty($qd->NgayKy)) {
                $date = $this->parseNgayKy($qd->NgayKy);
                $ngay = $date->format('d');
                $thang = $date->format('m');
                $nam = $date->format('Y');

                $item->TenQuyetDinh = "Quyết định công nhận tốt nghiệp số {$qd->SoQuyetDinh} ngày {$ngay} tháng {$thang} năm {$nam}";
                $item->TenHinhThuc = $qd->hinhThucDaoTao?->tenHinhThucDaoTao ?? '';
            } elseif ($qd) {
                $item->TenHinhThuc = $qd->hinhThucDaoTao?->tenHinhThucDaoTao ?? '';
                $item->TenQuyetDinh = "Quyết định công nhận tốt nghiệp số {$qd->SoQuyetDinh} ngày ... tháng ... năm ...";
            } else {
                $item->TenHinhThuc = "";
                $item->TenQuyetDinh = '';
            }
            $TrangThai = TrangThai::where("MaTrangThai", $item->TrangThai)->first();
            $item->TenTrangThai = optional($TrangThai)->TenTrangThai ?? 'Chưa xác định';
            $item->MauSac = optional($TrangThai)->MauSac ?? '#ffffff';
            $item->Ma= optional($TrangThai)->MaTrangThai ?? '';
            $item->txtNgayKy = optional($item->NgayKy)->format('d/m/Y') ?? '';
            $item->txtNguoiKy = optional(NhanVien::find($item->NhanVienID_NguoiKy))->tenNhanVien ?? '';
            $item->txtChucVu = optional(ChucVu::find($item->ChucVuID_NguoiKy))->tenChucVu ?? '';
            $item->txtCoQuanCapBang = optional(DonVi::find($item->DonViID_In))->TenDonVi ?? '';
            $item->txtPhoiBang = optional(MauVanBangChungChi::find($item->MauVanBangChungChiID))->TenMauVanBangChungChi ?? '';
            $item->SLHS =(string) CapBangTotNghiepCT::where('CapBangTotNghiepID',new ObjectId($item->_id))->count();
            return $item;
        });
    }
    function parseNgayKy($input): ?DateTime {
        if ($input instanceof \MongoDB\BSON\UTCDateTime) {
            return $input->toDateTime();
        }
        if ($input instanceof \DateTimeInterface) {
            return $input;
        }
        if (is_string($input)) {
            $dt = DateTime::createFromFormat('d/m/Y', $input);
            if ($dt) return $dt;

            try {
                return new DateTime($input);
            } catch (Exception $e) {
                return null;
            }
        }
        return null;
    }


public function GetListDonViCoHocSinh(Request $request)
{
    try {
        $QuyetDinhID = $request->input('QuyetDinhID');
        $CapBangTotNghiepID = $request->input('CapBangTotNghiepID');

       // Bước 1: Lấy danh sách HocSinhID từ quyết định (string)
        $hocSinhIDDocs = HocSinhTN::raw(function ($collection) use ($QuyetDinhID) {
            $pipeline = [];

            if (!empty($QuyetDinhID)) {
                $pipeline[] = [
                    '$match' => [
                        'QuyetDinhId' => new ObjectId($QuyetDinhID)
                    ]
                ];
            }

            $pipeline[] = [
                '$group' => [
                    '_id' => '$HocSinhID' // string
                ]
            ];

            return $collection->aggregate($pipeline);
        });

        // Chuyển string ID thành ObjectId
        $hocSinhIDs = collect(iterator_to_array($hocSinhIDDocs))
            ->pluck('_id')
            ->filter() // Bỏ null nếu có
            ->map(fn($id) => new ObjectId($id))
            ->toArray(); // để dùng trong whereIn

        // Bước 2: Lấy danh sách học sinh đã cấp bằng (nằm trong danh sách trên)
        $hocSinhIDDaCap = CapBangTotNghiepCT::raw(function ($collection) use ($CapBangTotNghiepID) {
            $pipeline = [];

            if (!empty($CapBangTotNghiepID)) {
                $pipeline[] = [
                    '$match' => [
                        'CapBangTotNghiepID' => new ObjectId($CapBangTotNghiepID)
                    ]
                ];
            }

            $pipeline[] = [
                '$group' => [
                    '_id' => '$HocSinhID'
                ]
            ];

            return $collection->aggregate($pipeline);
        });

        $hocSinhIDDaCapStr = array_map(fn($item) => (string) $item->_id, iterator_to_array($hocSinhIDDaCap));

        $hocSinhChuaCap = collect($hocSinhIDs)->filter(function ($id) use ($hocSinhIDDaCapStr) {
            return !in_array((string) $id, $hocSinhIDDaCapStr);
        })->values();

        // Truy vấn tất cả học sinh từ bước 1
        $doiTuongList = DoiTuong::whereIn('_id', $hocSinhIDs)->get();

        if ($doiTuongList->isEmpty()) {
            return response()->json([
                'Err' => false,
                'Result' => [],
                'Msg' => 'Không tìm thấy thông tin học sinh trong hệ thống.',
            ]);
        }

        // Gom theo đơn vị
        $grouped = $doiTuongList->groupBy(fn($item) => (string) $item->DonViID_Hoc);
        $donViIDs = $grouped->keys()->filter()->values();

        if ($donViIDs->isEmpty()) {
            return response()->json([
                'Err' => false,
                'Result' => [],
                'Msg' => 'Không có trường học nào có học sinh trong danh sách.',
            ]);
        }

        $ds = DonVi::whereIn('_id', $donViIDs)->orderBy('TenDonVi')->get();

        $result = $ds->map(function ($dv) use ($grouped, $hocSinhIDDaCapStr) {
            $dsHocSinh = $grouped->get((string) $dv->_id, collect());

            $tong = $dsHocSinh->count();
            $daChon = $dsHocSinh->filter(fn($hs) => in_array((string) $hs->_id, $hocSinhIDDaCapStr))->count();

            return [
                'id' => (string) $dv->_id,
                'code' => $dv->MaDonVi ?? '',
                'name' => $dv->TenDonVi ?? 'Chưa đặt tên',
                'count' => $tong,
                'daChonCount' => $daChon,
            ];
        });

        return response()->json([
            'Err' => false,
            'Result' => $result,
        ]);
    } catch (\Throwable $e) {
        return response()->json([
            'Err' => true,
            'Msg' => 'Đã xảy ra lỗi khi lấy danh sách đơn vị.',
            'Debug' => $e->getMessage(),
        ], 500);
    }
}



    public function GetListHocSinhByDonVi(Request $request)
    {
        try {
            $QuyetDinhID = $request->input('QuyetDinhID');
            $CapBangTotNghiepID = $request->input('CapBangTotNghiepID');
            $donViID = $request->input('DonViID');

            // Bước 1: Lấy toàn bộ HocSinhTN theo QuyetDinhID
            $hocSinhTNList = HocSinhTN::where('QuyetDinhId', new ObjectId($QuyetDinhID))
                ->get()
                ->keyBy('HocSinhID');
            // $hocSinhTNList = DoiTuong::where('TrangThai', true)
            //     ->get()
            //     ->keyBy('_id');
            $hocSinhIDs = $hocSinhTNList->keys();

            if ($hocSinhIDs->isEmpty()) {
                return response()->json([
                    'Err' => false,
                    'result' => [],
                    'Msg' => 'Không có học sinh trong sổ gốc.',
                ]);
            }

            $hocSinhIDDaCap = CapBangTotNghiepCT::raw(function ($collection) use ($CapBangTotNghiepID) {
                $pipeline = [];

                if (!empty($CapBangTotNghiepID)) {
                    $pipeline[] = [
                        '$match' => [
                            'CapBangTotNghiepID' => new ObjectId($CapBangTotNghiepID)
                        ]
                    ];
                }

                $pipeline[] = [
                    '$group' => [
                        '_id' => '$HocSinhID'
                    ]
                ];

                return $collection->aggregate($pipeline);
            });

            // Ép thành mảng ObjectId
            $hocSinhIDDaCapStr = array_map(fn($item) => $item->_id, iterator_to_array($hocSinhIDDaCap));

            // Bước 3: Lọc ra những học sinh chưa được cấp bằng
            $hocSinhChuaCap = collect($hocSinhIDs)->filter(function ($id) use ($hocSinhIDDaCapStr) {
                return !in_array((string) $id, $hocSinhIDDaCapStr);
            })->values();

            if ($hocSinhChuaCap->isEmpty()) {
                return response()->json([
                    'Err' => false,
                    'result' => [],
                    'Msg' => 'Tất cả học sinh đã được cấp văn bằng, chứng chỉ.',
                ]);
            }
            $hocSinhChuaCap = collect($hocSinhChuaCap)->map(fn($id) => new ObjectId($id))->toArray();

            // Bước 4: Truy vấn DoiTuong theo DonViID (nếu có)
            $query = DoiTuong::whereIn('_id', $hocSinhChuaCap)->where('DonViID_Hoc', new ObjectId($donViID));

            // Bước 5: Sắp xếp
            $sortField = $request->input('CbSapXep', 'MaDoiTuong');
            $query->orderBy($sortField, 'asc');

            $result = $query->get();
            // Bước 6: Gán KetQuaTN từ SoGocCT
            $result = $result->map(function ($item) use ($hocSinhTNList) {
                $hocSinhTN = $hocSinhTNList[$item->_id] ?? null;
                $item->KetQuaTN = $hocSinhTN->KetQuaTN ?? null;
                return $item;
            });

            // Bước 7: Format (nếu có)
            $result = $this->formatDoiTuongs($result);

            return response()->json([
                'Err' => false,
                'result' => $result,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Không thể lấy danh sách!',
                'debug' => $e->getMessage(),
            ]);
        }
    }


    public function formatDoiTuongs($collection)
    {
        return $collection->map(function ($item) {
            // 1. Format giới tính
            $item->txtGioitinh = optional(GioiTinh::find($item->Gioitinh))->TenGioiTinh ?? '';
            // 2. Format ngày sinh
            $item->txtNgaysinh = optional($item->Ngaysinh)->format('d/m/Y') ?? '';
            // 3. Format ngày cấp
            $item->txtNgayCap = optional($item->NgayCap)->format('d/m/Y') ?? '';
            // 4. DonViID_Hoc
            $item->TenLopHoc = optional(LopHoc::find($item->LopHocID))->TenLopHoc ?? '';
            // 4. DonViID_Hoc
            $item->TenDienUuTien = optional(DienUuTien::find($item->DienUuTienID))->TenDienUuTien ?? '';
            // 4. DonViID_Hoc
            $item->TenDonVi = optional(DonVi::find($item->DonViID_Hoc))->TenDonVi ?? '';
            // 4. DonViID_Hoc
            $item->MaDonVi = optional(DonVi::find($item->DonViID_Hoc))->MaDonVi ?? '';
            // 5. DiaBanHCID_NoiCap
            $item->TenNoiCap = optional(DiaBanHanhChinh::find($item->DiaBanHCID_NoiCap))->TenDiaBan ?? '';
            // Lấy thông tin trạng thái cấp bằng
            $trangThaiCapBang = TrangThai::where("MaTrangThai", $item->TrangThai_CapBang)->first();
            $item->TenTrangThai_CapBang = optional($trangThaiCapBang)->TenTrangThai ?? 'Chưa xác định';
            $item->MauSac_CapBang = optional($trangThaiCapBang)->MauSac ?? '#ffffff';

            // Lấy thông tin trạng thái tốt nghiệp
            $trangThaiTotNghiep = TrangThai::where("MaTrangThai", $item->TrangThai_TotNghiep)->first();
            $item->TenTrangThai_TotNghiep = optional($trangThaiTotNghiep)->TenTrangThai ?? 'Chưa xác định';
            $item->MauSac_TotNghiep = optional($trangThaiTotNghiep)->MauSac ?? '#ffffff';            
            // 6. DanTocID
            $item->TenDanToc = optional(DanToc::find($item->DanTocID))->tenDanToc ?? '';

            // 7. DiaBanHCID_Tinh
            $item->Tinh = optional(DiaBanHanhChinh::find($item->DiaBanHCID_Tinh))->TenDiaBan ?? '';

            // 8. DiaBanHCID_Xa
            $item->Xa = optional(DiaBanHanhChinh::find($item->DiaBanHCID_Xa))->TenDiaBan ?? '';

            // 9. DiaBanHCID_Thon
            $item->Thon = optional(DiaBanHanhChinh::find($item->DiaBanHCID_Thon))->TenDiaBan ?? '';

            return $item;
        });
    }
    public function getMauVanBang(Request $request)
    {
        try {
            $CapBangTotNghiepID = $request->input('CapBangTotNghiepID');
            $CapBangTotNghiep = CapBangTotNghiep::where('_id', new ObjectId($CapBangTotNghiepID))
            ->first(['MauVanBangChungChiID']);
            return response()->json([
                'Err' => false,
                'result' =>(string) $CapBangTotNghiep->MauVanBangChungChiID,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Không thể lấy danh sách!',
                'debug' => $e->getMessage(),
            ]);
        }
    }

    public function getFileWordV2(Request $request)
    {
        // Accept raw JSON: {"parts":[...]}
        $data = $request->getContent();
        $json = json_decode($data, true);
        $rows = $json['parts'] ?? [];

        $username = $this->currentUser->username();

        // Build directory and filenames
        $subDir = "/xuatword/{$username}/capbangtotnghiep";
        $folder = public_path($subDir);

        // Ensure directory exists
        if (!is_dir($folder)) {
            mkdir($folder, 0777, true);
        }

        // Delete old files if present
        $docxPath = "{$folder}/BangTotNgiep.docx";
        $pdfPath = "{$folder}/BangTotNgiep.pdf";
        if (file_exists($docxPath)) unlink($docxPath);
        if (file_exists($pdfPath)) unlink($pdfPath);

        // Create the Word file
        $phpWord = new \PhpOffice\PhpWord\PhpWord();
        $sectionStyle = [
            'marginTop'    => 1134,
            'marginBottom' => 1134,
            'marginLeft'   => 1134,
            'marginRight'  => 1134,
            'orientation'  => 'landscape', // <-- Bắt buộc phải có dòng này
            'pageSizeW'    => \PhpOffice\PhpWord\Shared\Converter::inchToTwip(8.3), // Chiều ngang A5
            'pageSizeH'    => \PhpOffice\PhpWord\Shared\Converter::inchToTwip(5.8), // Chiều dọc A5
        ];

        $CapBangTotNghiepCT = CapBangTotNghiepCT::where('CapBangTotNghiepID', new ObjectId($request->input('ID')))->get();

        foreach ($CapBangTotNghiepCT as $capBang) {
            $section = $phpWord->addSection($sectionStyle);
            $this->renderCertificate($section, $rows,$capBang->_id); // Lặp lại nội dung
        }

        $phpWordWriter = \PhpOffice\PhpWord\IOFactory::createWriter($phpWord, 'Word2007');
        $phpWordWriter->save($docxPath);

        // Convert DOCX → PDF
        // Note: relative path from public/ for your static method
        $docxRelative = "{$subDir}/BangTotNgiep.docx";
        $conversionSuccess = OfficeConvertService::DocxToPdf($docxRelative);

        // URLs for response (from web root)
        $baseUrl = url('/');
        $docxUrl = $baseUrl . '/'. ltrim($subDir . '/BangTotNgiep.docx', '/');
        $pdfUrl = $baseUrl . '/'. ltrim($subDir . '/BangTotNgiep.pdf', '/');

        return response()->json([
                'success' => true,
                'docxUrl' => $docxUrl,
                'pdfUrl' => $pdfUrl,
                'message' => $conversionSuccess ? 'OK' : 'PDF conversion failed'
            ]);
        }

        public function marginToSpacing($marginStr) {
            $parts = preg_split('/\s+/', trim($marginStr));
            $top = isset($parts[0]) ? (int) $parts[0] : 0;
            $bottom = isset($parts[2]) ? (int) $parts[2] : 0;
            return [
                'spaceBefore' => $top * 20,
                'spaceAfter' => $bottom * 20,
            ];
        }

        // Render a single row as a normal PHPWord paragraph
        public function renderRow($section, $row) {
            $bool = function($v) {
                return filter_var($v, FILTER_VALIDATE_BOOLEAN);
            };
            $fontStyle = [
                'name' => $row['Font'] ?? 'Times New Roman',
                'size' => (int)($row['FontSize'] ?? 11),
                'bold' => $bool($row['InDam'] ?? false),
                'italic' => $bool($row['InNghien'] ?? false),
                'underline' => $bool($row['GachChan'] ?? false) ? Font::UNDERLINE_SINGLE : Font::UNDERLINE_NONE,
                'allCaps' => $bool($row['InHoa'] ?? false),
                'color' => (!empty($row['MauSac']) && strtolower($row['MauSac']) !== 'null') ? $row['MauSac'] : null,
            ];
            $align = strtolower($row['TextAlign'] ?? 'left');
            $alignment = Jc::LEFT;
            if ($align === 'center') $alignment = Jc::CENTER;
            elseif ($align === 'right') $alignment = Jc::RIGHT;

            $margin = $this->marginToSpacing($row['Margin'] ?? '');

            $paraStyle = [
                'alignment' => $alignment,
                'spaceBefore' => $margin['spaceBefore'],
                'spaceAfter' => $margin['spaceAfter'],
            ];

            $text = $row['TenTieuChi'] ??  '';
            if ($bool($row['InHoa'] ?? false)) {
                $text = mb_strtoupper($text, 'UTF-8');
            }

            $section->addText($text, $fontStyle, $paraStyle);
        }

        // Render a grid row as a table (proportional cell widths)
        public function renderGridRow($section, $rowBuffer, $totalCols,$id)
        {

            $tableWidth = 9000;
            $table = $section->addTable([
                'borderSize' => 0,          // No borders
                'cellMargin' => 0,
                'alignment' => JcTable::CENTER,
                'width' => $tableWidth,
            ]);
            $table->addRow();

            // Group cells by Float: left first, then right
            $leftCells = [];
            $rightCells = [];
            foreach ($rowBuffer as $cellData) {
                $float = strtolower($cellData['row']['Float'] ?? 'left');
                if ($float === 'right') {
                    $rightCells[] = $cellData;
                } else {
                    $leftCells[] = $cellData;
                }
            }
            $orderedCells = array_merge($leftCells, $rightCells);

            foreach ($orderedCells as $cellData) {
                $colSpan = $cellData['col'];
                $cellWidth = intval($tableWidth * $colSpan / 12);
                $cell = $table->addCell($cellWidth, [
                    'valign' => 'top',
                    'borderSize' => 0,
                    'borderTopSize' => 0,
                    'borderRightSize' => 0,
                    'borderBottomSize' => 0,
                    'borderLeftSize' => 0,
                    'borderColor' => 'ffffff'

                    ]);
                if ($cellData['row'] !== null) {
                    $this->renderCellText($cell, $cellData['row'],$id);
                }
            }
        }

        // Helper for rendering text inside a table cell
        public function renderCellText($cell, $row, $id) {
            $bool = function($v) {
                return filter_var($v, FILTER_VALIDATE_BOOLEAN);
            };
            $fontStyle = [
                'name' => $row['Font'] ?? 'Times New Roman',
                'size' => (int)($row['FontSize'] ?? 11),
                'bold' => $bool($row['InDam'] ?? false),
                'italic' => $bool($row['InNghien'] ?? false),
                'underline' => $bool($row['GachChan'] ?? false) ? Font::UNDERLINE_SINGLE : Font::UNDERLINE_NONE,
                'allCaps' => $bool($row['InHoa'] ?? false),
                'color' => (!empty($row['MauSac']) && strtolower($row['MauSac']) !== 'null') ? $row['MauSac'] : null,
            ];
            $align = strtolower($row['TextAlign'] ?? 'left');
            $alignment = Jc::LEFT;
            if ($align === 'center') $alignment = Jc::CENTER;
            elseif ($align === 'right') $alignment = Jc::RIGHT;

            $margin = $this->marginToSpacing($row['Margin'] ?? '');

            $paraStyle = [
                'alignment' => $alignment,
                'spaceBefore' => $margin['spaceBefore'],
                'spaceAfter' => $margin['spaceAfter'],
            ];
            //Xử LÝ GÁN BIẾN
            $text = $row['TenTieuChiHTML'] ??  '';
            if (!empty($row['StringSQL'])) {
                $capBangCT = CapBangTotNghiepCT::find(new ObjectId($id));
                $capBang = CapBangTotNghiep::find($capBangCT->CapBangTotNghiepID);

                $params = json_decode($row['StringSQL'], true);
                $type = $params['type'] ?? 'Text';
                $fieldName  = $params['field']??"";
                $modelClass = "";
                $instance = "";
                $replacement = "";

                // if ($type === "Text") {
                //     $modelClass = '\\App\\Models\\' . $params['model'];
                //     $instance = $modelClass::find($capBangCT->HocSinhID);
                //     $replacement = $instance->{$params['field']} ?? '';
                //     $text = str_replace('@Bien', $replacement, $text);
                // }

                // if ($type === "Date") {
                //     $modelClass = '\\App\\Models\\' . $params['model'];
                //     $instance = $modelClass::find($capBangCT->HocSinhID);
                //     $replacement = $instance->{$params['field']} ?? '';
                //     if($fieldName === 'Ngaysinh'){
                //         $formatted = Carbon::parse($replacement)->format('d/m/Y');
                //         $text = str_replace('@Bien', $formatted, $text);
                //     }
                // }

                if ($type === "Guid") {
                    $modelClass = '\\App\\Models\\' . $params['model'];
                    $instance = $modelClass::find($capBangCT->HocSinhID);
                    $replacement = $instance->{$params['field']} ?? '';

                    // if ($fieldName === 'Gioitinh') {
                    //     $gioiTinh = GioiTinh::find($replacement);
                    //     $replacement = $gioiTinh->TenGioiTinh ?? '';
                    //     $text = str_replace('@Bien', $replacement, $text);
                    // }

                    // if ($fieldName === 'DanTocID') {
                    //     $gioiTinh = DanToc::find($replacement);
                    //     $replacement = $gioiTinh->tenDanToc ?? '';
                    //     $text = str_replace('@Bien', $replacement, $text);
                    // }

                    // if ($fieldName === 'DonViID_Hoc') {
                    //     $DonVi = DonVi::find($replacement);
                    //     $replacement = $DonVi->TenDonVi ?? '';
                    //     $text = str_replace('@Bien', $replacement, $text);
                    // }

                    if ($fieldName === 'KhoaThiID'||$fieldName === 'HinhThucID'||$fieldName === 'HoiDongID') {
                        $current = $capBangCT;
                        foreach ($params['chain'] as $step) {
                            $model = '\\App\\Models\\' . $step['model'];
                            $key = $step['key'];
                            if (!$current || !isset($current->{$key})) {
                                $current = null;
                                break;
                            }

                            $id = $current->{$key};
                            $current = $model::find($id instanceof ObjectId ? $id : new ObjectId($id));
                        }
                        if ($current && isset($step['field'])) {
                            $replacement = $current->{$step['field']} ?? '';
                            $text = str_replace('@Bien', $replacement, $text);
                        }else{
                             $text = str_replace('@Bien', "", $text);
                        }
                    }

                    // if ($fieldName === 'XepLoaiID') {
                    //     // Bước 1: SoGocCT::where('HocSinhID', ...)
                    //     $hocSinhId = $capBangCT->{$params['key']} ?? null;
                    //     $step1 = $params['chain'][0];
                    //     $model1 = '\\App\\Models\\' . $step1['model'];
                    //     $soGocCT = $model1::where($step1['key'], $hocSinhId)->first();
                    //     if ($soGocCT && isset($params['chain'][1])) {
                    //         $step2 = $params['chain'][1];
                    //         $model2 = '\\App\\Models\\' . $step2['model'];

                    //         $xepLoaiId = $soGocCT->{$step2['key']} ?? null;
                    //         $xepLoai = $model2::find($xepLoaiId);

                    //         $replacement = $xepLoai->{$step2['field']} ?? '';
                    //         $text = str_replace('@Bien', $replacement, $text);
                    //     }
                    // }

                }

                if ($type === "Custom") {
                    // if ($fieldName === 'SoHieuVanBang' || $fieldName === 'SoVaoSoGoc') {
                    //     $hocSinhId = $capBangCT->{$params['key']} ?? null;
                    //     $step = $params['chain'][0];
                    //     $modelClass = '\\App\\Models\\' . $step['model'];

                    //     $soGocCT = $modelClass::where($step['key'], $hocSinhId)->first();
                    //     $replacement = $soGocCT->{$step['field']} ?? '';
                    //     $text = str_replace('@Bien', $replacement, $text);
                    // }
                    if ($fieldName === 'DiaDanh') {
                        // Ngày cấp bằng

                            if (!empty($capBang->NgayKy)) {
                                $date = \Carbon\Carbon::parse($capBang->NgayKy);
                                $text = str_replace('@Ngay', $date->format('d'), $text);
                                $text = str_replace('@Thang', $date->format('m'), $text);
                                $text = str_replace('@Nam', $date->format('Y'), $text);
                            }

                            // Địa danh từ cấu hình
                            $diaDanh = ThietLapHeThong::where('UserID', auth()->id() ? new ObjectId(auth()->id()) : null)->first()?->DiaDanh ?? '';
                            $text = str_replace('@DiaDanh', $diaDanh, $text);
                    }
                    else if ($fieldName === 'DonViID_In') {
                         if (!empty($capBang->DonViID_In)) {
                            $DonVi = DonVi::find($capBang->DonViID_In);
                            $replacement = $DonVi->TenDonVi ?? '';
                            $text = str_replace('@Bien', $replacement, $text);
                        }else{$text = str_replace('@Bien', "", $text);}
                    }
                    else if($fieldName === "NamTotNghiep"){
                         if (!empty($capBang->QuyetDinhID)) {
                            $QuyetDinh = QuyetDinh::find($capBang->QuyetDinhID);
                            $replacement = $QuyetDinh->NamTotNghiep ?? '';
                            $text = str_replace('@Bien', $replacement, $text);
                        }
                        else{$text = str_replace('@Bien', "", $text);}
                    }
                    //
                    else if ($fieldName === 'TenHocSinh') {
                        if (!empty($capBangCT->TenHocSinh)) {
                            $text = str_replace('@Bien',$capBangCT->TenHocSinh, $text);
                        }else{$text = str_replace('@Bien', "", $text);}
                    }
                    else if ($fieldName === 'SoHieu') {
                        if (!empty($capBangCT->SoHieu)) {
                            $text = str_replace('@Bien',$capBangCT->SoHieu, $text);
                        }else{$text = str_replace('@Bien', "", $text);}
                    }
                    else if ($fieldName === 'SoVaoSo') {
                        if (!empty($capBangCT->SoVaoSo)) {
                            $text = str_replace('@Bien',$capBangCT->SoVaoSo, $text);
                        }else{$text = str_replace('@Bien', "", $text);}
                    }
                    else if ($fieldName === 'SoVaoSoBanSao') {
                        if (!empty($capBangCT->SoVaoSoBanSao)) {
                            $text = str_replace('@Bien',$capBangCT->SoVaoSoBanSao, $text);
                        }else{$text = str_replace('@Bien', "", $text);}
                    }
                    else if ($fieldName === 'GioiTinhID') {
                        if (!empty($capBangCT->GioiTinhID)) {
                            $gioiTinh = GioiTinh::find($capBangCT->GioiTinhID);
                            $replacement = $gioiTinh->TenGioiTinh ?? '';
                            $text = str_replace('@Bien', $replacement, $text);
                        }else{$text = str_replace('@Bien', "", $text);}
                    }
                    else if ($fieldName === 'DanTocID') {
                        if (!empty($capBangCT->DanTocID)) {
                            $DanToc = DanToc::find($capBangCT->DanTocID);
                            $replacement = $DanToc->tenDanToc ?? '';
                            $text = str_replace('@Bien', $replacement, $text);
                        }else{$text = str_replace('@Bien', "", $text);}
                    }
                    else if ($fieldName === 'XepLoaiID') {
                        if (!empty($capBangCT->XepLoaiID)) {
                            $XepLoai = XepLoai::find($capBangCT->XepLoaiID);
                            $replacement = $XepLoai->tenXepLoai ?? '';
                            $text = str_replace('@Bien', $replacement, $text);
                        }else{$text = str_replace('@Bien', "", $text);}
                    }
                    else if ($fieldName === 'DonViID_TruongHoc') {
                        if (!empty($capBangCT->DonViID_TruongHoc)) {
                            $DonVi = DonVi::find($capBangCT->DonViID_TruongHoc);
                            $replacement = $DonVi->TenDonVi ?? '';
                            $text = str_replace('@Bien', $replacement, $text);
                        }else{$text = str_replace('@Bien', "", $text);}
                    }
                    else if ($fieldName === 'NoiSinh') {
                        if (!empty($capBangCT->NoiSinh)) {
                            $text = str_replace('@Bien',$capBangCT->NoiSinh, $text);
                        }else{$text = str_replace('@Bien', "", $text);}
                    }
                    else if ($fieldName === 'NgaySinh') {
                        if (!empty($capBangCT->NgaySinh)) {
                            $formatted = Carbon::parse($capBangCT->NgaySinh)->format('d/m/Y');
                            $text = str_replace('@Bien', $formatted, $text);
                        }else{$text = str_replace('@Bien', "", $text);}
                    }
                }
            }
            if ($bool($row['InHoa'] ?? false)) {
                $text = mb_strtoupper($text, 'UTF-8');
            }
            $cell->addText($text, $fontStyle, $paraStyle);
        }
        private function isISODate($value)
        {
            return is_string($value) && preg_match('/^\d{4}-\d{2}-\d{2}T/', $value);
        }
        // Bootstrap grid column parser
        public function getGridColumn($classes) {
            if (!is_array($classes)) $classes = [];
            foreach ($classes as $class) {
                if (preg_match('/col-(xs|sm|md|lg|xl)-(\d+)/', $class, $m)) {
                    return (int)$m[2];
                } elseif (preg_match('/col-md-(\d+)/', $class, $m)) {
                    return (int)$m[1];
                }
            }
            return 12; // fallback
        }

        // The grid-aware certificate renderer!
        public function renderCertificate($section, $rows,$id)
        {

            $rowBuffer = [];
            $colCount = 0;
            $i = 0;
            $n = count($rows);

            while ($i < $n) {
                $row = $rows[$i];
                $col = $this->getGridColumn($row['Classes'] ?? []);

                $float = isset($row['Float']) ? strtolower($row['Float']) : 'left';

                // Flush buffer if adding this cell would exceed 12 columns
                if ($colCount + $col > 12) {
                    // Optionally pad before flush
                    if ($colCount < 12) {
                        $rowBuffer[] = ['col' => 12 - $colCount, 'row' => null];
                    }
                    $this->renderGridRow($section, $rowBuffer, $colCount,$id);
                    $rowBuffer = [];
                    $colCount = 0;
                }

                // Float: right and at row start → pad left
                if ($float == 'right' && empty($rowBuffer) && $col < 12) {
                    $pad = 12 - $col;
                    $rowBuffer[] = ['col' => $pad, 'row' => null];
                    $colCount += $pad;
                }

                // Add cell
                $rowBuffer[] = ['col' => $col, 'row' => $row];
                $colCount += $col;

                // Flush if full row (after adding)
                if ($colCount == 12) {
                    $this->renderGridRow($section, $rowBuffer, $colCount,$id);
                    $rowBuffer = [];
                    $colCount = 0;
                }

                $i++;
            }

            // Flush leftovers (pad if needed)
            if ($rowBuffer) {
                if ($colCount < 12) {
                    $rowBuffer[] = ['col' => (12 - $colCount), 'row' => null];
                }
                $this->renderGridRow($section, $rowBuffer, 12,$id);
            }
        }

    public function LayThongTinThongKeXepLoai(Request $request)
    {
        $CapBangTotNghiepID = new ObjectId($request->input('CapBangTotNghiepID'));

        // Bước 1: Thống kê số lượng theo XepLoaiID
        $thongKe = CapBangTotNghiepCT::raw(function ($collection) use ($CapBangTotNghiepID) {
            return $collection->aggregate([
                ['$match' => ['CapBangTotNghiepID' => $CapBangTotNghiepID]],
                ['$group' => [
                    '_id' => '$XepLoaiID',
                    'SoLuong' => ['$sum' => 1],
                ]],
            ]);
        });

        // Tổng cộng tất cả số lượng
        $tongCong = collect($thongKe)->sum('SoLuong');

        // Đưa thống kê về map: [XepLoaiID => SoLuong]
        $thongKeMap = collect($thongKe)->mapWithKeys(function ($item) {
            return [(string) $item['_id'] => $item['SoLuong']];
        });

        // Bước 2: Lấy toàn bộ danh sách XepLoai
        $xepLoaiList = XepLoai::all();

        // Bước 3: Gộp dữ liệu và thêm cột TongCong
        $ketQua = $xepLoaiList->map(function ($xepLoai) use ($thongKeMap, $tongCong) {
            $idStr = (string) $xepLoai->_id;
            return [
                'XepLoaiID'   => $idStr,
                'TenXepLoai'  => $xepLoai->tenXepLoai ?? '',
                'MaXepLoai'   => $xepLoai->maXepLoai ?? '',
                'SoLuong'     => $thongKeMap->get($idStr, 0),
                'TongCong'    => $tongCong,
            ];
        })->sortBy('MaXepLoai')->values();

        return response()->json($ketQua);
    }
    public function KiemTraDaTa(Request $request)
    {
        $kyTuDau = $request->input('KyTuDau');     // A
        $soTuTang = $request->input('SoTuTang');   // 001
        $soLuong = (int) $request->input('SoLuong'); // 5
        $CapBangTotNghiepID = new ObjectId($request->input('CapBangTotNghiepID'));

        $chuaTonTai = [];
        $daTonTai = [];
        $txtdaTonTai = [];

        $soBatDau = intval(ltrim($soTuTang, '0'));
        $doDai = strlen($soTuTang);
        $dem = 0;
        $i = 0;

        while (count($chuaTonTai) < $soLuong) {
            $so = str_pad($soBatDau + $i, $doDai, '0', STR_PAD_LEFT);
            $soVaoSo = $kyTuDau . $so;

            // Kiểm tra có tồn tại không
            $ct = CapBangTotNghiepCT::where('SoVaoSo', $soVaoSo)->first();

            if ($ct) {
                // Đã tồn tại
                $cb = CapBangTotNghiep::find($ct->CapBangTotNghiepID);
                $ngayCap = optional($cb?->NgayCap)->format('d/m/Y') ?? 'không rõ ngày';
                $tenHocSinh = $ct->TenHocSinh ?? 'Không rõ tên';
                $tenDonVi = optional(DonVi::find($cb?->DonViID_In))->TenDonVi ?? 'Không rõ đơn vị';
                $tenNguoiKy = optional(NhanVien::find($cb?->NhanVienID_NguoiKy))->tenNhanVien ?? 'Không rõ người ký';

                $daTonTai[] = $soVaoSo;
                $txtdaTonTai[] = "Số hiệu: $soVaoSo cấp ngày $ngayCap cho $tenHocSinh, đơn vị cấp $tenDonVi";
            } else {
                // Chưa tồn tại => thêm vào
                $chuaTonTai[] = $soVaoSo;
            }

            $i++; // tăng số tiếp theo
        }

            $CapBangTotNghiep = CapBangTotNghiep::where('_id', $CapBangTotNghiepID)->first();

                return response()->json([
                    'Err' => false,
                    'ChuaTonTai' => $chuaTonTai,
                    'DaTonTai' => $daTonTai,
                    'txtDaTonTai' => $txtdaTonTai,
                    'NgayKy' => optional($CapBangTotNghiep?->NgayKy)->format('d/m/Y') ?? '',
                    'TenNguoiKy' => optional(
                        NhanVien::find($CapBangTotNghiep?->NhanVienID_NguoiKy)
                    )->tenNhanVien ?? '',
                    'TenDonVi' => optional(
                        DonVi::find($CapBangTotNghiep?->DonViID_In)
                    )->TenDonVi ?? '',
                ]);
    }




        #region Nhận ex
        /**
         * Kiểm tra cấu trúc file, load lên session cho preview
         */
        public function checkExcel(Request $request)
        {
            $path  = $request->input('path', '');
            $sheet = $request->input('sheet', '');
            $CapBangTotNghiepID = $request->input('CapBangTotNghiepID', '');
            if (! $path || ! $sheet) {
                return response()->json(['Err' => true, 'Msg' => 'Missing path or sheet'], 422);
            }

            // Turn URL “/storage/…” into storage/app/public-relative
            $urlPath  = parse_url($path, PHP_URL_PATH);
            $urlPath  = preg_replace('#^/+/#', '/', $urlPath);
            $relative = Str::after($urlPath, '/storage/');

            if (! Storage::disk('public')->exists($relative)) {
                return response()->json(['Err' => true, 'Msg' => 'File not found on server'], 404);
            }
            $fullPath = Storage::disk('public')->path($relative);

            try {
                // load data‐only
                $reader      = IOFactoryNhanEx::createReaderForFile($fullPath);
                $reader->setReadDataOnly(true);
                $spreadsheet = $reader->load($fullPath);

                if (! $spreadsheet->sheetNameExists($sheet)) {
                    return response()->json([
                        'Err' => true,
                        'Msg' => "Sheet “{$sheet}” không tồn tại"
                    ], 422);
                }

                $ws   = $spreadsheet->getSheetByName($sheet);
                $rows = $ws->toArray(null, true, true, false);

                // grab & trim header
                $header = array_map('trim', array_shift($rows));

                // required columns
                $required = [
                        'Mã học sinh',     // Cột A
                        'Họ và tên',       // Cột B
                        'Số CMND/CCCD',    // Cột C
                        'Ngày sinh',       // Cột D
                        'Giới tính',       // Cột E
                        'Dân tộc',         // Cột F
                        'Nơi sinh',        // Cột G
                        'Lớp',             // Cột H
                        'Trường học',      // Cột H
                        'Tỉnh/thành phố',  // Cột H
                        'Địa chỉ',         // Cột I
                        'Xếp loại',        // Cột J
                        'Số hiệu',         // Cột K
                        'Số vào sổ',       // Cột L
                ];
                if ($missing = array_diff($required, $header)) {
                    return response()->json([
                        'Err' => true,
                        'Msg' => 'Thiếu cột: ' . implode(', ', $missing),
                    ], 422);
                }

                // combine into assoc rows
                $assoc = [];
                foreach ($rows as $r) {
                    if (count($r) === count($header)) {
                        $assoc[] = array_combine($header, $r);
                        $assoc = array_map(function ($row) {
                            return array_map(function ($val) {
                                if (is_string($val)) {
                                    return ltrim(trim($val), "'");
                                }
                                return $val;
                            }, $row);
                        }, $assoc);
                    }
                }

                // Lấy danh sách Mã đơn vị từ file
                $maSoList = array_column($assoc, 'Mã học sinh');
                $cccdList = array_column($assoc, 'Số CMND/CCCD');
                $soHieuList = array_column($assoc, 'Số hiệu');
                $soVaoSoList = array_column($assoc, 'Số vào sổ');

                // 1. Trùng trong file
                $maSoList = array_filter($maSoList, fn($val) => is_string($val) || is_int($val));
                $dupesInFile = array_filter(array_count_values($maSoList), fn($count) => $count > 1);
                $dupeKeysInFile = array_keys($dupesInFile);

                $soHieuList = array_filter($soHieuList, fn($val) => is_string($val) || is_int($val));
                $dupesInFile2 = array_filter(array_count_values($soHieuList), fn($count) => $count > 1);
                $dupeKeysInFile2 = array_keys($dupesInFile2);

                $soVaoSoList = array_filter($soVaoSoList, fn($val) => is_string($val) || is_int($val));
                $dupesInFile3 = array_filter(array_count_values($soVaoSoList), fn($count) => $count > 1);
                $dupeKeysInFile3 = array_keys($dupesInFile3);

                $cccdList = array_filter($cccdList, fn($val) => is_string($val) || is_int($val));
                $dupesInFile4 = array_filter(array_count_values($cccdList), fn($count) => $count > 1);
                $dupeKeysInFile4 = array_keys($dupesInFile4);

                // 2. Trùng trong DB
                $dupesInDB = CapBangTotNghiepCT::whereIn('MaHocSinh', $maSoList)
                    ->pluck('MaHocSinh')
                    ->toArray();

                $dupesInDB2 = CapBangTotNghiepCT::whereIn('SoHieu', $soHieuList)
                    ->pluck('SoHieu')
                    ->toArray();

                $dupesInDB3 = CapBangTotNghiepCT::whereIn('SoVaoSo', $soVaoSoList)
                    ->pluck('SoVaoSo')
                    ->toArray();

                $dupesInDB4 = CapBangTotNghiepCT::whereIn('CCCD', $cccdList)
                    ->pluck('CCCD')
                    ->toArray();

                // 3. Gắn cột "TrangThai"
                foreach ($assoc as &$row) {
                    $maSo = $row['Mã học sinh'];
                    $danToc = $row['Dân tộc'];
                    $gioiTinh = $row['Giới tính'];
                    $cccd = $row['Số CMND/CCCD'];
                    $lop = $row['Lớp'];
                    $xepLoai = $row['Xếp loại'];
                    $soHieu = $row['Số hiệu'];
                    $soVaoSo = $row['Số vào sổ'];

                    $truongHoc = $row['Trường học'];
                    $tinhThanhpho = $row['Tỉnh/thành phố'];

                    $maDonVi = $this->currentUser->maDonVi();
                    $pattern = '/^' . preg_quote($maDonVi, '/') . '(\..*)?$/';
                    $donViModel = DonVi::where('MaDonVi', 'regexp', $pattern)->where('TenDonVi', $truongHoc)->first();
                    $tinhModel = DiaBanHanhChinh::where('TenDiaBan', $tinhThanhpho)->first();

                    $gioiTinhModel = GioiTinh::where('TenGioiTinh', $gioiTinh)->first();
                    $cccdModel = DoiTuong::where(function ($query) use ($cccd, $maSo) {
                        $query->where('CCCD', $cccd)
                                ->orWhere('MaDoiTuong', $maSo);
                    })->first();
                    $danTocModel = DanToc::where('tenDanToc', $danToc)->first();
                    $lopModel = LopHoc::where('TenLopHoc', $lop)->first();
                    $xepLoaiModel = XepLoai::where('tenXepLoai', $xepLoai)->first();
                    $messages = [];

                    if (trim($maSo) === '') {
                        $messages[] = 'Mã học sinh không được để trống';
                    }
                    if (trim($cccd) === '') {
                        $messages[] = 'Số CMND/CCCD không được để trống';
                    }
                    if (trim($row['Họ và tên']) === '') {
                        $messages[] = 'Họ và tên học sinh không được để trống';
                    }

                    if (in_array($maSo, $dupeKeysInFile)) {
                        $messages[] = 'Trùng mã học sinh trong file';
                    }
                    if (in_array($soHieu, $dupeKeysInFile2)) {
                        $messages[] = 'Trùng số hiệu văn bằng trong file';
                    }
                    if (in_array($soVaoSo, $dupeKeysInFile3)) {
                        $messages[] = 'Trùng số vào sổ trong file';
                    }
                    if (in_array($cccd, $dupeKeysInFile4)) {
                        $messages[] = 'Trùng CMND/CCCD trong file';
                    }

                    if (in_array($maSo, $dupesInDB)) {
                        $messages[] = 'Đã tồn tại mã học sinh trên hệ thống';
                    }
                    if (in_array($soHieu, $dupesInDB2)) {
                        $messages[] = 'Đã tồn tại số hiệu văn bằng trên hệ thống';
                    }
                    if (in_array($soVaoSo, $dupesInDB3)) {
                        $messages[] = 'Đã tồn tại số vào sổ trên hệ thống';
                    }
                    if (in_array($cccd, $dupesInDB4)) {
                        $messages[] = 'Đã tồn tại CMND/CCCD trên hệ thống';
                    }

                    if ($danTocModel) {
                        $row['DanTocID'] = $danTocModel->id;
                    } else {
                        $messages[] = "Dân tộc '$danToc' không tồn tại trong hệ thống.";
                        $row['DanTocID'] = null;
                    }
                    if ($lopModel) {
                        $row['LopHocID'] = $lopModel->id;
                    } else {
                        $row['LopHocID'] = null;
                    }
                    if ($xepLoaiModel) {
                        $row['XepLoaiID'] = $xepLoaiModel->id;
                    } else {
                        $messages[] = "Xếp loại '$xepLoai' không tồn tại trong hệ thống.";
                        $row['XepLoaiID'] = null;
                    }
                    if ($cccdModel) {
                        $row['HocSinhID'] = $cccdModel->id;
                    } else {
                        $row['HocSinhID'] = null;
                    }
                    if ($gioiTinhModel) {
                        $row['GioiTinhID'] = $gioiTinhModel->id;
                    } else {
                        $row['GioiTinhID'] = null;
                    }
                    if ($tinhModel) {
                        $row['DiaBanHCID_Tinh'] = $tinhModel->id;
                    } else {
                        $row['DiaBanHCID_Tinh'] = null;
                    }
                    if ($donViModel) {
                        $row['DonViID_Hoc'] = $donViModel->id;
                    } else {
                        $row['DonViID_Hoc'] = null;
                    }
                    $row['CapBangTotNghiepID'] =$CapBangTotNghiepID;
                    $row['trangThai'] = count($messages) > 0 ? implode('; ', $messages) : 'Chờ nhận';
                }
                unset($row); // tránh lỗi reference
                session(['DSHocSinh_excel' => $assoc]);

                return response()->json(['Err' => false]);
            } catch (\Throwable $e) {
                return response()->json([
                    'Err' => true,
                    'Msg' => 'Lỗi đọc file: ' . $e->getMessage(),
                ], 500);
            }
        }
        /**
         * Trả về JSON để Tabulator preview
         */
        function parseExcelDate($value)
        {
            if (is_string($value)) {
                // Remove leading single quote (') and trim whitespace
                $value = ltrim(trim($value), "'");

                $formats = ['d/m/Y', 'd-m-Y', 'j/n/Y', 'Y-m-d'];
                foreach ($formats as $format) {
                    try {
                        return Carbon::createFromFormat($format, $value)->format('d/m/Y');
                    } catch (\Exception $e) {
                        continue;
                    }
                }
            }

            if (is_numeric($value)) {
                try {
                    return Carbon::instance(\PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($value))->format('d/m/Y');
                } catch (\Exception $e) {
                    return null;
                }
            }

            return null;
        }
        public function loadExcel()
        {
            $rows = collect(session('DSHocSinh_excel', []));
            $data = $rows->map(function(array $r) {
                // Xử lý loại bỏ dấu nháy đầu chuỗi cho toàn bộ giá trị
                $r = array_map(function ($val) {
                    if (is_string($val)) {
                        return ltrim(trim($val), "'");
                    }
                    return $val;
                }, $r);

                return [
                    'CapBangTotNghiepID'   => $r['CapBangTotNghiepID'] ?? null,
                    'HocSinhID'   => $r['HocSinhID'] ?? null,
                    'MaHocSinh'   => $r['Mã học sinh'] ?? null,
                    'TenHocSinh'   => $r['Họ và tên'] ?? null,
                    'CCCD'   => $r['Số CMND/CCCD'] ?? null,
                    'NgaySinh'   => $this->parseExcelDate($r['Ngày sinh'] ?? null),
                    'GioiTinhID'   => $r['GioiTinhID'] ?? null,
                    'TenGioiTinh'   => $r['Giới tính'] ?? null,
                    'DanTocID'   => $r['DanTocID'] ?? null,
                    'TenDanToc'   => $r['Dân tộc'] ?? null,
                    'NoiSinh'   => $r['Nơi sinh'] ?? null,
                    'LopHocID'   => $r['LopHocID'] ?? null,
                    'TenLop'   => $r['Lớp'] ?? null,
                    'DiaChi'   => $r['Địa chỉ'] ?? null,
                    'DiaBanHCID_Tinh'   =>  $r['DiaBanHCID_Tinh'] ?? null,
                    'TenTinh'   =>  $r['Tỉnh/thành phố'] ?? null,
                    'TenTruong'   => $r['Trường học'] ??  null,
                    'DonViID_TruongHoc'   => $r['DonViID_Hoc'] ??  null,
                    'GhiChu'   =>  null,
                    'XepLoaiID'   => $r['XepLoaiID'] ?? null,
                    'TenXepLoai'   => $r['Xếp loại'] ?? null,
                    'SoHieu'   => $r['Số hiệu'] ?? null,
                    'SoVaoSo'   => $r['Số vào sổ'] ?? null,
                    'trangThai'   => $r['trangThai'] ?? 'Chờ nhận',
                    ];
                });

            return response()->json($data);
        }
        /**
         * Nhập những dòng được chọn
         */
        public function importExcel(Request $request)
        {
            $selected = $request->input('rows', []);
            $all      = collect(session('DSHocSinh_excel', []));
            $out = [];
            $userId = auth()->id() ? new ObjectId(auth()->id()) : null;
            $now = now();
            // 3. Insert từng dòng một, có xử lý cha
            foreach ($selected as $idx) {
                $r = $all->get($idx - 1);

                if (!$r || !is_array($r)) {
                    $out[$idx] = ['Err' => true, 'Msg' => 'Dữ liệu hàng không tồn tại'];
                    continue;
                }
                if (empty($r['HocSinhID'])) {
                    $doiTuong = DoiTuong::create([
                        'MaDoiTuong'    => $r['Mã học sinh'],
                        'Hovaten'       => $r['Họ và tên'],
                        'Ngaysinh'      => $r['Ngày sinh'] ? Carbon::createFromFormat('d/m/Y', $r['Ngày sinh']) : null,
                        'Noisinh'       => $r['Nơi sinh'],
                        'Gioitinh'      => $this->safeObjectId($r['GioiTinhID']),
                        'CCCD'          => $r['Số CMND/CCCD'],
                        'NgayCap'       => null,
                        'DiaBanHCID_NoiCap' => null,
                        'DanTocID'      => $this->safeObjectId($r['DanTocID']),
                        'DiaBanHCID_Tinh' => null,
                        'DiaBanHCID_Xa' => null,
                        'DiaBanHCID_Thon' => null,
                        'DiaChi'        => $r['Địa chỉ'],
                        'SDT'           => null,
                        'Email'         => null,
                        'DonViID'       => $this->currentUser->donViId(),
                        'DonViID_Hoc'   => $this->safeObjectId($r['DonViID_Hoc']),
                        'GhiChu'        => null,
                        'TrangThai'     => true,
                        'UserID_ThaoTac'=> $userId,
                        'NgayThaoTac'   => $now,
                        'AnhDaiDien'    => null,
                        'DinhKem'       => null,
                        'LopHocID'      => $this->safeObjectId($r['LopHocID']),
                        'DienUuTienID'  => null,
                        'SoNha'         => null,
                        'TrangThai_TotNghiep' => '30',
                        'TrangThai_CapBang'   => '35',
                    ]);

                    // Cập nhật lại ID cho bước tiếp theo
                    $r['HocSinhID'] = $doiTuong->_id;
                }
            // Insert dòng chính
            CapBangTotNghiepCT::create([
                        'CapBangTotNghiepID'   => $this->safeObjectId($r['CapBangTotNghiepID']),
                        'HocSinhID'   => $this->safeObjectId($r['HocSinhID']),
                        'MaHocSinh'   => $r['Mã học sinh'],
                        'TenHocSinh'   => $r['Họ và tên'],
                        'CCCD'   => $r['Số CMND/CCCD'],
                        'NgaySinh'   => $r['Ngày sinh'] ? Carbon::createFromFormat('d/m/Y', $r['Ngày sinh']) : null,
                        'GioiTinhID'   => $this->safeObjectId($r['GioiTinhID']),
                        'DanTocID'   => $this->safeObjectId($r['DanTocID']),
                        'NoiSinh'   => $r['Nơi sinh'],
                        'LopHocID'   => $this->safeObjectId($r['LopHocID']),
                        'DiaChi'   => $r['Địa chỉ'],
                        'DiaBanHCID_Tinh'   => $this->safeObjectId($r['DiaBanHCID_Tinh']),
                        'DonViID_TruongHoc'   => $this->safeObjectId($r['DonViID_Hoc']),
                        'GhiChu'   => null,
                        'XepLoaiID'   => $this->safeObjectId($r['XepLoaiID']),
                        'SoHieu'   => $r['Số hiệu'],
                        'SoVaoSo'   => $r['Số vào sổ'],
                    ]);

                $out[$idx] = ['Err' => false];
            }
            return response()->json($out);
        }


        public function loadSheetNames(Request $request)
        {
            $path = $request->input('path');
            if (! $path) {
                return response()->json(['Err'=>true,'Msg'=>'Missing file path'], 422);
            }

            // 1) extract just the path part, e.g. "//storage/uploads/…"
            $urlPath = parse_url($path, PHP_URL_PATH);

            // 2) collapse multiple leading slashes: "/storage/uploads/…"
            $urlPath = preg_replace('#^/+/#','/', $urlPath);

            // 3) grab everything after "/storage/" → "uploads/…"
            $relative = Str::after($urlPath, '/storage/');

            // 4) now check on the public disk
            if (! Storage::disk('public')->exists($relative)) {
                return response()->json(['Err'=>true,'Msg'=>'File not found on disk'], 404);
            }

            $fullPath = Storage::disk('public')->path($relative);

            try {
                // create the best reader for this file
                $reader = IOFactoryNhanEx::createReaderForFile($fullPath);
                // only list sheet names, no data
                $sheetNames = $reader->listWorksheetNames($fullPath);
                $Result = array_map(fn($name) => ['TenSheet' => $name], $sheetNames);

                $payload = [
                    'CanhBao'     => false,
                    'Xem'         => false,
                    'Them'        => false,
                    'Sua'         => false,
                    'Xoa'         => false,
                    'InAn'        => false,
                    'Nap'         => false,
                    'Quyen1'      => false,
                    'Quyen2'      => false,
                    'Quyen3'      => false,
                    'Err'         => false,
                    'ErrCode'     => '',
                    'ErrCatch'    => '',
                    'Result'      => $Result,
                    'Msg'         => '',
                    'Logs'        => '',
                    'Redirect'    => false,
                    'RedirectUrl' => '',
                ];

                // encode as JSON string
                $json = json_encode($payload);

                // return it as a plain-text response
                return response($json, 200)
                    ->header('Content-Type', 'text/html');
            } catch (\Throwable $e) {
                // return the real exception message so we can see why it fails
                return response()->json([
                    'Err' => true,
                    'Msg' => 'Error reading file: ' . $e->getMessage()
                ], 500);
            }
        }
        public function downloadTemplate()
        {
            return Excel::download(
                new CapBangTotNghiepTemplateExport,
                'Mau_DSHocSinhCapBang.xlsx'
            );
        }
        #endregion
    }
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use PhpOffice\PhpSpreadsheet\Style\Color;   
class CapBangTotNghiepTemplateExport implements
 FromCollection,
 WithHeadings,
 WithColumnWidths,
 ShouldAutoSize,
 WithStyles,
 WithColumnFormatting
{
    public function collection()
    {
        return collect(); // Xuất file mẫu không có data
    }

    public function headings(): array
    {
        return [
            'Mã học sinh',     // Cột A
            'Họ và tên',       // Cột B
            'Số CMND/CCCD',    // Cột C
            'Ngày sinh',       // Cột D
            'Giới tính',       // Cột E
            'Dân tộc',         // Cột F
            'Nơi sinh',        // Cột G
            'Lớp',             // Cột H
                        'Trường học',       // Cột I
                        'Tỉnh/thành phố',       // Cột J
            'Địa chỉ',         // Cột K
            'Xếp loại',        // Cột L
            'Số hiệu',         // Cột M
            'Số vào sổ',       // Cột N
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 15,  // Mã học sinh
            'B' => 30,  // Họ và tên
            'C' => 25,  // Số CMND/CCCD
            'D' => 25,  // Ngày sinh
            'E' => 20,  // Giới tính
            'F' => 20,  // Dân tộc
            'G' => 30,  // Nơi sinh
            'H' => 20,  // Lớp
            'I' => 30,  // Trường học
            'J' => 20,  // Tỉnh/thành phố
            'K' => 30,  // Địa chỉ
            'L' => 20,  // Xếp loại
            'M' => 20,  // Số hiệu
            'N' => 20,  // Số vào sổ
        ];
    }
    public function columnFormats(): array
    {
        return [
            'A' => NumberFormat::FORMAT_TEXT,
            'B' => NumberFormat::FORMAT_TEXT,
            'C' => NumberFormat::FORMAT_TEXT,
            'D' => NumberFormat::FORMAT_TEXT,
            'E' => NumberFormat::FORMAT_TEXT,
            'F' => NumberFormat::FORMAT_TEXT,
            'G' => NumberFormat::FORMAT_TEXT,
            'H' => NumberFormat::FORMAT_TEXT,
            'I' => NumberFormat::FORMAT_TEXT,
            'J' => NumberFormat::FORMAT_TEXT,
            'K' => NumberFormat::FORMAT_TEXT,
            'L' => NumberFormat::FORMAT_TEXT,
            'M' => NumberFormat::FORMAT_TEXT,
            'N' => NumberFormat::FORMAT_TEXT,
        ];
    }
    public function styles(Worksheet $sheet)
    {
            // Áp dụng style cho dòng tiêu đề (dòng 1)
        $styles = [
            1 => [
                'font' => [
                    'bold' => true,
                    'name' => 'Times New Roman',
                    'size' => 13,
                ],
                'alignment' => [
                    'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                    'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                        'color' => ['argb' => '000000'],
                    ],
                ],
            ],
        ];

        // 👇 Thêm style riêng cho cột A1 (ô đầu tiên - "Mã học sinh") màu đỏ
        $sheet->getStyle('A1')->getFont()->getColor()->setARGB(Color::COLOR_RED);
        $sheet->getStyle('B1')->getFont()->getColor()->setARGB(Color::COLOR_RED);
        $sheet->getStyle('C1')->getFont()->getColor()->setARGB(Color::COLOR_RED);
        return $styles;
    }
}
#endregion
