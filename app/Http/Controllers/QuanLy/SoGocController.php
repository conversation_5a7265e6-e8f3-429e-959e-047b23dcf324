<?php

namespace App\Http\Controllers\QuanLy;

use Str;
use Storage;
use DateTime;
use Carbon\Carbon;
use App\Models\User;
use MongoDB\BSON\Regex;
use App\Services\ThongBao;
use App\Models\QuanLy\SoGoc;
use Illuminate\Http\Request;
use App\Models\DanhMuc\DonVi;
use App\Models\DanhMuc\KyThi;
use App\Services\DungChungDb;
use App\Models\DanhMuc\CapHoc;
use App\Models\DanhMuc\ChucVu;
use App\Models\DanhMuc\DanToc;
use App\Models\DanhMuc\LopHoc;
use App\Models\QuanLy\SoGocCT;
use App\Models\DanhMuc\KhoaThi;
use App\Models\DanhMuc\XepLoai;
use App\Models\QuanLy\DoiTuong;
use App\Services\DungChungNoDb;
use App\Models\DanhMuc\GioiTinh;
use App\Models\DanhMuc\NhanVien;
use App\Models\QuanLy\QuyetDinh;
use App\Models\DanhMuc\TrangThai;
use Illuminate\Support\Facades\DB;
use App\Models\DanhMuc\LoaiChungTu;
use App\Http\Controllers\Controller;
use App\Services\CurrentUserService;
use Illuminate\Support\Facades\Auth;
use App\Models\DanhMuc\HinhThucDaoTao;
use App\Services\OfficeConvertService;
use App\Models\DanhMuc\DiaBanHanhChinh;
use App\Models\QuanLy\CapBangTotNghiep;
use App\Models\QuanLy\TiepNhanPhoiVBCC;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpWord\TemplateProcessor;
use App\Models\QuanLy\CapBangTotNghiepCT;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use App\Models\DanhMuc\LoaiPhoiVanBangChungChi;
use MongoDB\BSON\ObjectId; // from mongodb/mongodb

class SoGocController extends Controller
{
    protected DungChungDb $dungChungDb;
    protected DungChungNoDb $logService;
    protected CurrentUserService $currentUser;

    public function __construct(DungChungDb $dungChungDb,CurrentUserService $currentUser, DungChungNoDb $logService)
    {
        $this->dungChungDb = $dungChungDb;
        $this->logService  = $logService;
        $this->currentUser  = $currentUser;
    }
    function safeObjectId($id) {
        return is_string($id) && preg_match('/^[a-f\d]{24}$/i', $id) ? new ObjectId($id) : null;
    }

    public function index()
    {
        return view('quanly.SoGoc.index' );
    }
    /**
     * Thêm mới hoặc cập nhật thông tin đia bàn
     */
    public function luuthongtin(Request $request)
    {
        try {
            $SoSoGoc =$request->input('SoSoGoc');
            $NgayKy = $request->filled('NgayKy')
            ? Carbon::createFromFormat('d/m/Y', $request->input('NgayKy'))
            : null;
            $NhanVienID_NguoiKy = $this->safeObjectId($request->input('NhanVienID_NguoiKy'));
            $ChucVuID_NguoiKy = $this->safeObjectId($request->input('ChucVuID_NguoiKy'));
            $HinhThucDaoTaoID = $this->safeObjectId($request->input('HinhThucDaoTaoID'));
            $CoQuanBanHanh =$request->input('CoQuanBanHanh');
            $QuyetDinhID = $this->safeObjectId($request->input('QuyetDinhID'));
            $NamTotNghiep = $request->input('NamTotNghiep');
            $DonViID_TruongHoc = $this->safeObjectId($request->input('DonViID_TruongHoc'));
            $SoGocID = $this->safeObjectId($request->input('SoGocID'));
            $TrichYeu = $request->input('TrichYeu');
            $DuongDanFileVB = $request->input('DuongDanFileVB') ? $request->input('DuongDanFileVB') : '';
        
            $userId = auth()->id() ? new ObjectId(auth()->id()) : null;
            $now = now();
        
            if ($request->isMethod('post')) {
               
            $paths =$this->logService->saveFiles($DuongDanFileVB,"SoGoc");

            $soGoc = SoGoc::create([
                    'SoSoGoc' => $SoSoGoc,
                    'NgayKy' => $NgayKy,
                    'NhanVienID_NguoiKy' => $NhanVienID_NguoiKy,
                    'ChucVuID_NguoiKy' => $ChucVuID_NguoiKy,
                    'HinhThucDaoTaoID' => $HinhThucDaoTaoID,
                    'CoQuanBanHanh' => $CoQuanBanHanh,

                    'QuyetDinhID' => $QuyetDinhID,
                    'NamTotNghiep' => $NamTotNghiep,
                    'DonViID_TruongHoc' => $DonViID_TruongHoc,
                    'DinhKem' => $paths,
                    'TrichYeu' => $TrichYeu,
                    'TrangThai_Giao' => "37",
                    'LaBanSao' => false,

                    'UserID_ThaoTac'   => $userId,
                    'UserID_CapNhat'   => $userId,
                    'NgayThaoTac'   => $now,
                    'NgayCapNhat'   => $now,
                ]);
                $this->logService->ghiLogs($request->all(), 'them', auth()->id(), null);
                return response()->json([
                    'Err' => false,
                    'Msg' => 'Thêm dữ liệu thành công!',
                    'Result'  => $this->formatSoGocs(collect([$soGoc]))->first()
                ]);
            }

            if ($request->isMethod('put') || $request->isMethod('patch')) {
                // Update
                $SoGoc = SoGoc::find($SoGocID);
                if (!$SoGoc) {
                    return response()->json([
                        'Err' => true,
                        'canhbao' => true,
                        'Msg' => 'Không tìm thấy dữ liệu cần cập nhật!'
                    ]);
                }       
                // ✅ Xóa các file đính kèm nếu có
                if (!empty($SoGoc->DinhKem)) {
                    $paths = explode('|', $SoGoc->DinhKem);
                    foreach ($paths as $path) {
                        // Xử lý path: bỏ /storage/ để về path thật trong public
                        $relativePath = str_replace('/storage/', 'public/', $path);
                        if (Storage::exists($relativePath)) {
                            Storage::delete($relativePath);
                        }
                    }
                }
                $paths = $this->logService->saveFiles($DuongDanFileVB,"SoGoc");

                $SoGoc->update([
                    'SoSoGoc' => $SoSoGoc,
                    'NgayKy' => $NgayKy,
                    'NhanVienID_NguoiKy' => $NhanVienID_NguoiKy,
                    'ChucVuID_NguoiKy' => $ChucVuID_NguoiKy,
                    'HinhThucDaoTaoID' => $HinhThucDaoTaoID,
                    'CoQuanBanHanh' => $CoQuanBanHanh,

                    'QuyetDinhID' => $QuyetDinhID,
                    'NamTotNghiep' => $NamTotNghiep,
                    'DonViID_TruongHoc' => $DonViID_TruongHoc,
                    'DinhKem' => $paths,
                    'TrichYeu' => $TrichYeu,
                    'LaBanSao' => false,
                    
                    'UserID_CapNhat'   => $userId,
                    'NgayCapNhat'   => $now,
                ]);
                $this->logService->ghiLogs($request->all(), 'sua', auth()->id(), null);
                return response()->json([
                    'Err' => false,
                    'Msg' => 'Cập nhật dữ liệu thành công!',
                    'Result'  =>$this->formatSoGocs(collect([$SoGoc]))->first()//$SoGoc->_id
                ]);
            }

            // Nếu không phải POST hoặc PUT
            return response()->json([
                'Err' => true,
                'canhbao' => true,
                'Msg' => 'Phương thức HTTP không hợp lệ!'
            ]);

        } catch (\Exception $e) {
            $this->logService->ghiLogs($request->all(), $request->isMethod('put') ? 'sua' : 'them', auth()->id(), null);
            $code = $this->dungChungDb->insertMaCodeLoi($e);
            return response()->json([
                'Err' => true,
                'Msg' => 'Có lỗi xảy ra!',
                'debug' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Lấy toàn bộ đia bàn dưới dạng JSON
     */
    public function getAll(Request $request)
    {
        try {
            $query = SoGoc::query()->where('LaBanSao', false);

            if ($request->filled('QuyetDinhID')) {
                $query->where('QuyetDinhID', new ObjectId($request->input('QuyetDinhID')));
            }
            if ($request->filled('NamTotNghiep')) {
                $query->where('NamTotNghiep', $request->input('NamTotNghiep'));
            }
            if ($request->filled('CapHocID')) {
                $capHocID = $request->input('CapHocID');
                $kyThiIDs = KyThi::where('CapHocID', $capHocID)->get()
                    ->pluck('_id')
                    ->filter(fn($id) => !empty($id)) // lọc bỏ null, rỗng
                    ->map(fn($id) => $id instanceof ObjectId ? $id : new ObjectId((string) $id));
                // Gắn vào query chính
                $query->whereIn('KyThiID', $kyThiIDs);
            }
            if ($request->filled('TrangThai')) {
                $query->where('TrangThai_Giao', $request->input('TrangThai'));
            }
            $searchKey = null;
            if ($request->filled('SearchKey')) {
                $searchKey = mb_strtolower($request->input('SearchKey')); // chuẩn hóa để so sánh không phân biệt hoa thường
            }
            // Sắp xếp theo field được chọn (nếu có), mặc định là 'MaSoGoc'
            $sortField = $request->input('CbSapXep', 'NamTotNghiep');
            $query->orderBy($sortField, 'asc');
            
            // Lấy dữ liệu DonViID
            $arrDonViID = $this->logService->LayMaDonViTheoUserID();
            // Lấy danh sách _id của User thuộc các đơn vị
            $userIds = User::whereIn('DonViID', $arrDonViID)
               ->pluck('id')
               ->map(fn($id) => new ObjectId($id)) // đảm bảo là ObjectId nếu cần
               ->toArray();
            // Truy tiếp theo UserID_ThaoTac nằm trong danh sách đó
            $result = $query->whereIn('UserID_ThaoTac', $userIds)->get();

            $result = $this->formatSoGocs($result);
            if ($searchKey) {
                $result = $result->filter(function ($item) use ($searchKey) {
                    $fieldsToSearch = [
                        $item->TenQuyetDinh ?? '',
                        $item->TenKyThi ?? '',
                        $item->TenCapHoc ?? '',
                        $item->TenTrangThai_Giao ?? '',
                        $item->TenDonVi ?? '',
                        $item->TenHinhThuc ?? '',
                        $item->TenChucVu ?? '',
                        $item->NguoiKy ?? '',
                        $item->TenChucVuGiao ?? '',
                        $item->TenNhanVienGiao ?? '',
                        $item->SoSoGoc ?? '',
                    ];

                    foreach ($fieldsToSearch as $value) {
                        if (mb_stripos($value, $searchKey) !== false) {
                            return true;
                        }
                    }
                    return false;
                })->values(); // reset key để không bị lỗ hổng index
            }
        // Map lại từng dòng để thêm Tên Cha
            return response()->json([
                'Err'    => false,
                'result' => $result,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err'   => true,
                'Msg'   => 'Không thể lấy danh sách!',
                'debug' => $e->getMessage(),
            ]);
        }
    }
    /**
     * Tải dữ liệu cho form sửa
     */
    public function loadDuLieuSua(Request $request)
    {
        $id = $request->input('id');
        $SoGoc = SoGoc::find($id);
        if (!$SoGoc) {
            return response()->json([
                'Err' => true,
                'canhbao' => true,
                'Msg' => 'Không tìm thấy dữ liệu!'
            ]);
        }
        
        $SoGoc = $this->formatSoGocs(collect([$SoGoc]))->first(); // Map 1 item rồi lấy lại object

        return response()->json([
            'Err'    => false,
            'Result' => $SoGoc
        ]);
    }
    /**
     * Tải dữ liệu cho form sửa
     */
    public function loadDuLieuSuact(Request $request)
    {
        $id = $request->input('id');
        $SoGoc = SoGocCT::find($id);

        if (!$SoGoc) {
            return response()->json([
                'Err' => true,
                'canhbao' => true,
                'Msg' => 'Không tìm thấy dữ liệu!'
            ]);
        }

        // Lấy thông tin học sinh
        $hocSinh = DoiTuong::find($SoGoc->DoiTuongID_HocSinh);
        $tenHocSinh = $hocSinh->Hovaten ?? 'Không rõ';
        $tenTruong = optional(DonVi::find($hocSinh->DonViID_Hoc))->TenDonVi ?? 'Không rõ';

        // Gắn thêm vào model (nếu muốn bạn có thể trả riêng)
        $SoGoc->TenHocSinh = $tenHocSinh;
        $SoGoc->TenTruong = $tenTruong;

        return response()->json([
            'Err'    => false,
            'Result' => $SoGoc
        ]);
    }
   /**
     * Xóa
     */
    public function xoa(Request $request)
    {
        try {
            $id = $request->input('ma');
            $model = SoGoc::whereKey($id)->firstOrFail();
            // ✅ Xoá các bản ghi chi tiết liên quan (SoGocCT)
            SoGocCT::where('SoGocID', $model->_id)->delete(); // hoặc (string)$model->_id nếu cần
            // ✅ Xóa file đính kèm nếu có
            if (!empty($model->DinhKem)) {
                $paths = [];
    
                // Xử lý chuỗi đường dẫn file (dùng | hoặc , làm dấu ngăn cách)
                if (is_string($model->DinhKem)) {
                    $paths = preg_split('/[|,]/', $model->DinhKem);
                }
    
                foreach ($paths as $path) {
                    $cleanPath = str_replace('\\', '/', trim($path));
    
                    // Loại bỏ '/storage/' => còn lại: uploads/...
                    $relativePath = str_replace('/storage/', '', $cleanPath);
    
                    // Laravel file system path: storage/app/public/...
                    $fullStoragePath = 'public/' . $relativePath;
                    if (Storage::disk('public')->exists($relativePath)) {
                        Storage::disk('public')->delete($relativePath);
                    } else {
                        Log::warning("Không tìm thấy file để xoá: $fullStoragePath");
                    }
                }
            }
    
            // ✅ Xoá dữ liệu gốc
            $model->delete();
    
            return response()->json([
                'Err' => false,
                'Msg' => 'Xóa dữ liệu và file đính kèm thành công!'
            ]);
    
        } catch (\Exception $e) {
            $code = $this->dungChungDb->insertMaCodeLoi($e);
    
            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }

    function parseNgayKy($input): ?DateTime {
        if ($input instanceof \MongoDB\BSON\UTCDateTime) {
            return $input->toDateTime();
        }
        if ($input instanceof \DateTimeInterface) {
            return $input;
        }
        if (is_string($input)) {
            $dt = DateTime::createFromFormat('d/m/Y', $input);
            if ($dt) return $dt;
    
            try {
                return new DateTime($input);
            } catch (Exception $e) {
                return null;
            }
        }
        return null;
    }
    public function formatSoGocs($collection)
    {
        return $collection->map(function ($item) {
            // 1. Lấy quyết định
        $qd = QuyetDinh::with(['hinhThucDaoTao'])
        ->find($item->QuyetDinhID);
            if ($qd && !empty($qd->NgayKy)) {
                $date = $this->parseNgayKy($qd->NgayKy);
                $ngay = $date->format('d');
                $thang = $date->format('m');
                $nam = $date->format('Y');

                $item->TenQuyetDinh = "Quyết định công nhận tốt nghiệp số {$qd->SoQuyetDinh} ngày {$ngay} tháng {$thang} năm {$nam}";
                $item->TenKyThi = optional(KyThi::find($qd->KyThiID))->TenKyThi ?? '';
                $item->TenCapHoc = optional(CapHoc::find(optional(KyThi::find($qd->KyThiID))->CapHocID))->tenCapHoc ?? '';
            } elseif ($qd) {
                $item->TenQuyetDinh = "Quyết định công nhận tốt nghiệp số {$qd->SoQuyetDinh} ngày ... tháng ... năm ...";
                $item->TenKyThi = optional(KyThi::find($qd->KyThiID))->TenKyThi ?? '';
                $item->TenCapHoc = optional(CapHoc::find(optional(KyThi::find($qd->KyThiID))->CapHocID))->tenCapHoc ?? '';

            } else {
                $item->TenKyThi ='';
                $item->TenCapHoc ='';
                $item->TenQuyetDinh = '';
            }      
             // Lấy thông tin trạng thái cấp bằng
           
            $trangThai_Giao = TrangThai::where("MaTrangThai", $item->TrangThai_Giao)->first();
            $item->TenTrangThai_Giao = optional($trangThai_Giao)->TenTrangThai ?? 'Chưa xác định';
            $item->MauSac_Giao = optional($trangThai_Giao)->MauSac ?? '#ffffff';
            $item->Ma_Giao = optional($trangThai_Giao)->MaTrangThai ?? '';

            $item->txtNgayKy = optional($item->NgayKy)->format('d/m/Y') ?? '';
            $item->txtNgayGiao = optional($item->NgayGiao)->format('d/m/Y') ?? '';
            $item->TenDonVi = optional(DonVi::find($item->DonViID_TruongHoc))->TenDonVi ?? '';
            $item->TenHinhThuc = optional(HinhThucDaoTao::find($item->HinhThucDaoTaoID))->tenHinhThucDaoTao ?? '';
            $item->TenChucVu = optional(ChucVu::find($item->ChucVuID_NguoiKy))->tenChucVu ?? '';
            $item->NguoiKy = optional(NhanVien::find($item->NhanVienID_NguoiKy))->tenNhanVien ?? '';
            $item->TenChucVuGiao = optional(ChucVu::find($item->ChucVuID_Giao))->tenChucVu ?? '';
            $item->TenNhanVienGiao = optional(NhanVien::find($item->NhanVienID_Giao))->tenNhanVien ?? '';
            return $item;
        });
    }

        /**
     * Thêm mới hoặc cập nhật thông tin đia bàn
     */
    public function luuthongtinct(Request $request)
    {
        try {
            $DoiTuongID_HocSinh = $this->safeObjectId($request->input('DoiTuongID_HocSinh'));
            $XepLoaiID = $this->safeObjectId($request->input('XepLoaiID'));
            $DiemThi = $request->input('DiemThi');
            $SoVaoSoGoc = $request->input('SoVaoSoGoc');
            $SoHieuVanBang = $request->input('SoHieuVanBang');
            $GhiChu = $request->input('GhiChu');
            $SoGocID = $this->safeObjectId($request->input('SoGocID'));

            $SoGocCTID = $this->safeObjectId($request->input('SoGocCTID'));
        
            $MaHocSinh = $request->input('MaHocSinh');
            $TenHocSinh = $request->input('TenHocSinh');
            $CCCD = $request->input('CCCD');
            $NgaySinh = $request->filled('NgaySinh')
            ? Carbon::createFromFormat('d/m/Y', $request->input('NgaySinh'))
            : null;;
            $GioiTinhID = $this->safeObjectId($request->input('GioiTinhID'));
            $DanTocID = $this->safeObjectId($request->input('DanTocID'));
            $NoiSinh = $request->input('NoiSinh');
            $LopHocID = $this->safeObjectId($request->input('LopHocID'));
            $DiaChi = $request->input('DiaChi');
            $DiaBanHCID_Tinh = $this->safeObjectId($request->input('DiaBanHCID_Tinh'));

            $userId = auth()->id() ? new ObjectId(auth()->id()) : null;
            $now = now();
        
            if ($request->isMethod('post')) {           
            SoGocCT::create([
                    'SoGocID' => $SoGocID,
                    'DoiTuongID_HocSinh' => $DoiTuongID_HocSinh,
                    'XepLoaiID' => $XepLoaiID,
                    'DiemThi' => $DiemThi,
                    'SoVaoSoGoc' => $SoVaoSoGoc,
                    'SoHieuVanBang' => $SoHieuVanBang,
                    'GhiChu' => $GhiChu,
                    
                    'MaHocSinh' => $MaHocSinh,
                    'TenHocSinh' => $TenHocSinh,
                    'CCCD' => $CCCD,
                    'NgaySinh' => $NgaySinh,
                    'GioiTinhID' => $GioiTinhID,
                    'DanTocID' => $DanTocID,
                    'NoiSinh' => $NoiSinh,
                    'LopHocID' => $LopHocID,
                    'DiaChi' => $DiaChi,
                    'DiaBanHCID_Tinh' => $DiaBanHCID_Tinh,

                    'UserID_ThaoTac'   => $userId,
                    'UserID_CapNhat'   => $userId,
                    'NgayThaoTac'   => $now,
                    'NgayCapNhat'   => $now,
                ]);
                $this->logService->ghiLogs($request->all(), 'them', auth()->id(), null);
                return response()->json([
                    'Err' => false,
                    'Msg' => 'Thêm dữ liệu thành công!'
                ]);
            }

            if ($request->isMethod('put') || $request->isMethod('patch')) {
                // Update
                $SoGocCT = SoGocCT::find($SoGocCTID);
                if (!$SoGocCT) {
                    return response()->json([
                        'Err' => true,
                        'canhbao' => true,
                        'Msg' => 'Không tìm thấy dữ liệu cần cập nhật!'
                    ]);
                }       
               
                $SoGocCT->update([
                    'SoGocID' => $SoGocID,
                    'DoiTuongID_HocSinh' => $DoiTuongID_HocSinh,
                    'XepLoaiID' => $XepLoaiID,
                    'DiemThi' => $DiemThi,
                    'SoVaoSoGoc' => $SoVaoSoGoc,
                    'SoHieuVanBang' => $SoHieuVanBang,
                    'GhiChu' => $GhiChu,

                     'MaHocSinh' => $MaHocSinh,
                    'TenHocSinh' => $TenHocSinh,
                    'CCCD' => $CCCD,
                    'NgaySinh' => $NgaySinh,
                    'GioiTinhID' => $GioiTinhID,
                    'DanTocID' => $DanTocID,
                    'NoiSinh' => $NoiSinh,
                    'LopHocID' => $LopHocID,
                    'DiaChi' => $DiaChi,
                    'DiaBanHCID_Tinh' => $DiaBanHCID_Tinh,

                    'UserID_CapNhat'   => $userId,
                    'NgayCapNhat'   => $now,
                ]);
                $this->logService->ghiLogs($request->all(), 'sua', auth()->id(), null);
                return response()->json([
                    'Err' => false,
                    'Msg' => 'Cập nhật dữ liệu thành công!'
                ]);
            }

            // Nếu không phải POST hoặc PUT
            return response()->json([
                'Err' => true,
                'canhbao' => true,
                'Msg' => 'Phương thức HTTP không hợp lệ!'
            ]);

        } catch (\Exception $e) {
            $this->logService->ghiLogs($request->all(), $request->isMethod('put') ? 'sua' : 'them', auth()->id(), null);
            $code = $this->dungChungDb->insertMaCodeLoi($e);
            return response()->json([
                'Err' => true,
                'Msg' => 'Có lỗi xảy ra!',
                'debug' => $e->getMessage(),
            ]);
        }
    }
    /**
     * Lấy toàn bộ đia bàn dưới dạng JSON
     */
    public function getallct(Request $request)
    {
        try {
            // 1. Truy vấn SoGocCT theo SoGocID
            $query = SoGocCT::query();
            $query->where('SoGocID', new ObjectId($request->input('SoGocID')));
            $result = $query->get();
            $result = $result->values()->map(function ($item, $index) {
                            $item['STT'] = $index + 1;
                            $item['txtNgaySinh'] = optional($item->NgaySinh)->format('d/m/Y') ?? '';
                            $item['TenGioiTinh'] = optional(GioiTinh::find($item->GioiTinhID))->TenGioiTinh ?? '';
                            $item['TenDanToc'] = optional(DanToc::find($item->DanTocID))->tenDanToc ?? '';
                            $item['TenLop'] = optional(LopHoc::find($item->LopHocID))->TenLopHoc ?? '';
                            $item['TenXepLoai'] = optional(XepLoai::find($item->XepLoaiID))->tenXepLoai ?? '';
                            return $item;
                        });
            return response()->json([
                'Err'    => false,
                'result' => $result,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err'   => true,
                'Msg'   => 'Không thể lấy danh sách!',
                'debug' => $e->getMessage(),
            ]);
        }
    }


    public function formatDoiTuongs($collection)
    {
        return $collection->map(function ($item) {
            // 2. Format ngày sinh
            $item->txtNgaysinh = optional($item->Ngaysinh)->format('d/m/Y') ?? '';

            // 3. Format ngày cấp
            $item->txtNgayCap = optional($item->NgayCap)->format('d/m/Y') ?? '';

            // 4. DonViID_Hoc
            $item->TenDonVi = optional(DonVi::find($item->DonViID_Hoc))->TenDonVi ?? '';

            // 5. DiaBanHCID_NoiCap
            $item->TenNoiCap = optional(DiaBanHanhChinh::find($item->DiaBanHCID_NoiCap))->TenDiaBan ?? '';

            // 6. DanTocID
            $item->TenDanToc = optional(DanToc::find($item->DanTocID))->tenDanToc ?? '';
            // 6. DanTocID
            $item->TenGioiTinh = optional(GioiTinh::find($item->Gioitinh))->TenGioiTinh ?? '';
            // 7. DiaBanHCID_Tinh
            $item->Tinh = optional(DiaBanHanhChinh::find($item->DiaBanHCID_Tinh))->TenDiaBan ?? '';
           
            // 8. DiaBanHCID_Xa
            $item->Xa = optional(DiaBanHanhChinh::find($item->DiaBanHCID_Xa))->TenDiaBan ?? '';

            // 9. DiaBanHCID_Thon
            $item->Thon = optional(DiaBanHanhChinh::find($item->DiaBanHCID_Thon))->TenDiaBan ?? '';

            return $item;
        });
    }
    public function xoact(Request $request)
    {
        try {
            $id = $request->input('ma');
            $model = SoGocCT::whereKey($id)->firstOrFail();
            
            // ✅ Xoá dữ liệu gốc
            $model->delete();
    
            return response()->json([
                'Err' => false,
                'Msg' => 'Xóa dữ liệu và file đính kèm thành công!'
            ]);
    
        } catch (\Exception $e) {
            $code = $this->dungChungDb->insertMaCodeLoi($e);
    
            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }
    public function LayThongTinThongKeXepLoai(Request $request)
    {
        $soGocID = new ObjectId($request->input('SoGocID'));

        // Bước 1: Thống kê số lượng theo XepLoaiID
        $thongKe = SoGocCT::raw(function ($collection) use ($soGocID) {
            return $collection->aggregate([
                ['$match' => ['SoGocID' => $soGocID]],
                ['$group' => [
                    '_id' => '$XepLoaiID',
                    'SoLuong' => ['$sum' => 1],
                ]],
            ]);
        });

        // Tổng cộng tất cả số lượng
        $tongCong = collect($thongKe)->sum('SoLuong');

        // Đưa thống kê về map: [XepLoaiID => SoLuong]
        $thongKeMap = collect($thongKe)->mapWithKeys(function ($item) {
            return [(string) $item['_id'] => $item['SoLuong']];
        });

        // Bước 2: Lấy toàn bộ danh sách XepLoai
        $xepLoaiList = XepLoai::all();

        // Bước 3: Gộp dữ liệu và thêm cột TongCong
        $ketQua = $xepLoaiList->map(function ($xepLoai) use ($thongKeMap, $tongCong) {
            $idStr = (string) $xepLoai->_id;
            return [
                'XepLoaiID'   => $idStr,
                'TenXepLoai'  => $xepLoai->tenXepLoai ?? '',
                'MaXepLoai'   => $xepLoai->maXepLoai ?? '',
                'SoLuong'     => $thongKeMap->get($idStr, 0),
                'TongCong'    => $tongCong,
            ];
        })->sortBy('MaXepLoai')->values();

        return response()->json($ketQua);
    }
    public function LayMaTuTang(Request $request) {
        try {
            $kyhieuLoaiPhieu=$request->input('kyhieuLoaiPhieu');
            $bangDuLieu=$request->input('bangDuLieu');
            $cotDuLieu=$request->input('cotDuLieu');
            // 1) Tra cứu loại chứng từ
            $loai = LoaiChungTu::where('DangSD', true)
                ->where('MaLoaiChungTu', $kyhieuLoaiPhieu)
                ->first();
            // 2) Xây dựng tiền tố/hậu tố
            $user    = Auth::user();
            $donVi   = DonVi::find($user->DonViID);
            $maDonVi = $donVi->MaDonVi ?? '';
            $nienDo  = $request->session()->get('nien_do', '');

            $prefix = str_replace('MaDonVi', $maDonVi, $loai->KyHieuPhiaTruoc);
            $suffix = str_replace('NienDo',   $nienDo,  $loai->KyHieuPhiaSau);

            // 3) Chuẩn bị regex an toàn với delimiter tùy chỉnh (#) và tùy chọn UTF-8
            $delim   = '#';
            $pattern = '^'
                . preg_quote($prefix, $delim)
                . '.*'
                . preg_quote($suffix, $delim)
                . '$';

            // 4) Truy vấn Mongo để tìm các mục đã tồn tại khớp với tiền tố…hậu tố
            $client     = DB::connection('mongodb')->getMongoClient();
            $database   = config('database.connections.mongodb.database');
            $collection = $client->selectCollection($database, $bangDuLieu);

            $cursor = $collection->find([
                $cotDuLieu => [
                    '$regex'   => $pattern,
                    '$options' => 'u',
                ]
            ], [
                'projection' => [$cotDuLieu => 1]
            ]);

            // 5) Trích xuất phần số và theo dõi giá trị lớn nhất
            $maxNum = 0;
            foreach ($cursor as $doc) {
                $val = $doc[$cotDuLieu] ?? '';
                // sử dụng mb_strlen / mb_substr để ký tự đa byte không làm hỏng offset
                $lenVal   = mb_strlen($val);
                $lenPre   = mb_strlen($prefix);
                $lenSuf   = mb_strlen($suffix);
                $inner    = mb_substr($val, $lenPre, $lenVal - $lenPre - $lenSuf);

                if (ctype_digit($inner)) {
                    $maxNum = max($maxNum, (int) $inner);
                }
            }

            // 6) Tính toán số thứ tự tiếp theo, thêm số 0 để đạt độ dài cấu hình
            $next    = $maxNum + 1;
            $padLen  = max($loai->ChieuDaiChuoiTT, mb_strlen((string)$next));
            $numPart = str_pad((string)$next, $padLen, '0', STR_PAD_LEFT);

            // 7) Trả về số chứng từ đã được lắp ráp đầy đủ
            return response()->json([
                'SoChungTu' => $prefix . $numPart . $suffix,
                'KyTuDau' => $prefix,
                'SoTuTang' => $numPart,
            ]);

            return response()->json(['Err' => true]);
        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }
    public function Update_SoVaoSoGoc(Request $request)
    {
        $id = new ObjectId($request->input('id'));
        $kyTuDau = trim($request->input('KyTuDauTien') ?? '');
        $soTuTang = intval($request->input('SoTuTang'));
    
        if (!$kyTuDau || $soTuTang <= 0) {
            return response()->json(['error' => 'Dữ liệu không hợp lệ.'], 400);
        }
    
        $record = SoGocCT::find($id);
        if (!$record) {
            return response()->json(['error' => 'Không tìm thấy bản ghi.'], 404);
        }
    
        // Lặp để tìm SoVaoSoGoc chưa trùng
        do {
            $maSo = $kyTuDau . str_pad($soTuTang, 4, '0', STR_PAD_LEFT); // Ví dụ: ABC0001
            $tontai = SoGocCT::where('SoVaoSoGoc', $maSo)->exists();
    
            if (!$tontai) {
                $record->SoVaoSoGoc = $maSo;
                $record->save();
                return response()->json(['success' => true, 'SoVaoSoGoc' => $maSo, 'SoTuTangKeTiep' => $soTuTang + 1]);
            }
    
            $soTuTang++;
        } while (true); // Dừng khi không trùng và đã update
    }  
    public function Update_GiaoThuHoi(Request $request)
    {
        try {
            $TrangThai =$request->input('TrangThai');
            $Ngay = $request->filled('Ngay') ? Carbon::createFromFormat('d/m/Y', $request->input('Ngay')): null;
            $NhanVienID = $this->safeObjectId($request->input('NhanVienID'));
            $ChucVuID = $this->safeObjectId($request->input('ChucVuID'));
            $NoiDung =$request->input('NoiDung');
            $SoGocID = $this->safeObjectId($request->input('SoGocID'));
        
                // Update
                $SoGoc = SoGoc::find($SoGocID);
                if (!$SoGoc) {
                    return response()->json([
                        'Err' => true,
                        'canhbao' => true,
                        'Msg' => 'Không tìm thấy dữ liệu cần cập nhật!'
                    ]);
                }       
                if($TrangThai=="37"){
                    $SoGoc->update([
                        'TrangThai_Giao' => $TrangThai,
                        'NgayThuHoi' => $Ngay,
                        'NhanVienID_ThuHoi' => $NhanVienID,
                        'ChucVuID_ThuHoi' => $ChucVuID,
                        'NoiDung_ThuHoi' => $NoiDung,
                        'NgayGiao' => null,
                        'NhanVienID_Giao' => "",
                        'ChucVuID_Giao' => "",
                        'NoiDung_Giao' => "",
                    ]);
                    $this->logService->ghiLogs($request->all(), 'sua', auth()->id(), null);
                    return response()->json([
                        'Err' => false,
                        'Msg' => 'Thực hiện giao sổ gốc thành công!',
                    ]);
                }
                else
                {
                    $SoGoc->update([
                        'TrangThai_Giao' => $TrangThai,
                        'NgayGiao' => $Ngay,
                        'NhanVienID_Giao' => $NhanVienID,
                        'ChucVuID_Giao' => $ChucVuID,
                        'NoiDung_Giao' => $NoiDung,

                        'NgayThuHoi' => null,
                        'NhanVienID_ThuHoi' => "",
                        'ChucVuID_ThuHoi' => "",
                        'NoiDung_ThuHoi' => "",
                    ]);
                    $this->logService->ghiLogs($request->all(), 'sua', auth()->id(), null);
                    return response()->json([
                        'Err' => false,
                        'Msg' => 'Thực hiện giao sổ gốc thành công!',
                    ]);
                };
        } catch (\Exception $e) {
            $this->logService->ghiLogs($request->all(), $request->isMethod('put') ? 'sua' : 'them', auth()->id(), null);
            $code = $this->dungChungDb->insertMaCodeLoi($e);
            return response()->json([
                'Err' => true,
                'Msg' => 'Có lỗi xảy ra!',
                'debug' => $e->getMessage(),
            ]);
        }
    }
    public function XuatExcelSoGoc(Request $request)
    {
        try {
            $soGocID = new ObjectId($request->SoGocID);
            $username = auth()->user()->username ?? 'unknown';
            $folder = 'SoGoc';
            $fileName = "DanhSach_SoGoc_{$username}.xlsx";
            $exportFolder = "XuatExcel/{$folder}/{$username}";

            // Đường dẫn thư mục vật lý để lưu file Excel
            $storageExcelPath = storage_path("app/public/{$exportFolder}");

            // Tạo thư mục nếu chưa tồn tại
            if (!file_exists($storageExcelPath)) {
                mkdir($storageExcelPath, 0775, true);
            }

            // Đường dẫn file mẫu Excel
            $templatePath = public_path('ExcelMau/MauSoGoc.xlsx');
            if (!file_exists($templatePath)) {
                return response()->json(['Err' => true, 'Msg' => 'Không tìm thấy file mẫu Excel.']);
            }

            // Load file mẫu
            $spreadsheet = IOFactory::load($templatePath);
            $sheet = $spreadsheet->getActiveSheet();

            // Lấy dữ liệu
            $ds = SoGoc::find($soGocID);
            $qd = QuyetDinh::find($ds->QuyetDinhID);
            $kt = KyThi::find($qd->KyThiID);
            $dsCT = SoGocCT::where('SoGocID', $soGocID)->get();
            $dsHocSinh = DoiTuong::whereIn('_id', $dsCT->pluck('DoiTuongID_HocSinh')->filter())->get()->keyBy('_id');

            $sheet->getPageSetup()->setPrintArea("A1:J500");
            $sheet->getStyle("A1:J500")->getFont()->setName('Times New Roman')->setSize(13);
            // Ghi dữ liệu
            $sheet->setCellValue("A1", mb_strtoupper($ds->CoQuanBanHanh ?? "", 'UTF-8'));
            $sheet->setCellValue("A4", mb_strtoupper("SỔ GỐC CẤP BẰNG TỐT NGHIỆP " . (optional(CapHoc::find($kt->CapHocID))->tenCapHoc ?? ''), 'UTF-8'));
            $sheet->setCellValue("B6", "Quyết định công nhận tốt nghiệp số {$qd->SoQuyetDinh}, ngày {$qd->NgayKy}");
            $sheet->setCellValue("B7", "Học sinh trường: " . (optional(DonVi::find($ds->DonViID_TruongHoc))->TenDonVi ?? ''));
            $sheet->setCellValue("H6", "Năm tốt nghiệp: " . $ds->NamTotNghiep);
            $sheet->setCellValue("H7", "Hình thức đào tạo: " . (optional(HinhThucDaoTao::find($qd->HinhThucID))->tenHinhThucDaoTao ?? ''));

            $startRow = 10;
            $stt = 1;
            foreach ($dsCT as $ct) {
                $hocSinh = $dsHocSinh[(string)$ct->DoiTuongID_HocSinh] ?? null;

                $sheet->setCellValue("A{$startRow}", $stt);
                $sheet->setCellValue("B{$startRow}", $hocSinh->Hovaten ?? '');
                $sheet->setCellValue("C{$startRow}", optional($hocSinh->Ngaysinh)->format('d/m/Y') ?? '');
                $sheet->setCellValue("D{$startRow}", $hocSinh->Noisinh ?? '');
                $sheet->setCellValue("E{$startRow}", optional(GioiTinh::find($hocSinh->Gioitinh))->TenGioiTinh ?? '');
                $sheet->setCellValue("F{$startRow}", optional(DanToc::find($hocSinh->DanTocID))->tenDanToc ?? '');
                $sheet->setCellValue("G{$startRow}", optional(XepLoai::find($ct->XepLoaiID))->tenXepLoai ?? '');
                $sheet->setCellValue("H{$startRow}", $ct->SoHieuVanBang);
                $sheet->setCellValue("I{$startRow}", $ct->SoVaoSoGoc);
                $sheet->setCellValue("J{$startRow}", $ct->GhiChu);
                // 👉 THÊM VIỀN KẺ SAU MỖI DÒNG
                $sheet->getStyle("A{$startRow}:J{$startRow}")->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

                $startRow++;
                $stt++;
            }

            // Căn giữa dòng cuối (optional)
            $footerRow = $startRow + 5;
            $sheet->getStyle("A{$footerRow}:E{$footerRow}")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

            // Ghi file Excel
            $filePath = "{$storageExcelPath}/{$fileName}";
            $writer = IOFactory::createWriter($spreadsheet, 'Xlsx');
            $writer->save($filePath);

            // Gọi hàm chuyển sang PDF
            $relativeExcelPath = "storage/{$exportFolder}/{$fileName}"; // dùng public_path()
            $pdfSuccess = \App\Services\OfficeConvertService::XlsxToPdf($relativeExcelPath, 'L');

            $returnPath = $pdfSuccess
                ? str_replace('.xlsx', '.pdf', $relativeExcelPath)
                : $relativeExcelPath;

            return response()->json([
                'Err' => false,
                'Result' => $returnPath,
                'canhbao' => !$pdfSuccess,
                'Msg' => $pdfSuccess ? null : 'Không thể chuyển sang PDF, trả lại file Excel.',
            ]);
        } catch (\Exception $e) {
            Log::error('XuatExcelSoGoc error: ' . $e->getMessage());
            return response()->json([
                'Err' => true,
                'Msg' => 'Có lỗi xảy ra!',
                'debug' => $e->getMessage(),
            ]);
        }
    }

    public function GetListDonViCoHocSinh(Request $request)
    {
        try {
            $QuyetDinhID = $request->input('QuyetDinhID');

            // Bước 1: Lấy danh sách HocSinhID từ bảng HocSinhTN (theo QuyetDinhID)
            $quyetDinhID = new ObjectId($request->input('QuyetDinhID'));
            $capBangIDs = CapBangTotNghiep::where('QuyetDinhID', $quyetDinhID)->where('BanSao', false)
                ->pluck('_id')
                ->toArray();
                $hocSinhIDs = CapBangTotNghiepCT::whereIn('CapBangTotNghiepID', $capBangIDs)
                ->raw(function ($collection) {
                    return $collection->distinct('HocSinhID', [
                        'HocSinhID' => ['$ne' => null]
                    ]);
                });

            // Bước 2: Nếu có SoGocID, loại bỏ các học sinh đã có trong SoGocCT
            if ($request->filled('SoGocID')) {

                $soGocID = new ObjectId($request->SoGocID);

                $hocSinhDaCo = SoGocCT::where('SoGocID', $soGocID)
                    ->pluck('DoiTuongID_HocSinh')
                    ->toArray();

                // Loại bỏ những học sinh đã có
                //$hocSinhIDs = array_values(array_diff($hocSinhIDs, $hocSinhDaCo));
            }

            // Bước 3: Truy vấn DoiTuong theo danh sách HocSinhID sau khi loại trừ
            $doiTuongList = DoiTuong::whereIn('_id', $hocSinhIDs)->get();

            $grouped = $doiTuongList->groupBy(fn($item) => (string) $item->DonViID_Hoc);    

            // // Bước 4: Lấy danh sách DonViID_Hoc từ DoiTuong
            // $donViIDs = $doiTuongList
            //     ->pluck('DonViID_Hoc')
            //     ->filter(fn($id) => !empty($id))
            //     ->unique()
            //     ->values();

            // // Nếu không có đơn vị nào thì trả về rỗng
            // if ($donViIDs->isEmpty()) {
            //     return response()->json([
            //         'Err' => false,
            //         'CanhBao' => true,
            //         'Result' => [],
            //         'Msg' => 'Không có trường học nào có học sinh tốt nghiệp.',
            //     ]);
            // }

            // Bước 3: Truy vấn DonVi từ danh sách DonViID_Hoc
            $ds = DonVi::whereIn('_id', SoGoc::where('_id', $soGocID)
                    ->pluck('DonViID_TruongHoc')
                    ->toArray())->orderBy('TenDonVi')->get();

            // Bước 4: Map kết quả
           $result = $ds->map(function ($dv) use ($grouped, $hocSinhDaCo) {
                 $dsHocSinh = $grouped->get((string) $dv->_id, collect());

                $tong = $dsHocSinh->count();
                $daChon = $dsHocSinh->filter(fn($hs) => in_array((string) $hs->_id, $hocSinhDaCo))->count();
                return [
                    'id' => (string) $dv->_id,
                    'code' => $dv->MaDonVi ?? '',
                    'name' => $dv->TenDonVi ?? 'Chưa đặt tên',
                    'count' => $tong,
                    'daChonCount' => $daChon,
                ];
            });

            return response()->json([
                'Err' => false,
                'Result' => $result,
            ]);
        } catch (\Throwable $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Đã xảy ra lỗi khi lấy danh sách đơn vị.',
                'Debug' => $e->getMessage(),
            ], 500);
        }
    }
    /**
     * Lấy danh sách học sinh thuộc một trường, và đã có trong bảng HocSinhTN
     */
    public function GetListHocSinhByDonVi(Request $request)
    {
        try {
            $QuyetDinhID = $request->input('QuyetDinhID');
            $donViID = $request->input('DonViID');

            // if (empty($donViID)) {
            //     return response()->json([
            //         'Err' => true,
            //         'Msg' => 'Thiếu DonViID.',
            //     ]);
            // }

            // Bước 1: Lấy danh sách CapBangTotNghiep theo Quyết định
            $capBangIDs = CapBangTotNghiep::where('QuyetDinhID', new ObjectId($QuyetDinhID))->where('BanSao', false)
                ->pluck('_id')
                ->toArray();

            // Bước 2: Lấy danh sách HocSinhID từ CapBangTotNghiepCT
            $hocSinhIDs = CapBangTotNghiepCT::whereIn('CapBangTotNghiepID', $capBangIDs)
                ->raw(function ($collection) {
                    return $collection->distinct('HocSinhID', [
                        'HocSinhID' => ['$ne' => null]
                    ]);
                });
            $soGocID = new ObjectId($request->SoGocID);
            $hocSinhDaCo = SoGocCT::where('SoGocID', $soGocID)
                    ->pluck('DoiTuongID_HocSinh')
                    ->toArray();

            // Loại bỏ những học sinh đã có
            $hocSinhIDs = array_values(array_diff($hocSinhIDs, $hocSinhDaCo));
            // Bước 3: Truy vấn DoiTuong
            $query = DoiTuong::where('DonViID_Hoc', new ObjectId($donViID))
                ->whereIn('_id', $hocSinhIDs);

            $sortField = $request->input('CbSapXep', 'MaDoiTuong');
            $query->orderBy($sortField, 'asc');

            $result = $query->get();

            // Nếu cần map thêm gì từ CapBangTotNghiepCT thì cần load lại danh sách chi tiết
            $capBangCTMap = CapBangTotNghiepCT::whereIn('HocSinhID', $hocSinhIDs)->get()->keyBy('HocSinhID');
            $result = $result->map(function ($item) use ($capBangCTMap) {
                $item->KetQuaTN = $capBangCTMap[$item->_id]->XepLoaiID ?? null;
                 $item->SoHieu = $capBangCTMap[$item->_id]->SoHieu ?? null;
                 $item->SoVaoSo = $capBangCTMap[$item->_id]->SoVaoSo ?? null;
                return $item;
            });
            //$QuyetDinhCTMap = HocSinhTN::whereIn('HocSinhID', $hocSinhIDs)->get()->keyBy('HocSinhID');
            //$result = $result->map(function ($item) use ($QuyetDinhCTMap) {
                //$item->KetQuaTN = $QuyetDinhCTMap[$item->_id]->XepLoaiID ?? null;
                //return $item;
            //});
            // Format lại nếu bạn dùng hàm formatDoiTuongs
            $result = $this->formatDoiTuongs($result);

            return response()->json([
                'Err' => false,
                'result' => $result,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Không thể lấy danh sách!',
                'debug' => $e->getMessage(),
            ]);
        }
    }

    
    #region Nhận ex
        /**
         * Kiểm tra cấu trúc file, load lên session cho preview
         */
        public function checkExcel(Request $request)
        {
            $path  = $request->input('path', '');
            $sheet = $request->input('sheet', '');
            $CapBangTotNghiepID = $request->input('CapBangTotNghiepID', '');
            if (! $path || ! $sheet) {
                return response()->json(['Err' => true, 'Msg' => 'Missing path or sheet'], 422);
            }

            // Turn URL “/storage/…” into storage/app/public-relative
            $urlPath  = parse_url($path, PHP_URL_PATH);
            $urlPath  = preg_replace('#^/+/#', '/', $urlPath);
            $relative = Str::after($urlPath, '/storage/');

            if (! Storage::disk('public')->exists($relative)) {
                return response()->json(['Err' => true, 'Msg' => 'File not found on server'], 404);
            }
            $fullPath = Storage::disk('public')->path($relative);

            try {
                // load data‐only
                $reader      = IOFactoryNhanEx::createReaderForFile($fullPath);
                $reader->setReadDataOnly(true);
                $spreadsheet = $reader->load($fullPath);

                if (! $spreadsheet->sheetNameExists($sheet)) {
                    return response()->json([
                        'Err' => true,
                        'Msg' => "Sheet “{$sheet}” không tồn tại"
                    ], 422);
                }

                $ws   = $spreadsheet->getSheetByName($sheet);
                $rows = $ws->toArray(null, true, true, false);

                // grab & trim header
                $header = array_map('trim', array_shift($rows));

                // required columns
                $required = [
                        'Mã học sinh',     // Cột A
                        'Họ và tên',       // Cột B
                        'Số CMND/CCCD',    // Cột C
                        'Ngày sinh',       // Cột D
                        'Giới tính',       // Cột E
                        'Dân tộc',         // Cột F
                        'Nơi sinh',        // Cột G
                        'Lớp',             // Cột H
                        'Trường học',      // Cột H
                        'Tỉnh/thành phố',  // Cột H
                        'Địa chỉ',         // Cột I
                        'Xếp loại',        // Cột J
                        'Số hiệu',         // Cột K
                        'Số vào sổ',       // Cột L
                ];
                if ($missing = array_diff($required, $header)) {
                    return response()->json([
                        'Err' => true,
                        'Msg' => 'Thiếu cột: ' . implode(', ', $missing),
                    ], 422);
                }

                // combine into assoc rows
                $assoc = [];
                foreach ($rows as $r) {
                    if (count($r) === count($header)) {
                        $assoc[] = array_combine($header, $r);
                        $assoc = array_map(function ($row) {
                            return array_map(function ($val) {
                                if (is_string($val)) {
                                    return ltrim(trim($val), "'");
                                }
                                return $val;
                            }, $row);
                        }, $assoc);
                    }
                }

                // Lấy danh sách Mã đơn vị từ file
                $maSoList = array_column($assoc, 'Mã học sinh');
                $cccdList = array_column($assoc, 'Số CMND/CCCD');
                $soHieuList = array_column($assoc, 'Số hiệu');
                $soVaoSoList = array_column($assoc, 'Số vào sổ');

                // 1. Trùng trong file
                $maSoList = array_filter($maSoList, fn($val) => is_string($val) || is_int($val));
                $dupesInFile = array_filter(array_count_values($maSoList), fn($count) => $count > 1);
                $dupeKeysInFile = array_keys($dupesInFile);

                $soHieuList = array_filter($soHieuList, fn($val) => is_string($val) || is_int($val));
                $dupesInFile2 = array_filter(array_count_values($soHieuList), fn($count) => $count > 1);
                $dupeKeysInFile2 = array_keys($dupesInFile2);

                $soVaoSoList = array_filter($soVaoSoList, fn($val) => is_string($val) || is_int($val));
                $dupesInFile3 = array_filter(array_count_values($soVaoSoList), fn($count) => $count > 1);
                $dupeKeysInFile3 = array_keys($dupesInFile3);

                $cccdList = array_filter($cccdList, fn($val) => is_string($val) || is_int($val));
                $dupesInFile4 = array_filter(array_count_values($cccdList), fn($count) => $count > 1);
                $dupeKeysInFile4 = array_keys($dupesInFile4);

                // 2. Trùng trong DB
                $dupesInDB = CapBangTotNghiepCT::whereIn('MaHocSinh', $maSoList)
                    ->pluck('MaHocSinh')
                    ->toArray();

                $dupesInDB2 = CapBangTotNghiepCT::whereIn('SoHieu', $soHieuList)
                    ->pluck('SoHieu')
                    ->toArray();

                $dupesInDB3 = CapBangTotNghiepCT::whereIn('SoVaoSo', $soVaoSoList)
                    ->pluck('SoVaoSo')
                    ->toArray();

                $dupesInDB4 = CapBangTotNghiepCT::whereIn('CCCD', $cccdList)
                    ->pluck('CCCD')
                    ->toArray();

                // 3. Gắn cột "TrangThai"
                foreach ($assoc as &$row) {
                    $maSo = $row['Mã học sinh'];
                    $danToc = $row['Dân tộc'];
                    $gioiTinh = $row['Giới tính'];
                    $cccd = $row['Số CMND/CCCD'];
                    $lop = $row['Lớp'];
                    $xepLoai = $row['Xếp loại'];
                    $soHieu = $row['Số hiệu'];
                    $soVaoSo = $row['Số vào sổ'];

                    $truongHoc = $row['Trường học'];
                    $tinhThanhpho = $row['Tỉnh/thành phố'];

                    $maDonVi = $this->currentUser->maDonVi();
                    $pattern = '/^' . preg_quote($maDonVi, '/') . '(\..*)?$/';
                    $donViModel = DonVi::where('MaDonVi', 'regexp', $pattern)->where('TenDonVi', $truongHoc)->first();
                    $tinhModel = DiaBanHanhChinh::where('TenDiaBan', $tinhThanhpho)->first();

                    $gioiTinhModel = GioiTinh::where('TenGioiTinh', $gioiTinh)->first();
                    $cccdModel = DoiTuong::where(function ($query) use ($cccd, $maSo) {
                        $query->where('CCCD', $cccd)
                                ->orWhere('MaDoiTuong', $maSo);
                    })->first();
                    $danTocModel = DanToc::where('tenDanToc', $danToc)->first();
                    $lopModel = LopHoc::where('TenLopHoc', $lop)->first();
                    $xepLoaiModel = XepLoai::where('tenXepLoai', $xepLoai)->first();
                    $messages = [];

                    if (trim($maSo) === '') {
                        $messages[] = 'Mã học sinh không được để trống';
                    }
                    if (trim($cccd) === '') {
                        $messages[] = 'Số CMND/CCCD không được để trống';
                    }
                    if (trim($row['Họ và tên']) === '') {
                        $messages[] = 'Họ và tên học sinh không được để trống';
                    }

                    if (in_array($maSo, $dupeKeysInFile)) {
                        $messages[] = 'Trùng mã học sinh trong file';
                    }
                    if (in_array($soHieu, $dupeKeysInFile2)) {
                        $messages[] = 'Trùng số hiệu văn bằng trong file';
                    }
                    if (in_array($soVaoSo, $dupeKeysInFile3)) {
                        $messages[] = 'Trùng số vào sổ trong file';
                    }
                    if (in_array($cccd, $dupeKeysInFile4)) {
                        $messages[] = 'Trùng CMND/CCCD trong file';
                    }

                    if (in_array($maSo, $dupesInDB)) {
                        $messages[] = 'Đã tồn tại mã học sinh trên hệ thống';
                    }
                    if (in_array($soHieu, $dupesInDB2)) {
                        $messages[] = 'Đã tồn tại số hiệu văn bằng trên hệ thống';
                    }
                    if (in_array($soVaoSo, $dupesInDB3)) {
                        $messages[] = 'Đã tồn tại số vào sổ trên hệ thống';
                    }
                    if (in_array($cccd, $dupesInDB4)) {
                        $messages[] = 'Đã tồn tại CMND/CCCD trên hệ thống';
                    }

                    if ($danTocModel) {
                        $row['DanTocID'] = $danTocModel->id;
                    } else {
                        $messages[] = "Dân tộc '$danToc' không tồn tại trong hệ thống.";
                        $row['DanTocID'] = null;
                    }
                    if ($lopModel) {
                        $row['LopHocID'] = $lopModel->id;
                    } else {
                        $row['LopHocID'] = null;
                    }
                    if ($xepLoaiModel) {
                        $row['XepLoaiID'] = $xepLoaiModel->id;
                    } else {
                        $messages[] = "Xếp loại '$xepLoai' không tồn tại trong hệ thống.";
                        $row['XepLoaiID'] = null;
                    }
                    if ($cccdModel) {
                        $row['HocSinhID'] = $cccdModel->id;
                    } else {
                        $row['HocSinhID'] = null;
                    }
                    if ($gioiTinhModel) {
                        $row['GioiTinhID'] = $gioiTinhModel->id;
                    } else {
                        $row['GioiTinhID'] = null;
                    }
                    if ($tinhModel) {
                        $row['DiaBanHCID_Tinh'] = $tinhModel->id;
                    } else {
                        $row['DiaBanHCID_Tinh'] = null;
                    }
                    if ($donViModel) {
                        $row['DonViID_Hoc'] = $donViModel->id;
                    } else {
                        $row['DonViID_Hoc'] = null;
                    }
                    $row['CapBangTotNghiepID'] =$CapBangTotNghiepID;
                    $row['trangThai'] = count($messages) > 0 ? implode('; ', $messages) : 'Chờ nhận';
                }
                unset($row); // tránh lỗi reference
                session(['DSHocSinh_excel' => $assoc]);

                return response()->json(['Err' => false]);
            } catch (\Throwable $e) {
                return response()->json([
                    'Err' => true,
                    'Msg' => 'Lỗi đọc file: ' . $e->getMessage(),
                ], 500);
            }
        }
        /**
         * Trả về JSON để Tabulator preview
         */
        function parseExcelDate($value)
        {
            if (is_string($value)) {
                // Remove leading single quote (') and trim whitespace
                $value = ltrim(trim($value), "'");

                $formats = ['d/m/Y', 'd-m-Y', 'j/n/Y', 'Y-m-d'];
                foreach ($formats as $format) {
                    try {
                        return Carbon::createFromFormat($format, $value)->format('d/m/Y');
                    } catch (\Exception $e) {
                        continue;
                    }
                }
            }

            if (is_numeric($value)) {
                try {
                    return Carbon::instance(\PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($value))->format('d/m/Y');
                } catch (\Exception $e) {
                    return null;
                }
            }

            return null;
        }
        public function loadExcel()
        {
            $rows = collect(session('DSHocSinh_excel', []));
            $data = $rows->map(function(array $r) {
                // Xử lý loại bỏ dấu nháy đầu chuỗi cho toàn bộ giá trị
                $r = array_map(function ($val) {
                    if (is_string($val)) {
                        return ltrim(trim($val), "'");
                    }
                    return $val;
                }, $r);

                return [
                    'CapBangTotNghiepID'   => $r['CapBangTotNghiepID'] ?? null,
                    'HocSinhID'   => $r['HocSinhID'] ?? null,
                    'MaHocSinh'   => $r['Mã học sinh'] ?? null,
                    'TenHocSinh'   => $r['Họ và tên'] ?? null,
                    'CCCD'   => $r['Số CMND/CCCD'] ?? null,
                    'NgaySinh'   => $this->parseExcelDate($r['Ngày sinh'] ?? null),
                    'GioiTinhID'   => $r['GioiTinhID'] ?? null,
                    'TenGioiTinh'   => $r['Giới tính'] ?? null,
                    'DanTocID'   => $r['DanTocID'] ?? null,
                    'TenDanToc'   => $r['Dân tộc'] ?? null,
                    'NoiSinh'   => $r['Nơi sinh'] ?? null,
                    'LopHocID'   => $r['LopHocID'] ?? null,
                    'TenLop'   => $r['Lớp'] ?? null,
                    'DiaChi'   => $r['Địa chỉ'] ?? null,
                    'DiaBanHCID_Tinh'   =>  $r['DiaBanHCID_Tinh'] ?? null,
                    'TenTinh'   =>  $r['Tỉnh/thành phố'] ?? null,
                    'TenTruong'   => $r['Trường học'] ??  null,
                    'DonViID_TruongHoc'   => $r['DonViID_Hoc'] ??  null,
                    'GhiChu'   =>  null,
                    'XepLoaiID'   => $r['XepLoaiID'] ?? null,
                    'TenXepLoai'   => $r['Xếp loại'] ?? null,
                    'SoHieu'   => $r['Số hiệu'] ?? null,
                    'SoVaoSo'   => $r['Số vào sổ'] ?? null,
                    'trangThai'   => $r['trangThai'] ?? 'Chờ nhận',
                    ];
                });

            return response()->json($data);
        }
        /**
         * Nhập những dòng được chọn
         */
        public function importExcel(Request $request)
        {
            $selected = $request->input('rows', []);
            $all      = collect(session('DSHocSinh_excel', []));
            $out = [];
            $userId = auth()->id() ? new ObjectId(auth()->id()) : null;
            $now = now();
            // 3. Insert từng dòng một, có xử lý cha
            foreach ($selected as $idx) {
                $r = $all->get($idx - 1);

                if (!$r || !is_array($r)) {
                    $out[$idx] = ['Err' => true, 'Msg' => 'Dữ liệu hàng không tồn tại'];
                    continue;
                }
                if (empty($r['HocSinhID'])) {
                    $doiTuong = DoiTuong::create([
                        'MaDoiTuong'    => $r['Mã học sinh'],
                        'Hovaten'       => $r['Họ và tên'],
                        'Ngaysinh'      => $r['Ngày sinh'] ? Carbon::createFromFormat('d/m/Y', $r['Ngày sinh']) : null,
                        'Noisinh'       => $r['Nơi sinh'],
                        'Gioitinh'      => $this->safeObjectId($r['GioiTinhID']),
                        'CCCD'          => $r['Số CMND/CCCD'],
                        'NgayCap'       => null,
                        'DiaBanHCID_NoiCap' => null,
                        'DanTocID'      => $this->safeObjectId($r['DanTocID']),
                        'DiaBanHCID_Tinh' => null,
                        'DiaBanHCID_Xa' => null,
                        'DiaBanHCID_Thon' => null,
                        'DiaChi'        => $r['Địa chỉ'],
                        'SDT'           => null,
                        'Email'         => null,
                        'DonViID'       => $this->currentUser->donViId(),
                        'DonViID_Hoc'   => $this->safeObjectId($r['DonViID_Hoc']),
                        'GhiChu'        => null,
                        'TrangThai'     => true,
                        'UserID_ThaoTac'=> $userId,
                        'NgayThaoTac'   => $now,
                        'AnhDaiDien'    => null,
                        'DinhKem'       => null,
                        'LopHocID'      => $this->safeObjectId($r['LopHocID']),
                        'DienUuTienID'  => null,
                        'SoNha'         => null,
                        'TrangThai_TotNghiep' => '30',
                        'TrangThai_CapBang'   => '35',
                    ]);

                    // Cập nhật lại ID cho bước tiếp theo
                    $r['HocSinhID'] = $doiTuong->_id;
                }
            // Insert dòng chính
            CapBangTotNghiepCT::create([
                        'CapBangTotNghiepID'   => $this->safeObjectId($r['CapBangTotNghiepID']),
                        'HocSinhID'   => $this->safeObjectId($r['HocSinhID']),
                        'MaHocSinh'   => $r['Mã học sinh'],
                        'TenHocSinh'   => $r['Họ và tên'],
                        'CCCD'   => $r['Số CMND/CCCD'],
                        'NgaySinh'   => $r['Ngày sinh'] ? Carbon::createFromFormat('d/m/Y', $r['Ngày sinh']) : null,
                        'GioiTinhID'   => $this->safeObjectId($r['GioiTinhID']),
                        'DanTocID'   => $this->safeObjectId($r['DanTocID']),
                        'NoiSinh'   => $r['Nơi sinh'],
                        'LopHocID'   => $this->safeObjectId($r['LopHocID']),
                        'DiaChi'   => $r['Địa chỉ'],
                        'DiaBanHCID_Tinh'   => $this->safeObjectId($r['DiaBanHCID_Tinh']),
                        'DonViID_TruongHoc'   => $this->safeObjectId($r['DonViID_Hoc']),
                        'GhiChu'   => null,
                        'XepLoaiID'   => $this->safeObjectId($r['XepLoaiID']),
                        'SoHieu'   => $r['Số hiệu'],
                        'SoVaoSo'   => $r['Số vào sổ'],
                    ]);

                $out[$idx] = ['Err' => false];
            }
            return response()->json($out);
        }


        public function loadSheetNames(Request $request)
        {
            $path = $request->input('path');
            if (! $path) {
                return response()->json(['Err'=>true,'Msg'=>'Missing file path'], 422);
            }

            // 1) extract just the path part, e.g. "//storage/uploads/…"
            $urlPath = parse_url($path, PHP_URL_PATH);

            // 2) collapse multiple leading slashes: "/storage/uploads/…"
            $urlPath = preg_replace('#^/+/#','/', $urlPath);

            // 3) grab everything after "/storage/" → "uploads/…"
            $relative = Str::after($urlPath, '/storage/');

            // 4) now check on the public disk
            if (! Storage::disk('public')->exists($relative)) {
                return response()->json(['Err'=>true,'Msg'=>'File not found on disk'], 404);
            }

            $fullPath = Storage::disk('public')->path($relative);

            try {
                // create the best reader for this file
                $reader = IOFactoryNhanEx::createReaderForFile($fullPath);
                // only list sheet names, no data
                $sheetNames = $reader->listWorksheetNames($fullPath);
                $Result = array_map(fn($name) => ['TenSheet' => $name], $sheetNames);

                $payload = [
                    'CanhBao'     => false,
                    'Xem'         => false,
                    'Them'        => false,
                    'Sua'         => false,
                    'Xoa'         => false,
                    'InAn'        => false,
                    'Nap'         => false,
                    'Quyen1'      => false,
                    'Quyen2'      => false,
                    'Quyen3'      => false,
                    'Err'         => false,
                    'ErrCode'     => '',
                    'ErrCatch'    => '',
                    'Result'      => $Result,
                    'Msg'         => '',
                    'Logs'        => '',
                    'Redirect'    => false,
                    'RedirectUrl' => '',
                ];

                // encode as JSON string
                $json = json_encode($payload);

                // return it as a plain-text response
                return response($json, 200)
                    ->header('Content-Type', 'text/html');
            } catch (\Throwable $e) {
                // return the real exception message so we can see why it fails
                return response()->json([
                    'Err' => true,
                    'Msg' => 'Error reading file: ' . $e->getMessage()
                ], 500);
            }
        }
        public function downloadTemplate()
        {
            return Excel::download(
                new SoGocTemplateExport,
                'Mau_SoGocDSHocSinhCapBang.xlsx'
            );
        }
        #endregion
    }
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use PhpOffice\PhpSpreadsheet\Style\Color;   
class SoGocTemplateExport implements
 FromCollection,
 WithHeadings,
 WithColumnWidths,
 ShouldAutoSize,
 WithStyles,
 WithColumnFormatting
{
    public function collection()
    {
        return collect(); // Xuất file mẫu không có data
    }

    public function headings(): array
    {
        return [
            'Mã học sinh',     // Cột A
            'Họ và tên',       // Cột B
            'Số CMND/CCCD',    // Cột C
            'Ngày sinh',       // Cột D
            'Giới tính',       // Cột E
            'Dân tộc',         // Cột F
            'Nơi sinh',        // Cột G
            'Lớp',             // Cột H
            'Trường học',       // Cột I
            'Tỉnh/thành phố',  // Cột J
            'Địa chỉ',         // Cột K
            'Xếp loại',        // Cột L
            'Số hiệu',         // Cột M
            'Số vào sổ',       // Cột N
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 15,  // Mã học sinh
            'B' => 30,  // Họ và tên
            'C' => 25,  // Số CMND/CCCD
            'D' => 25,  // Ngày sinh
            'E' => 20,  // Giới tính
            'F' => 20,  // Dân tộc
            'G' => 30,  // Nơi sinh
            'H' => 20,  // Lớp
            'I' => 30,  // Trường học
            'J' => 20,  // Tỉnh/thành phố
            'K' => 30,  // Địa chỉ
            'L' => 20,  // Xếp loại
            'M' => 20,  // Số hiệu
            'N' => 20,  // Số vào sổ
        ];
    }
    public function columnFormats(): array
    {
        return [
            'A' => NumberFormat::FORMAT_TEXT,
            'B' => NumberFormat::FORMAT_TEXT,
            'C' => NumberFormat::FORMAT_TEXT,
            'D' => NumberFormat::FORMAT_TEXT,
            'E' => NumberFormat::FORMAT_TEXT,
            'F' => NumberFormat::FORMAT_TEXT,
            'G' => NumberFormat::FORMAT_TEXT,
            'H' => NumberFormat::FORMAT_TEXT,
            'I' => NumberFormat::FORMAT_TEXT,
            'J' => NumberFormat::FORMAT_TEXT,
            'K' => NumberFormat::FORMAT_TEXT,
            'L' => NumberFormat::FORMAT_TEXT,
            'M' => NumberFormat::FORMAT_TEXT,
            'N' => NumberFormat::FORMAT_TEXT,
        ];
    }
    public function styles(Worksheet $sheet)
    {
            // Áp dụng style cho dòng tiêu đề (dòng 1)
        $styles = [
            1 => [
                'font' => [
                    'bold' => true,
                    'name' => 'Times New Roman',
                    'size' => 13,
                ],
                'alignment' => [
                    'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                    'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                        'color' => ['argb' => '000000'],
                    ],
                ],
            ],
        ];

        // 👇 Thêm style riêng cho cột A1 (ô đầu tiên - "Mã học sinh") màu đỏ
        $sheet->getStyle('A1')->getFont()->getColor()->setARGB(Color::COLOR_RED);
        $sheet->getStyle('B1')->getFont()->getColor()->setARGB(Color::COLOR_RED);
        $sheet->getStyle('C1')->getFont()->getColor()->setARGB(Color::COLOR_RED);
        return $styles;
    }
}
#endregion