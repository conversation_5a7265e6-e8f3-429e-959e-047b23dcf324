<?php

namespace App\Http\Controllers\QuanLy;

use App\Models\DanhMuc\CapHoc;
use App\Models\DanhMuc\ChucVu;
use App\Models\DanhMuc\DonViTinh;
use App\Models\DanhMuc\HinhThucNhanPhoi;
use App\Models\DanhMuc\LoaiPhoiVanBangChungChi;
use App\Models\DanhMuc\NhanVien;
use App\Models\QuanLy\TiepNhanPhoiVBCC;
use Str;
use Storage;
use Carbon\Carbon;
use App\Services\ThongBao;
use Illuminate\Http\Request;
use App\Models\DanhMuc\DonVi;
use App\Services\DungChungDb;
use App\Services\DungChungNoDb;
use App\Models\DanhMuc\TrangThai;
use App\Http\Controllers\Controller;
use App\Services\CurrentUserService;
use App\Models\QuanLy\DonYeuCau;
use MongoDB\BSON\ObjectId; // from mongodb/mongodb
use App\Models\quanly\ThaoTac;
use App\Models\QuanLy\NhatKyThaoTac;
use Log;


class DuyetDonXinCapPhoiBangController extends Controller
{
    protected DungChungDb $dungChungDb;
    protected DungChungNoDb $logService;
    protected CurrentUserService $currentUser;

    public function __construct(DungChungDb $dungChungDb, CurrentUserService $currentUser, DungChungNoDb $logService)
    {
        $this->dungChungDb = $dungChungDb;
        $this->logService = $logService;
        $this->currentUser = $currentUser;
    }


    public function index()
    {
        return view('quanly.DuyetDonXinCapPhoiBang.index');
    }
    function isMongoId($id)
    {
        return is_string($id) && preg_match('/^[a-f\d]{24}$/i', $id);
    }

    function safeObjectId($id)
    {
        return $this->isMongoId($id) ? new ObjectId($id) : null;
    }

    /**
     * 
     */


    public function getAll(Request $request)
    {
        $donViId = $this->safeObjectId($this->currentUser->donViId());

        try {
            $query = DonYeuCau::query();

            $query->where(function ($q) use ($donViId) {
                $q->where('DonViTiepNhanXuLyID', $donViId);
            });
            $query->whereIn('TrangThaiXuLyID', ['32', '40', '42']);

            if ($request->filled('LoaiPhoiID_Loc')) {
                $query->where('LoaiPhoiID', new ObjectId($request->input('LoaiPhoiID_Loc')));
            }

            if ($request->filled('TrangThaiXuLyID_Loc')) {
                $query->where('TrangThaiXuLyID', operator: $request->input('TrangThaiXuLyID_Loc'));
            }

            if ($request->filled('TuNgay_Loc') || $request->filled('DenNgay_Loc')) {
                $tuNgay = $request->filled('TuNgay_Loc')
                    ? Carbon::createFromFormat('d/m/Y', $request->input('TuNgay_Loc'))->startOfDay()
                    : null;

                $denNgay = $request->filled('DenNgay_Loc')
                    ? Carbon::createFromFormat('d/m/Y', $request->input('DenNgay_Loc'))->endOfDay()
                    : null;

                if ($tuNgay && $denNgay) {
                    $query->whereBetween('NgayLap', [$tuNgay, $denNgay]);
                } elseif ($tuNgay) {
                    $query->where('NgayLap', '>=', $tuNgay);
                } elseif ($denNgay) {
                    $query->where('NgayLap', '<=', $denNgay);
                }
            }

            if ($request->filled('SearchKey')) {
                $keyword = $request->input('SearchKey');
                $regex = new \MongoDB\BSON\Regex($keyword, 'i');

                $nguoiIds = NhanVien::where('TenNhanVien', 'like', $regex)->pluck('_id')->toArray();
                $donViIds = DonVi::where('TenDonVi', 'like', $regex)->pluck('_id')->toArray();

                $query->where(function ($q) use ($regex, $nguoiIds, $donViIds) {
                    $q->orWhere('LyDoXinCap', 'like', $regex)
                        ->orWhere('GhiChu', 'like', $regex)
                        ->orWhere('TenLoaiPhoi', 'like', $regex)
                        ->orWhere('TenTrangThaiXuLy', 'like', $regex);

                    if (!empty($nguoiIds)) {
                        $q->orWhereIn('NguoiLapID', $nguoiIds)
                            ->orWhereIn('NguoiXuLyID', $nguoiIds);
                    }

                    if (!empty($donViIds)) {
                        $q->orWhereIn('DonViGuiID', $donViIds)
                            ->orWhereIn('DonViNhanID', $donViIds);
                    }
                });
            }

            $sortField = $request->input('CbSapXep', default: 'DonYeuCauID');

            $result = $query->get();

            if ($sortField === 'NgayLap') {
                $result = $result->sortBy('NgayLap')->values();
            } elseif ($sortField === 'TrangThaiXuLyID') {
                $order = ['40' => 0, '41' => 1, '42' => 2];
                $result = $result->sortBy(function ($item) use ($order) {
                    return $order[$item->TrangThaiXuLyID] ?? 999;
                })->values();
            } else {
                $result = $result->sortBy($sortField)->values();
            }

            $result = $this->formatDonYeuCaus($result);

            return response()->json([
                'Err' => false,
                'result' => $result,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Không thể lấy danh sách!',
                'debug' => $e->getMessage(),
            ]);
        }
    }


    public function formatDonYeuCaus($collection)
    {
        return $collection->map(function ($item) {

            $item->txtNgayLap = optional($item->NgayLap)->format('d/m/Y') ?? '';
            $item->TenNguoiLap = optional(NhanVien::find($item->NguoiLapID))->tenNhanVien ?? '';
            $item->TenChucVuNguoiLap = optional(ChucVu::find($item->ChucVuNguoiLapID))->tenChucVu ?? '';

            $item->TenCapHoc = optional(CapHoc::find($item->CapHocID))->tenCapHoc ?? '';

            $item->TenLoaiPhoi = optional(LoaiPhoiVanBangChungChi::find($item->LoaiPhoiID))->TenLoaiPhoiVanBangChungChi ?? '';

            $donViNhan = DonVi::find($item->DonViNhanID);
            $item->TenDonViNhan = optional($donViNhan)->TenDonVi ?? '';
            $item->MaDonViNhan = optional($donViNhan)->MaDonVi ?? '';

            $donViGui = DonVi::find($item->DonViGuiID);
            $item->TenDonViGui = optional($donViGui)->TenDonVi ?? '';
            $item->MaDonViGui = optional($donViGui)->MaDonVi ?? '';

            $item->TenTepDinhKem = basename($item->DinhKem ?? '');
            $item->LinkDinhKem = $item->DinhKem ? asset($item->DinhKem) : null;

            $item->TenChucVuNguoiXuLy = optional(ChucVu::find($item->ChucVuNguoiXuLyID))->tenChucVu ?? '';

            $item->txtNgayXuLy = optional($item->NgayXuLy)->format('d/m/Y') ?? '';
            $item->TenNguoiXuLy = optional(NhanVien::find($item->NguoiXuLyID))->tenNhanVien ?? '';


            $item->TenDonViTiepNhanXuLy = optional(DonVi::find($item->DonViTiepNhanXuLyID))->TenDonVi ?? '';
            $item->TenNguoiTiepNhanXuLy = optional(NhanVien::find($item->NguoiTiepNhanXuLyID))->tenNhanVien ?? '';
            $item->txtNgayTiepNhanXuLy = optional($item->NgayTiepNhanXuLy)->format('d/m/Y') ?? '';
            $item->TenChucVuNguoiTiepNhanXuLy = optional(ChucVu::find($item->ChucVuNguoiTiepNhanXuLyID))->tenChucVu ?? '';

            $trangThaiXuLy = TrangThai::where("MaTrangThai", $item->TrangThaiXuLyID)->first();
            $item->TenTrangThaiXuLy = optional($trangThaiXuLy)->TenTrangThai ?? 'Chưa xác định';
            $item->MauSacTrangThaiXuLy = optional($trangThaiXuLy)->MauSac ?? '#ffffff';

            $item->TenHinhThucNhanPhoi = optional(HinhThucNhanPhoi::find($item->HinhThucNhanPhoiID))->tenHinhThucNhanPhoi ?? '';

            if (!empty($item->PhoiVBCC) && is_array($item->PhoiVBCC)) {
                $item->PhoiVBCC = array_map(function ($phoi) {
                    $loaiPhoi = LoaiPhoiVanBangChungChi::find($phoi['LoaiPhoiVBCCID'] ?? '');
                    $donViTinh = DonViTinh::find($phoi['DonViTinhID'] ?? '');

                    return array_merge($phoi, [
                        'TenLoaiPhoiVanBangChungChi' => optional($loaiPhoi)->TenLoaiPhoiVanBangChungChi ?? '',
                        'TenDonViTinh' => optional($donViTinh)->TenDonViTinh ?? '',
                    ]);
                }, $item->PhoiVBCC);
            }
            return $item;

        });
    }

    public function loadDuLieuSua(Request $request)
    {
        $id = $request->input('id');
        $DonYeuCauID = DonYeuCau::find($id);
        if (!$DonYeuCauID) {
            return response()->json([
                'Err' => true,
                'canhbao' => true,
                'Msg' => 'Không tìm thấy đơn yêu cầu!'
            ]);
        }
        $DonYeuCauID = $this->formatDonYeuCaus(collect([$DonYeuCauID]))->first();
        return response()->json([
            'Err' => false,
            'Result' => $DonYeuCauID
        ]);
    }

    public function getThongKe(Request $request)
    {
        try {
            $donViId = $this->safeObjectId($this->currentUser->donViId());
            $UserID_CapNhat = auth()->id() ? new ObjectId(auth()->id()) : null;

            $LoaiPhoiID_Loc = $this->safeObjectId($request->input('LoaiPhoiID_Loc'));
            $TrangThai_Loc = $request->input('TrangThai_Loc');
            $TrangThaiXuLyID_Loc = $request->input('TrangThaiXuLyID_Loc');

            $tuNgay = $request->filled('TuNgay_Loc')
                ? Carbon::createFromFormat('d/m/Y', $request->input('TuNgay_Loc'))->startOfDay()
                : null;

            $denNgay = $request->filled('DenNgay_Loc')
                ? Carbon::createFromFormat('d/m/Y', $request->input('DenNgay_Loc'))->endOfDay()
                : null;

            $trangThaiIDs = ['40', '42', '32'];

            $counts = [];
            $TS = 0;
            foreach ($trangThaiIDs as $id) {
                $query = DonYeuCau::where('TrangThaiXuLyID', $id)
                    ->where('DonViGuiID', $donViId);

                if ($UserID_CapNhat) {
                    $query->where('UserID_ThaoTac', $UserID_CapNhat);
                }

                if (!is_null($LoaiPhoiID_Loc) && $LoaiPhoiID_Loc !== '') {
                    $query->where('PhoiVBCC', 'elemMatch', [
                        'LoaiPhoiVBCCID' => $LoaiPhoiID_Loc
                    ]);
                }

                if (!is_null($TrangThai_Loc) && $TrangThai_Loc !== '') {
                    $query->where('TrangThai', $TrangThai_Loc);
                }

                if (!is_null($TrangThaiXuLyID_Loc) && $TrangThaiXuLyID_Loc !== '') {
                    $query->where('TrangThaiXuLyID', $TrangThaiXuLyID_Loc);
                }

                if ($tuNgay && $denNgay) {
                    $query->whereBetween('NgayLap', [$tuNgay, $denNgay]);
                } elseif ($tuNgay) {
                    $query->where('NgayLap', '>=', $tuNgay);
                } elseif ($denNgay) {
                    $query->where('NgayLap', '<=', $denNgay);
                }

                $counts[$id] = $query->count();
                $TS += $counts[$id];
            }

            $trangThais = TrangThai::whereIn('MaTrangThai', $trangThaiIDs)->get()
                ->keyBy('MaTrangThai');

            $resultTrangThai = [];
            foreach ($trangThaiIDs as $id) {
                $info = $trangThais[$id] ?? null;
                $resultTrangThai[] = [
                    'TrangThaiXuLyID' => $id,
                    'SoLuong' => $counts[$id],
                    'TenTrangThai' => $info->TenTrangThai ?? '[Không xác định]',
                    'MauSac' => $info->MauSac ?? '#cccccc',
                ];
            }

            // Thống kê loại phôi
            $thongKePhoiVBCC = DonYeuCau::raw(function ($collection) use ($donViId, $UserID_CapNhat, $LoaiPhoiID_Loc, $TrangThai_Loc, $TrangThaiXuLyID_Loc, $tuNgay, $denNgay) {
                $matchConditions = ['DonViGuiID' => $donViId];

                if ($UserID_CapNhat) {
                    $matchConditions['UserID_ThaoTac'] = $UserID_CapNhat;
                }

                if (!is_null($TrangThai_Loc) && $TrangThai_Loc !== '') {
                    $matchConditions['TrangThai'] = $TrangThai_Loc;
                }

                if (!is_null($TrangThaiXuLyID_Loc) && $TrangThaiXuLyID_Loc !== '') {
                    $matchConditions['TrangThaiXuLyID'] = $TrangThaiXuLyID_Loc;
                }

                if ($tuNgay || $denNgay) {
                    $dateRange = [];
                    if ($tuNgay) {
                        $dateRange['$gte'] = new \MongoDB\BSON\UTCDateTime($tuNgay);
                    }
                    if ($denNgay) {
                        $dateRange['$lte'] = new \MongoDB\BSON\UTCDateTime($denNgay);
                    }
                    $matchConditions['NgayLap'] = $dateRange;
                }

                if (!is_null($LoaiPhoiID_Loc) && $LoaiPhoiID_Loc !== '') {
                    $matchConditions['PhoiVBCC.LoaiPhoiVBCCID'] = $LoaiPhoiID_Loc;
                }

                return $collection->aggregate([
                    ['$match' => $matchConditions],
                    ['$unwind' => '$PhoiVBCC'],
                    [
                        '$group' => [
                            '_id' => '$PhoiVBCC.LoaiPhoiVBCCID',
                            'SoLuong' => [
                                '$sum' => [
                                    '$toInt' => '$PhoiVBCC.SoLuongPhoi'
                                ]
                            ]
                        ]
                    ]
                ]);
            });

            $loaiPhois = LoaiPhoiVanBangChungChi::whereIn('_id', collect($thongKePhoiVBCC)->pluck('_id'))->get()
                ->keyBy(function ($item) {
                    return (string) $item->_id;
                });

            $resultLoaiPhoi = [];
            foreach ($thongKePhoiVBCC as $item) {
                $id = (string) $item->_id;
                $tenLoai = $loaiPhois[$id]->TenLoaiPhoiVanBangChungChi ?? '[Không xác định]';

                $resultLoaiPhoi[] = [
                    'LoaiPhoiVBCCID' => $id,
                    'TenLoaiPhoiVanBangChungChi' => $tenLoai,
                    'SoLuong' => $item->SoLuong
                ];
            }

            return response()->json([
                'Err' => false,
                'Result' => [
                    'TS' => $TS,
                    'ThongKeTrangThai' => $resultTrangThai,
                    'ThongKeLoaiPhoi' => $resultLoaiPhoi,
                ],
                'Msg' => '',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'Err' => true,
                'Msg' => 'Không thể lấy thống kê!',
                'debug' => $e->getMessage(),
            ]);
        }
    }


    public function getListTrangThaiCapBang()
    {
        try {
            $result = TrangThai::whereIn('MaTrangThai', ['40', '42', '32'])
                ->where('TrangThai', true)
                ->orderBy('MaTrangThai', 'asc')
                ->get();

            $data = $result->map(function ($item) {
                return [
                    'id' => (string) $item->_id,
                    'code' => $item->MaTrangThai,
                    'name' => $item->TenTrangThai ?? "Chưa có tên",
                    'color' => $item->MauSac ?? "#000000",
                    'parent_id' => $item->TrangThaiID_Cha ? (string) $item->TrangThaiID_Cha : null,
                ];
            });

            return response()->json([
                'Err' => false,
                'Result' => $data,
            ]);
        } catch (Exception $e) {
            $code = $this->dungChungDb->insertMaCodeLoi($e);

            return response()->json([
                'Err' => true,
                'Msg' => ThongBao::coLoiXayRa($code),
            ]);
        }
    }

    public function GuiDon(Request $request)
    {
        $DonYeuCau = null;
        $DonYeuCauID = $this->safeObjectId($request->input('DonYeuCauID'));
        try {

            $DonYeuCauID = $this->safeObjectId($request->input('DonYeuCauID'));
            $NguoiTiepNhanXuLyID = $this->safeObjectId($request->input('NguoiTiepNhanXuLyID'));
            $ChucVuNguoiTiepNhanXuLyID = $this->safeObjectId($request->input('ChucVuNguoiTiepNhanXuLyID'));
            $TrangThaiXuLyID = $request->input('TrangThaiXuLyID');
            $NgayTiepNhanXuLy = $request->filled('NgayTiepNhanXuLy') ? Carbon::createFromFormat('d/m/Y', $request->input('NgayTiepNhanXuLy')) : null;
            $NoiDungTiepNhanXuLy = $request->input('NoiDungTiepNhanXuLy');
            if ($request->isMethod('post')) {
                $DonYeuCau = DonYeuCau::find($DonYeuCauID);
                if (!$DonYeuCau) {
                    return response()->json([
                        'Err' => true,
                        'canhbao' => true,
                        'Msg' => 'Không tìm thấy dữ liệu cần cập nhật!'
                    ]);
                }
                $DonYeuCau->update([
                    'NguoiTiepNhanXuLyID' => $NguoiTiepNhanXuLyID
                    ,
                    'NgayTiepNhanXuLy' => $NgayTiepNhanXuLy
                    ,
                    'NoiDungTiepNhanXuLy' => $NoiDungTiepNhanXuLy
                    ,
                    'ChucVuNguoiTiepNhanXuLyID' => $ChucVuNguoiTiepNhanXuLyID
                    ,
                    'TrangThaiXuLyID' => $TrangThaiXuLyID
                ]);

                return response()->json([
                    'Err' => false,
                    'Msg' => 'Phê duyệt phiếu đề nghị thành công!'
                ]);
            }

            switch ((string) $TrangThaiXuLyID) {
                case '42':
                    $msg = 'Từ chối phiếu yêu cầu cấp phôi thành công!';
                    break;
                case '40':
                    $msg = 'Gửi phiếu yêu cầu cấp phôi thành công!';
                    break;
                case '32':
                    $msg = 'Phê duyệt phiếu yêu cầu cấp phôi thành công!';
                    break;
                default:
                    $msg = 'Cập nhật phiếu yêu cầu cấp phôi thành công!';
                    break;
            }

            return response()->json([
                'Err' => false,
                'Msg' => $msg
            ]);

        } catch (\Exception $e) {
            $this->logService->ghiLogs($request->all(), $request->isMethod('put') ? 'sua' : 'them', auth()->id(), null);
            $code = $this->dungChungDb->insertMaCodeLoi($e);
            return response()->json([
                'Err' => true,
                'Msg' => 'Có lỗi xảy ra!',
                'debug' => $e->getMessage(),
            ]);
        } finally {
            try {
                $DonYeuCau = DonYeuCau::find($DonYeuCauID);
                if (!$DonYeuCau) {
                    Log::warning('Không tìm thấy đơn để ghi nhật ký thao tác!');
                    return;
                }

                $TenNhanVien = NhanVien::where('_id', $NguoiTiepNhanXuLyID)->value('tenNhanVien') ?? '';
                $TenChucVu = ChucVu::where('_id', $ChucVuNguoiTiepNhanXuLyID)->value('tenChucVu') ?? '';
                $SoPhieu = $DonYeuCau->SoPhieu ?? '';

                // Gán thao tác theo trạng thái xử lý (dưới dạng chuỗi)
                switch ((string) $TrangThaiXuLyID) {
                    case '42':
                        $thaoTac = 'Từ chối phiếu yêu cầu cấp phôi Văn bằng, chứng chỉ';
                        $noiDungChiTiet = sprintf(
                            'Từ chối phiếu yêu cầu cấp phôi (Số phiếu: %s, Ngày tiếp nhận: %s)',
                            $SoPhieu,
                            $NgayTiepNhanXuLy
                        );
                        break;
                    case '40':
                        $thaoTac = 'Gửi phiếu yêu cầu cấp phôi Văn bằng, chứng chỉ';
                        $noiDungChiTiet = sprintf(
                            'Gửi phiếu yêu cầu cấp phôi (Số phiếu: %s, Ngày tiếp nhận: %s)',
                            $SoPhieu,
                            $NgayTiepNhanXuLy
                        );
                        break;
                    case '32':
                        $thaoTac = 'Phê duyệt phiếu yêu cầu cấp phôi Văn bằng, chứng chỉ';
                        $noiDungChiTiet = sprintf(
                            'Phê duyệt phiếu yêu cầu cấp phôi (Số phiếu: %s, Ngày tiếp nhận: %s)',
                            $SoPhieu,
                            $NgayTiepNhanXuLy
                        );
                        break;
                    default:
                        $thaoTac = 'Cập nhật phiếu yêu cầu cấp phôi Văn bằng, chứng chỉ';
                        $noiDungChiTiet = sprintf(
                            'Cập nhật phiếu yêu cầu cấp phôi (Số phiếu: %s, Ngày tiếp nhận: %s)',
                            $SoPhieu,
                            $NgayTiepNhanXuLy
                        );
                        break;
                }

                $method = $request->isMethod('post') ? 'Thêm' : 'Sửa';
                $duLieuThaoTac = ThaoTac::getFirstByThaoTac($method, (string) $this->currentUser->id());

                NhatKyThaoTac::luuNhatKyThaoTac(
                    $duLieuThaoTac['MaThaoTac'],
                    $duLieuThaoTac['ThaoTac'],
                    $duLieuThaoTac['MauSac'],
                    $thaoTac,
                    $noiDungChiTiet,
                    (string) $DonYeuCau->_id,
                    $DonYeuCau->getTable(),
                    url()->current(),
                    $TenNhanVien,
                    $TenChucVu,
                    (string) $this->currentUser->id(),
                    (string) $this->currentUser->donViId()
                );
            } catch (\Throwable $t) {
                \Log::error('Ghi NhatKyThaoTac thất bại: ' . $t->getMessage());
                \Log::error($t->getTraceAsString());
            }
        }

    }


    public function CapPhatPhoiBang(Request $request)
    {
        try {

            $TiepNhanPhoiVBCCID = $this->safeObjectId($request->input('TiepNhanPhoiVBCCID'));
            $MaPhieu = $request->input('MaPhieu');
            $NgayNhap = $request->filled('NgayNhap') ? Carbon::createFromFormat('d/m/Y', $request->input('NgayNhap')) : null;
            $CapHocID = $this->safeObjectId($request->input('CapHocID'));
            $NhanVienID_Nhap = $this->safeObjectId($request->input('NhanVienID_Nhap'));
            $ChucVuID = $this->safeObjectId($request->input('ChucVuID'));
            $LoaiPhoiVanBangChungChiID = $this->safeObjectId($request->input('LoaiPhoiVanBangChungChiID'));
            $DonViID_Cap = $this->safeObjectId($request->input('DonViID_Cap'));
            $DonViID_Nhap = $this->safeObjectId($request->input('DonViID_Nhap'));
            $DonViID_YeuCau = $this->safeObjectId($request->input('DonViID_YeuCau'));
            $SoLuongNhap = (int) $request->input('SoLuongNhap');
            $SoHieuTu = $request->input('SoHieuTu');
            $SoHieuDen = $request->input('SoHieuDen');
            $GhiChu = $request->input('GhiChu');
            $DinhKem = $request->input('txtDuongDanFileVB') ? $request->input('txtDuongDanFileVB') : '';
            $NgayThaoTac = now();
            $NgayCapNhat = now();
            $UserID_CapNhat = auth()->id() ? new ObjectId(auth()->id()) : null;
            $UserID_ThaoTac = auth()->id() ? new ObjectId(auth()->id()) : null;
            $UserID_PheDuyet = auth()->id() ? (auth()->id()) : null;


            if ($request->isMethod('post')) {
                // duplicate check
                if ($this->dungChungDb->kiemTraTonTai('tiep_nhan_phoi_v_b_c_c_s', 'MaPhieu', $MaPhieu)) {
                    return response()->json([
                        'Err' => true,
                        'canhbao' => true,
                        'Msg' => ThongBao::coLoiXayRa('Số phiếu đã tồn tại')
                    ]);
                }

                $paths = $this->saveFilesCapPhat($DinhKem);

                TiepNhanPhoiVBCC::create([
                    'MaPhieu' => $MaPhieu
                    ,
                    'NgayNhap' => $NgayNhap
                    ,
                    'CapHocID' => $CapHocID
                    ,
                    'NhanVienID_Nhap' => $NhanVienID_Nhap
                    ,
                    'ChucVuID' => $ChucVuID
                    ,
                    'LoaiPhoiVanBangChungChiID' => $LoaiPhoiVanBangChungChiID
                    ,
                    'DonViID_Cap' => $DonViID_Cap
                    ,
                    'DonViID_Nhap' => $DonViID_Nhap
                    ,
                    'DonViID_YeuCau' => $DonViID_YeuCau
                    ,
                    'SoLuongNhap' => $SoLuongNhap
                    ,
                    'SoHieuTu' => $SoHieuTu
                    ,
                    'SoHieuDen' => $SoHieuDen
                    ,
                    'GhiChu' => $GhiChu
                    ,
                    'DinhKem' => $paths
                    ,
                    'NgayThaoTac' => $NgayThaoTac
                    ,
                    'NgayCapNhat' => $NgayCapNhat
                    ,
                    'UserID_CapNhat' => $UserID_CapNhat
                    ,
                    'UserID_ThaoTac' => $UserID_ThaoTac
                    ,
                    'TrangThai' => '32' //Phe Duyet
                    ,
                    'NhanVienID_PheDuyet' => (string) $UserID_ThaoTac
                    ,
                    'NgayPheDuyet' => $NgayNhap
                    ,
                    'NoiDung_PheDuyet' => 'Cấp phát phôi bằng'
                    ,
                    'ChucVuID_PheDuyet' => $ChucVuID

                ]);
                $this->logService->ghiLogs($request->all(), 'them', auth()->id(), null);
                return response()->json([
                    'Err' => false,
                    'Msg' => 'Thêm dữ liệu thành công!'
                ]);
            }

            if ($request->isMethod('put') || $request->isMethod('patch')) {
                // Update
                $TiepNhanPhoiVBCC = TiepNhanPhoiVBCC::find($TiepNhanPhoiVBCCID);
                if (!$TiepNhanPhoiVBCC) {
                    return response()->json([
                        'Err' => true,
                        'canhbao' => true,
                        'Msg' => 'Không tìm thấy dữ liệu cần cập nhật!'
                    ]);
                }
                // ✅ Xóa các file đính kèm nếu có
                if (!empty($TiepNhanPhoiVBCC->DinhKem)) {
                    $paths = explode('|', $TiepNhanPhoiVBCC->DinhKem);
                    foreach ($paths as $path) {
                        // Xử lý path: bỏ /storage/ để về path thật trong public
                        $relativePath = str_replace('/storage/', 'public/', $path);
                        if (Storage::exists($relativePath)) {
                            Storage::delete($relativePath);
                        }
                    }
                }
                $paths = $this->saveFilesCapPhat($DinhKem);

                $TiepNhanPhoiVBCC->update([
                    'MaPhieu' => $MaPhieu
                    ,
                    'NgayNhap' => $NgayNhap
                    ,
                    'CapHocID' => $CapHocID
                    ,
                    'NhanVienID_Nhap' => $NhanVienID_Nhap
                    ,
                    'ChucVuID' => $ChucVuID
                    ,
                    'LoaiPhoiVanBangChungChiID' => $LoaiPhoiVanBangChungChiID
                    ,
                    'DonViID_Cap' => $DonViID_Cap
                    ,
                    'DonViID_Nhap' => $DonViID_Nhap
                    ,
                    'DonViID_YeuCau' => $DonViID_YeuCau
                    ,
                    'SoLuongNhap' => $SoLuongNhap
                    ,
                    'SoHieuTu' => $SoHieuTu
                    ,
                    'SoHieuDen' => $SoHieuDen
                    ,
                    'GhiChu' => $GhiChu
                    ,
                    'DinhKem' => $paths
                    ,
                    'NgayCapNhat' => $NgayCapNhat
                    ,
                    'UserID_CapNhat' => $UserID_CapNhat
                    ,
                    'TrangThai' => '32' //Phe Duyet
                    ,
                    'NhanVienID_PheDuyet' => (string) $UserID_ThaoTac
                    ,
                    'NgayPheDuyet' => $NgayNhap
                    ,
                    'NoiDung_PheDuyet' => ''
                    ,
                    'ChucVuID_PheDuyet' => $ChucVuID
                ]);
                $this->logService->ghiLogs($request->all(), 'sua', auth()->id(), null);
                return response()->json([
                    'Err' => false,
                    'Msg' => 'Cập nhật dữ liệu thành công!'
                ]);
            }

            // Nếu không phải POST hoặc PUT
            return response()->json([
                'Err' => true,
                'canhbao' => true,
                'Msg' => 'Phương thức HTTP không hợp lệ!'
            ]);

        } catch (\Exception $e) {
            $this->logService->ghiLogs($request->all(), $request->isMethod('put') ? 'sua' : 'them', auth()->id(), null);
            $code = $this->dungChungDb->insertMaCodeLoi($e);
            return response()->json([
                'Err' => true,
                'Msg' => 'Có lỗi xảy ra!',
                'debug' => $e->getMessage(),
            ]);
        }
    }

    protected function saveFilesCapPhat(string $paths): string
    {
        $donViid = $this->currentUser->donViId();
        $username = $this->currentUser->username();
        $disk = Storage::disk('public');

        $permanentBase = "uploads/{$donViid}/{$username}/files/TiepNhanPhoiVBCC";
        $results = [];

        foreach (explode('|', $paths) as $p) {
            $p = trim($p);
            if (!$p) {
                continue;
            }

            // grab only the path portion (e.g. "/storage/uploads/…")
            $urlPath = parse_url($p, PHP_URL_PATH);

            // if it already lives under our permanent folder, keep it
            if (Str::startsWith($urlPath, "/storage/{$permanentBase}/")) {
                $results[] = $urlPath;
                continue;
            }

            // otherwise treat it as a "temp" file under /storage/…
            // strip leading "/" and "storage/" to get the disk key
            $diskKey = preg_replace('#^/storage/#', '', $urlPath);

            // build a new filename in the permanent folder
            $name = pathinfo($diskKey, PATHINFO_FILENAME);
            $ext = pathinfo($diskKey, PATHINFO_EXTENSION);
            $slug = Str::slug($name);
            $newName = "{$slug}-" . time() . ".{$ext}";
            $newKey = "{$permanentBase}/{$newName}";

            // move on the 'public' disk (move = copy + delete original)
            $disk->move($diskKey, $newKey);

            // push the new public URL
            $results[] = '/storage/' . $newKey;
        }

        // re‐implode with '|'
        return implode('|', $results);
    }
    

}
