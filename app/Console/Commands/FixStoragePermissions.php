<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class FixStoragePermissions extends Command
{
    protected $signature = 'storage:fix-permissions';
    protected $description = 'Fix storage permissions and ensure symlink exists';

    public function handle()
    {
        $this->info('🔧 Fixing storage permissions and symlinks...');

        // Check if storage symlink exists
        $publicStoragePath = public_path('storage');
        $storageAppPublicPath = storage_path('app/public');

        if (!File::exists($publicStoragePath)) {
            $this->warn('⚠️  Storage symlink does not exist. Creating...');
            $this->call('storage:link');
        } else {
            $this->info('✅ Storage symlink exists');
        }

        // Fix permissions for storage directories
        $storagePaths = [
            storage_path('app/public'),
            storage_path('app/public/congthongtin'),
            storage_path('framework'),
            storage_path('logs'),
        ];

        foreach ($storagePaths as $path) {
            if (File::exists($path)) {
                // Set directory permissions to 755
                chmod($path, 0755);
                $this->info("✅ Fixed permissions for: " . basename($path));
                
                // Fix permissions for all subdirectories and files
                $this->fixPermissionsRecursively($path);
            } else {
                $this->warn("⚠️  Path does not exist: {$path}");
            }
        }

        // Verify APP_URL matches current server
        $appUrl = config('app.url');
        $this->info("📍 Current APP_URL: {$appUrl}");
        
        if ($appUrl === 'http://localhost' && request()->getHost() === 'localhost:8000') {
            $this->warn('⚠️  APP_URL might need to be updated to http://localhost:8000');
            $this->info('💡 Run: php artisan config:clear after updating .env');
        }

        $this->info('🎉 Storage permissions and symlinks have been fixed!');
        return 0;
    }

    private function fixPermissionsRecursively($path)
    {
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($path, \RecursiveDirectoryIterator::SKIP_DOTS),
            \RecursiveIteratorIterator::SELF_FIRST
        );

        foreach ($iterator as $item) {
            if ($item->isDir()) {
                chmod($item->getRealPath(), 0755);
            } else {
                chmod($item->getRealPath(), 0644);
            }
        }
    }
}
