<?php

use App\Http\Controllers\HeThong\CauHinhHeThongController;
use App\Http\Controllers\QuanLy\DuyetYeuCauCSNDVBCCController;
use App\Http\Controllers\CongThongTin\TraCuuVBCC_CTTController;
use App\Http\Controllers\QuanLy\YeuCauCSNDVBCCController;
use App\Http\Controllers\TraCuu\TraCuuQuyetDinhCSNDVBCCController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\SoHoa\HoSoController;
use App\Http\Controllers\DanhMuc\LyDoController;
use App\Http\Controllers\HeThong\AuthController;
use App\Http\Controllers\hethong\LogsController;
use App\Http\Controllers\QuanLy\SoGocController;
use App\Http\Controllers\DanhMuc\DonViController;
use App\Http\Controllers\DanhMuc\KyThiController;
use App\Http\Controllers\SoHoa\HopHoSoController;
use App\Http\Controllers\DanhMuc\CapHocController;
use App\Http\Controllers\DanhMuc\ChucVuController;
use App\Http\Controllers\DanhMuc\DanTocController;
use App\Http\Controllers\DanhMuc\LopHocController;
use App\Http\Controllers\DanhMuc\MonHocController;
use App\Http\Controllers\DanhMuc\HoiDongController;
use App\Http\Controllers\DanhMuc\KhoaThiController;
use App\Http\Controllers\DanhMuc\NgonNguController;
use App\Http\Controllers\DanhMuc\TieuChiController;
use App\Http\Controllers\DanhMuc\TonGiaoController;
use App\Http\Controllers\DanhMuc\XepLoaiController;
use App\Http\Controllers\HeThong\ProfileController;
use App\Http\Controllers\QuanLy\DoiTuongController;
use App\Http\Controllers\QuanLy\TongQuanController;
use App\Http\Controllers\BaoCao\BaoCaoMauController;
use App\Http\Controllers\DanhMuc\HeDaoTaoController;
use App\Http\Controllers\DanhMuc\KeLuuTruController;
use App\Http\Controllers\DanhMuc\NhanVienController;
use App\Http\Controllers\DanhMuc\PhongBanController;
use App\Http\Controllers\DanhMuc\QuocTichController;
use App\Http\Controllers\DungChung\QuanLyController;
use App\Http\Controllers\HeThong\ChucNangController;
use App\Http\Controllers\SoHoa\TraCuuHoSoController;
use App\Http\Controllers\DanhMuc\CapToChucController;
use App\Http\Controllers\DanhMuc\DonViTinhController;
use App\Http\Controllers\DanhMuc\KhoLuuTruController;
use App\Http\Controllers\DanhMuc\TrangThaiController;
use App\Http\Controllers\DungChung\DanhMucController;
use App\Http\Controllers\HeThong\NguoiDungController;
use App\Http\Controllers\TraCuu\TraCuuVBCCController;
use App\Http\Controllers\DanhMuc\LoaiLuuTruController;
use App\Http\Controllers\DanhMuc\LoaiVanBanController;
use App\Http\Controllers\HeThong\DoiMatKhauController;
use App\Http\Controllers\HeThong\KiemTraXoaController;
use App\Http\Controllers\QuanLy\HuyPhoiVBCCController;
use App\Http\Controllers\QuanLy\SoGocBanSaoController;
use App\Http\Controllers\DanhMuc\CheDoSuDungController;
use App\Http\Controllers\DanhMuc\LoaiChungTuController;
use App\Http\Controllers\DanhMuc\MucDoTinCayController;
use App\Http\Controllers\DanhMuc\NhomTaiLieuController;
use App\Http\Controllers\DanhMuc\PhongLuuTruController;
use App\Http\Controllers\DungChung\DungChungController;
use App\Http\Controllers\HeThong\CauHinhMailController;
use App\Http\Controllers\BaoCao\ThongKeSDPhoiController;
use App\Http\Controllers\DanhMuc\LoaiDiaBanHCController;
use App\Http\Controllers\TraCuu\TraCuuHocSinhController;
use App\Http\Controllers\BaoCao\DanhSachBaoCaoController;
use App\Http\Controllers\DanhMuc\LoaiHinhDonViController;
use App\Http\Controllers\HeThong\NhomNguoiDungController;
use App\Http\Controllers\DanhMuc\HinhThucDaoTaoController;
use App\Http\Controllers\DanhMuc\TinhTrangVatLyController;
use App\Http\Controllers\HeThong\NhatKyDangNhapController;
use App\Http\Controllers\QuanLy\BangDiemHocSinhController;
use App\Http\Controllers\QuanLy\CapPhatPhoiVBCCController;
use App\Http\Controllers\DanhMuc\DiaBanHanhChinhController;
use App\Http\Controllers\DungChung\GenericExportController;
use App\Http\Controllers\HeThong\ThietLapHeThongController;
use App\Http\Controllers\QuanLy\CapBangTotNghiepController;
use App\Http\Controllers\QuanLy\TiepNhanPhoiVBCCController;
use App\Http\Controllers\QuanLy\DonXinCapBanSaoController;
use App\Http\Controllers\QuanLy\DuyetDonXinCapBanSaoController;
use App\Http\Controllers\BaoCao\ThietLapMauBaoCaoController;
use App\Http\Controllers\QuanLy\DonXinCapPhoiBangController;
use App\Http\Controllers\QuanLy\HoSoTotNghiepTHPTController;
use App\Http\Controllers\CongThongTin\CongThongTinController;
use App\Http\Controllers\DanhMuc\MauVanBangChungChiController;
use App\Http\Controllers\HeThong\CauHinhGuiThongBaoController;
use App\Http\Controllers\HeThong\ThietLapMauHeThongController;
use App\Http\Controllers\SoHoa\TraCuuHoSoDangThuMucController;
use App\Http\Controllers\DanhMuc\LoaiPhoiVanBangChungChiController;
use App\Http\Controllers\DanhMuc\LoaiVanBangChungChiController;
use App\Http\Controllers\BaoCao\DungChungController as DungChungBaoCao;
use App\Http\Controllers\BienTapCongThongTin\PheDuyetBinhLuanController;
use App\Http\Controllers\BienTapCongThongTin\TrangController as BienTapTrangController;
use App\Http\Controllers\BienTapCongThongTin\TinTucController as BienTapTinTucController;
use App\Http\Controllers\BaoCao\ThongKeSLCapBanSaoController;
use App\Http\Controllers\BienTapCongThongTin\LoaiTinTucController as BienTapLoaiTinTucController;
use App\Http\Controllers\BienTapCongThongTin\ThietLapWebsiteController as BienTapThietLapWebsiteController;
use App\Http\Controllers\BaoCao\ThongKeHSTNController;
use App\Http\Controllers\QuanLy\QuyetDinhChinhSuaVanBangController;
use App\Http\Controllers\BaoCao\BaoCaoCapPhatVBController as BaoCaoCapPhatVBController;
use App\Http\Controllers\QuanLy\QuyetDinhHuyVanBangController;
use App\Http\Controllers\QuanLy\DuyetDonXinCapPhoiBangController;
use App\Http\Controllers\QuanLy\PhieuYeuCauHuyVBCCController;
use App\Http\Controllers\QuanLy\PheDuyetPhieuYeuCauHuyVBCCController;




#region Guest
// ====================
// Tuyến đường công khai (Guest)
// ====================
// Chỉ cho phép người dùng chưa đăng nhập
Route::middleware('guest')->group(function () {
    Route::get('login', [AuthController::class, 'showLogin'])->name('login');
    Route::post('login', [AuthController::class, 'login']);
    Route::get('register', [AuthController::class, 'showRegister'])->name('register');
    Route::post('register', [AuthController::class, 'register']);
});

// ====================
// Trang chính
// ====================
Route::get('/', [CongThongTinController::class, 'index'])->name('home');

// ====================
// Tra cuu cổng thông tin
// ====================
// Route::get('congthongtin/tracuuvbcc_ctt', [TraCuuVBCC_CTTController::class, 'index'])->name('congthongtin.tracuuvbcc_ctt.index');
Route::get('/tra-cuu', [TraCuuVBCC_CTTController::class, 'index'])->name('congthongtin.tracuuvbcc_ctt.index');
Route::prefix('/tra-cuu')->name('congthongtin.tracuuvbcc_ctt.')->group(function () {
    Route::get('getAll', [TraCuuVBCC_CTTController::class, 'getAll'])->name('db.getAll');
    Route::get('getAllByID', [TraCuuVBCC_CTTController::class, 'getAllByID'])->name('db.getAllByID');
    Route::get('getlist-quyetdinh', [DanhMucController::class, 'getListQuyetDinh'])->name('comboQuyetDinh');
    Route::get('getlist-sogoc', [DanhMucController::class, 'getListSoGoc'])->name('comboSoGoc');
    Route::get('getAllTiepNhanPhoiVBCC', [TiepNhanPhoiVBCCController::class, 'getAllTiepNhanPhoiVBCC'])->name('getAllTiepNhanPhoiVBCC');

});



// ====================
// Cổng thông tin routes
// ====================
Route::prefix('congthongtin')->name('congthongtin.')->group(function () {
    Route::post('/contact', [CongThongTinController::class, 'contact'])->name('contact');
    Route::get('/services', [CongThongTinController::class, 'getServices'])->name('services');
    Route::get('/team', [CongThongTinController::class, 'getTeam'])->name('team');
    //Route::get('/tra-cuu', [CongThongTinController::class, 'traCuu'])->name('tracuu');
    Route::get('/test-header-edit', function () {
        $thietLapWebsite = \App\Models\CongThongTin\ThietLapWebsite::first();
        return view('congthongtin.test-header-edit', compact('thietLapWebsite'));
    })->name('test-header-edit');

    Route::get('/test-header-debug', function () {
        $thietLapWebsite = \App\Models\CongThongTin\ThietLapWebsite::first();
        return view('congthongtin.test-header-debug', compact('thietLapWebsite'));
    })->name('test-header-debug');

    Route::get('/test-footer-cot1', function () {
        $thietLapWebsite = \App\Models\CongThongTin\ThietLapWebsite::first();
        $footerComponents = \App\Models\CongThongTin\TuyChinhCongThongTin::where('LoaiComponent', 'footer')->get();
        return view('congthongtin.test-footer-cot1', compact('thietLapWebsite', 'footerComponents'));
    })->name('test-footer-cot1');

    // Test API without CSRF for debugging
    Route::post('/api/company/test-update', function (\Illuminate\Http\Request $request) {
        try {
            \Illuminate\Support\Facades\Log::info('Test Company API Update:', $request->all());

            $thietLap = \App\Models\CongThongTin\ThietLapWebsite::first();

            $updateData = [
                'TenDonVi' => $request->companyName ?? 'TEST DEFAULT',
                'TenPhanMem' => $request->softwareName ?? 'TEST SOFTWARE',
                'Facebook' => $request->facebookUrl ?? '#',
                'Youtube' => $request->youtubeUrl ?? '#',
                'NgayCapNhat' => now(),
            ];

            if ($thietLap) {
                $thietLap->update($updateData);
                $message = 'Test update successful!';
            } else {
                $updateData['Ma'] = '2';
                $updateData['NgayTao'] = now();
                $thietLap = \App\Models\CongThongTin\ThietLapWebsite::create($updateData);
                $message = 'Test create successful!';
            }

            return response()->json([
                'err' => false,
                'msg' => $message,
                'data' => $thietLap
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'err' => true,
                'msg' => 'Test error: ' . $e->getMessage(),
            ], 500);
        }
    })->withoutMiddleware([\App\Http\Middleware\VerifyCsrfToken::class]);

    Route::get('/test-edit-sidebar', function () {
        $thietLapWebsite = \App\Models\CongThongTin\ThietLapWebsite::first();
        return view('congthongtin.test-edit-sidebar', compact('thietLapWebsite'));
    })->name('test-edit-sidebar');

    Route::get('/test-logo-edit', function () {
        return view('congthongtin.test-logo-edit');
    })->name('test-logo-edit');

    Route::get('/debug-logo', function () {
        $thietLapWebsite = \App\Models\CongThongTin\ThietLapWebsite::first();
        return view('congthongtin.debug-logo', compact('thietLapWebsite'));
    })->name('debug-logo');

    // API for company info update (footer cot-1)
    Route::put('/api/company/update', function (\Illuminate\Http\Request $request) {
        try {
            // Debug logging
            \Illuminate\Support\Facades\Log::info('Company API Update Request:', [
                'all_data' => $request->all(),
                'json_data' => $request->json()->all(),
                'method' => $request->method(),
                'content_type' => $request->header('Content-Type'),
                'csrf_token' => $request->header('X-CSRF-TOKEN')
            ]);

            $request->validate([
                'companyName' => 'required|string|max:255',
                'softwareName' => 'required|string|max:255',
                'email' => 'nullable|email|max:255',
                'phone' => 'nullable|string|max:20',
                'facebookUrl' => 'nullable|string|max:255',
                'youtubeUrl' => 'nullable|string|max:255',
                'logoUrl' => 'nullable|string|max:500',
            ]);

            $thietLap = \App\Models\CongThongTin\ThietLapWebsite::first();

            $updateData = [
                'TenDonVi' => $request->companyName,
                'TenPhanMem' => $request->softwareName,
                'Email' => $request->email ?: '',
                'SoDienThoai' => $request->phone ?: '',
                'Facebook' => $request->facebookUrl ?: '#',
                'Youtube' => $request->youtubeUrl ?: '#',
                'NgayCapNhat' => now(),
            ];

            // Update logo if provided
            if ($request->logoUrl) {
                $updateData['LogoUrl'] = $request->logoUrl;
            }

            if (!$thietLap) {
                // Create new
                $updateData['Ma'] = '2';
                $updateData['NgayTao'] = now();
                $updateData['NguoiTao'] = 'Public Edit';

                $thietLap = \App\Models\CongThongTin\ThietLapWebsite::create($updateData);
                $message = 'Thêm thông tin công ty thành công!';
            } else {
                // Update
                $thietLap->update($updateData);
                $message = 'Cập nhật thông tin công ty thành công!';
            }

            return response()->json([
                'err' => false,
                'msg' => $message,
                'data' => $thietLap
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'err' => true,
                'msg' => 'Có lỗi xảy ra: ' . $e->getMessage(),
            ], 500);
        }
    })->name('api.company.update');

    // API for logo update (header logo)
    Route::put('/api/logo/update', function (\Illuminate\Http\Request $request) {
        try {
            // Debug logging
            \Illuminate\Support\Facades\Log::info('Logo API Update Request:', [
                'all_data' => $request->all(),
                'json_data' => $request->json()->all(),
                'method' => $request->method(),
                'content_type' => $request->header('Content-Type'),
                'csrf_token' => $request->header('X-CSRF-TOKEN')
            ]);

            // Validate request
            $request->validate([
                'logoUrl' => 'required|string|max:500',
            ]);

            // Get or create ThietLapWebsite record
            $thietLap = \App\Models\CongThongTin\ThietLapWebsite::first();
            if (!$thietLap) {
                $thietLap = \App\Models\CongThongTin\ThietLapWebsite::create([
                    'Ma' => '2',
                    'TenDonVi' => 'CÔNG TY TNHH PHÁT TRIỂN PHẦN MÊM NHẤT TÂM - NTSOFT',
                    'LogoUrl' => $request->logoUrl,
                ]);
            } else {
                $thietLap->update([
                    'LogoUrl' => $request->logoUrl,
                ]);
            }

            \Illuminate\Support\Facades\Log::info('Logo updated successfully:', [
                'id' => $thietLap->id,
                'logoUrl' => $thietLap->LogoUrl,
            ]);

            return response()->json([
                'err' => false,
                'msg' => 'Logo đã được cập nhật thành công',
                'data' => [
                    'logoUrl' => $thietLap->LogoUrl,
                ]
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            \Illuminate\Support\Facades\Log::error('Logo API Validation Error:', $e->errors());
            return response()->json([
                'err' => true,
                'msg' => 'Dữ liệu không hợp lệ: ' . implode(', ', array_flatten($e->errors())),
            ], 422);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Logo API Error:', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'err' => true,
                'msg' => 'Có lỗi xảy ra: ' . $e->getMessage(),
            ], 500);
        }
    })->name('api.logo.update');

    // API for header contact info update
    Route::get('/api/thietlapwebsite/load', function () {
        try {
            $thietLap = \App\Models\CongThongTin\ThietLapWebsite::first();

            if (!$thietLap) {
                // Create default if not exists
                $thietLap = \App\Models\CongThongTin\ThietLapWebsite::create([
                    'Ma' => '2',
                    'TenDonVi' => 'CÔNG TY TNHH PHÁT TRIỂN PHẦN MÊM NHẤT TÂM - NTSOFT',
                    'DiaChi' => 'Số H25, Đường Phan Văn Đáng, Phường 8, TP.Vĩnh Long, Tỉnh Vĩnh Long',
                    'SoDienThoai' => '(02703) 843 058',
                    'Fax' => '(02703) 843 058',
                    'Email' => '<EMAIL>',
                    'TenPhanMem' => 'HỆ THỐNG QUẢN LÝ AN TOÀN THỰC PHẨM - NTSOFT',
                    'TenPhienBan' => '2024 Copyright',
                    'Website' => 'https://nhattamsoft.vn',
                    'Facebook' => '#',
                    'Youtube' => '#',
                    'NgayTao' => now(),
                    'NguoiTao' => 'System',
                ]);
            }

            return response()->json([
                'err' => false,
                'Result' => [$thietLap]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'err' => true,
                'msg' => 'Có lỗi xảy ra: ' . $e->getMessage(),
            ], 500);
        }
    })->name('api.thietlapwebsite.load');

    Route::put('/api/thietlapwebsite/update', function (\Illuminate\Http\Request $request) {
        try {
            // Debug logging
            \Illuminate\Support\Facades\Log::info('Header API Update Request:', [
                'all_data' => $request->all(),
                'method' => $request->method(),
                'content_type' => $request->header('Content-Type'),
                'csrf_token' => $request->header('X-CSRF-TOKEN')
            ]);

            $request->validate([
                'TenDonVi' => 'required|string|max:255',
                'DiaChi' => 'required|string',
                'SoDienThoai' => 'nullable|string|max:20',
                'Fax' => 'nullable|string|max:20',
                'Email' => 'nullable|email|max:255',
                'TenPhanMem' => 'nullable|string|max:255',
                'TenPhienBan' => 'nullable|string|max:255',
                'Website' => 'nullable|url|max:255',
                'Facebook' => 'nullable|string|max:255',
                'Youtube' => 'nullable|string|max:255',
            ]);

            $thietLap = \App\Models\CongThongTin\ThietLapWebsite::first();
            \Illuminate\Support\Facades\Log::info('Current ThietLapWebsite record:', ['record' => $thietLap]);

            $updateData = [
                'TenDonVi' => $request->TenDonVi,
                'DiaChi' => $request->DiaChi,
                'SoDienThoai' => $request->SoDienThoai,
                'Fax' => $request->Fax,
                'Email' => $request->Email,
                'TenPhanMem' => $request->TenPhanMem,
                'TenPhienBan' => $request->TenPhienBan,
                'Website' => $request->Website,
                'Facebook' => $request->Facebook,
                'Youtube' => $request->Youtube,
                'NgayCapNhat' => now(),
            ];

            if (!$thietLap) {
                // Create new
                $updateData['Ma'] = '2';
                $updateData['NgayTao'] = now();
                $updateData['NguoiTao'] = 'Public Edit';

                \Illuminate\Support\Facades\Log::info('Creating new ThietLapWebsite record:', $updateData);
                $thietLap = \App\Models\CongThongTin\ThietLapWebsite::create($updateData);
                $message = 'Thêm thông tin thành công!';
            } else {
                // Update
                \Illuminate\Support\Facades\Log::info('Updating ThietLapWebsite record:', [
                    'id' => $thietLap->id,
                    'update_data' => $updateData
                ]);

                $result = $thietLap->update($updateData);
                \Illuminate\Support\Facades\Log::info('Update result:', ['success' => $result]);

                $message = 'Cập nhật thông tin thành công!';
            }

            // Reload to get fresh data
            $thietLap = $thietLap->fresh();
            \Illuminate\Support\Facades\Log::info('Final ThietLapWebsite record:', ['record' => $thietLap]);

            return response()->json([
                'err' => false,
                'msg' => $message,
                'data' => $thietLap
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'err' => true,
                'msg' => 'Có lỗi xảy ra: ' . $e->getMessage(),
            ], 500);
        }
    })->name('api.thietlapwebsite.update');

    // Debug route to check database directly
    Route::get('/debug/thietlapwebsite', function () {
        $thietLap = \App\Models\CongThongTin\ThietLapWebsite::first();
        return response()->json([
            'record_exists' => $thietLap ? true : false,
            'record_count' => \App\Models\CongThongTin\ThietLapWebsite::count(),
            'data' => $thietLap,
            'table_name' => (new \App\Models\CongThongTin\ThietLapWebsite())->getTable(),
            'fillable' => (new \App\Models\CongThongTin\ThietLapWebsite())->getFillable()
        ]);
    })->name('debug.thietlapwebsite');

    // Debug route to update ThietLapWebsite with contact info
    Route::get('/debug/update-contact', function () {
        $thietLap = \App\Models\CongThongTin\ThietLapWebsite::first();
        if ($thietLap) {
            $thietLap->update([
                'SoDienThoai' => '02703 843 058',
                'Email' => '<EMAIL>',
                'NgayCapNhat' => now()
            ]);
            return response()->json([
                'success' => true,
                'message' => 'Updated contact info',
                'data' => $thietLap->fresh()
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'ThietLapWebsite not found'
            ]);
        }
    })->name('debug.update-contact');
});

// ====================
// Tin tức routes (public)
// ====================
Route::prefix('tin-tuc')->name('news.')->group(function () {
    Route::get('/', [CongThongTinController::class, 'news'])->name('index');
    Route::get('/{slug}', [CongThongTinController::class, 'show'])->name('detail');

    // Bình luận routes
    Route::post('/{slug}/binh-luan', [CongThongTinController::class, 'storeBinhLuan'])->name('binh-luan.store');
    Route::get('/{slug}/binh-luan', [CongThongTinController::class, 'getBinhLuan'])->name('binh-luan.get');
});

// ====================
// Demo routes (public)
// ====================
Route::get('/demo-header', function () {
    return view('congthongtin.demo-header');
})->name('demo.header');

// Test route for bang tot nghiep tinh
Route::get('/test-bang-tot-nghiep', function () {
    return view('test-bang-tot-nghiep');
})->name('test.bang.tot.nghiep');

// Test route public cho bảng điểm
Route::get('/test-bangdiem-public/{id}', function ($id) {
    try {
        $hocSinhObjectId = new \MongoDB\BSON\ObjectId($id);
        $bangDiemChiTietList = \App\Models\QuanLy\BangDiem_ChiTiet::where(function ($query) use ($id, $hocSinhObjectId) {
            $query->where('HocSinhID', $id)
                ->orWhere('HocSinhID', $hocSinhObjectId)
                ->orWhere('HocSinhID', (string) $hocSinhObjectId);
        })->get();

        return response()->json([
            'Err' => false,
            'count' => $bangDiemChiTietList->count(),
            'message' => 'Tìm thấy ' . $bangDiemChiTietList->count() . ' bảng điểm',
            'sample' => $bangDiemChiTietList->first() ? [
                'SBD' => $bangDiemChiTietList->first()->SBD,
                'DiemUT' => $bangDiemChiTietList->first()->DiemUT,
                'PhongThi' => $bangDiemChiTietList->first()->PhongThi
            ] : null
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'Err' => true,
            'Msg' => $e->getMessage()
        ]);
    }
});

// Test route public cho API getBangDiemByHocSinh đầy đủ
Route::get('/test-bangdiem-full/{id}', function ($id) {
    try {
        // Sử dụng logic từ controller
        $hocSinhId = $id;
        $hocSinhObjectId = new \MongoDB\BSON\ObjectId($hocSinhId);

        // 1. Lấy thông tin học sinh
        $hocSinh = \App\Models\QuanLy\DoiTuong::with(['donViHoc', 'lopHoc', 'dienUuTien'])->find($hocSinhObjectId);
        if (!$hocSinh) {
            return response()->json(['Err' => true, 'Msg' => 'Không tìm thấy học sinh']);
        }

        // 2. Lấy danh sách bảng điểm chi tiết
        $bangDiemChiTietList = \App\Models\QuanLy\BangDiem_ChiTiet::where(function ($query) use ($hocSinhId, $hocSinhObjectId) {
            $query->where('HocSinhID', $hocSinhId)
                ->orWhere('HocSinhID', $hocSinhObjectId)
                ->orWhere('HocSinhID', (string) $hocSinhObjectId);
        })->get();

        $result = [];

        foreach ($bangDiemChiTietList as $bangDiemChiTiet) {
            // 3. Lấy thông tin bảng điểm
            $bangDiem = \App\Models\QuanLy\BangDiem::find($bangDiemChiTiet->BangDiemId);
            if (!$bangDiem)
                continue;

            // 4. Lấy thông tin quyết định
            $quyetDinh = \App\Models\QuanLy\QuyetDinh::where('NamTotNghiep', $bangDiem->NamHoc)->first();

            // 5. Lấy thông tin tốt nghiệp
            $hocSinhTN = \App\Models\QuanLy\HocSinhTN::where(function ($query) use ($hocSinhId, $hocSinhObjectId) {
                $query->where('HocSinhID', $hocSinhId)
                    ->orWhere('HocSinhID', $hocSinhObjectId)
                    ->orWhere('HocSinhID', (string) $hocSinhObjectId);
            })->first();

            // 6. Lấy thông tin năm học
            $namTotNghiep = '';
            if ($bangDiem->NamHoc) {
                $namHoc = \App\Models\DanhMuc\NamHoc::find($bangDiem->NamHoc);
                if ($namHoc) {
                    $namTotNghiep = $namHoc->TuNam . ' - ' . $namHoc->DenNam;
                } else {
                    // Nếu không tìm thấy trong bảng namhoc, hiển thị ObjectId
                    $namTotNghiep = '2024 - 2025'; // Default cho test
                }
            }

            // 7. Tạo monHocList từ DiemMonThi
            $monHocList = [];
            if (!empty($bangDiemChiTiet->DiemMonThi)) {
                foreach ($bangDiemChiTiet->DiemMonThi as $monHocId => $diem) {
                    $monHoc = \App\Models\DanhMuc\MonHoc::find($monHocId);
                    if ($monHoc) {
                        $monHocList[] = [
                            'id' => (string) $monHoc->_id,
                            'tenMonHoc' => $monHoc->tenMonHoc,
                            'diemThi' => $diem,
                            'diemCuoiCap' => $bangDiemChiTiet->DiemCuoiCap[$monHocId] ?? 0
                        ];
                    }
                }
            }

            $result[] = [
                'bangDiemChiTiet' => [
                    'DiaDiemThi' => $bangDiemChiTiet->DiaDiemThi ?? '',
                    'PhongThi' => $bangDiemChiTiet->PhongThi ?? '',
                    'SBD' => $bangDiemChiTiet->SBD ?? '',
                    'DiemUT' => $bangDiemChiTiet->DiemUT ?? '',
                    'DiemMonThi' => $bangDiemChiTiet->DiemMonThi ?? [],
                    'DiemCuoiCap' => $bangDiemChiTiet->DiemCuoiCap ?? []
                ],
                'quyetDinh' => [
                    'SoQuyetDinh' => $quyetDinh->SoQuyetDinh ?? '',
                    'NgayBanHanh' => $quyetDinh->NgayBanHanh ?? '',
                    'NguoiKy' => $quyetDinh->NguoiKy ?? ''
                ],
                'hocSinhTN' => [
                    'KetQua_HT' => $hocSinhTN->KetQua_HT ?? '',
                    'KetQua_RL' => $hocSinhTN->KetQua_RL ?? '',
                    'KetQuaTN' => $hocSinhTN->KetQuaTN ?? ''
                ],
                'namTotNghiep' => $namTotNghiep,
                'monHocList' => $monHocList
            ];
        }

        return response()->json([
            'Err' => false,
            'Result' => $result
        ]);

    } catch (\Exception $e) {
        return response()->json([
            'Err' => true,
            'Msg' => $e->getMessage()
        ]);
    }
});

// Debug route for tin tuc
Route::get('/debug-tintuc', function () {
    return view('debug_tintuc');
})->name('debug.tintuc');

Route::get('/test-responsive-header', function () {
    // Get menu data for the header
    $loaiTinTucsMenu = \App\Models\CongThongTin\LoaiTinTuc::where('DangSD', true)
        ->where('HienThiHDSD', true)
        ->orderBy('TenLoaiTinTuc')
        ->get();

    $thietLapWebsite = \App\Models\CongThongTin\ThietLapWebsite::first();

    return view('congthongtin.test-responsive', compact('loaiTinTucsMenu', 'thietLapWebsite'));
})->name('test.responsive.header');







// Upload image for Cong Thong Tin (with basic auth but no complex middleware)
Route::middleware(['auth'])->group(function () {
    Route::post('/congthongtin/upload-image', [App\Http\Controllers\CongThongTin\DungChung\UploadFileController::class, 'uploadImage'])
        ->name('congthongtin.upload.image');
});

Route::prefix('dungchung')->name('dungchung.')->group(function () {
    //Nhóm danh mục (get combo,...)
    Route::get('getlist-nam', [DanhMucController::class, 'getNam'])->name('comboNam');
    Route::get('getlist-namhoc', [DanhMucController::class, 'getListNamHoc'])->name('comboNamHoc');
});

#endregion




// ====================
// API Routes (some public, some protected)
// ====================
Route::middleware('api')
    ->prefix('api')
    ->group(base_path('routes/api.php'));

// ====================
// Tuyến đường bảo vệ (Auth)
// ====================
// Chỉ cho phép người dùng đã đăng nhập
Route::middleware('auth')->group(function () {


    #region Dùng chung
    // Đăng xuất
    Route::post('logout', [AuthController::class, 'logout'])->name('logout');

    // ====================
    // Chức năng dùng chung
    // ====================
    Route::prefix('dungchung')->name('dungchung.')->group(function () {
        Route::get('/lay-ma-tutang/{kyhieuLoaiPhieu}/{bangDuLieu}/{cotDuLieu}', [DungChungController::class, 'LayMaTuTang'])->name('lay-ma-tutang');
        Route::get('/kiem-tra-ton-tai/{collection}/{field}/{value}', [DungChungController::class, 'kiemTraTonTai'])->name('kiemTraTonTai');
        Route::post('/kiem-tra-ton-tai-nhieu-cot', [DungChungController::class, 'kiemTraTonTaiNhieuCot'])->name('kiemTraTonTaiNhieuCot');
        Route::get('/kiem-tra-phu-thuoc', [DungChungController::class, 'kiemTraPhuThuoc'])->name('kiemTraPhuThuoc');
        Route::get('/kiem-tra-xoa', [DungChungController::class, 'kiemTraXoa'])->name('kiemTraXoa');
        Route::get('/leftbar', [DungChungController::class, 'leftbar'])->name('left-bar');
        Route::post('/luu-dang-sd', [DungChungController::class, 'LuuDangSD'])->name('luu-dang-sd');
        Route::post('/luu-dang-sd-nhieu', [DungChungController::class, 'LuuDangSDNhieu'])->name('luu-dang-sd-nhieu');
        //kiemTraXoaV1
        Route::get('/kiemTraXoaV1', [DungChungController::class, 'kiemTraXoaV1'])->name('kiem-tra-xoa-v1');
        Route::get('/quyen/{permissionKey}', [DungChungController::class, 'getQuyen'])->name('getPermission');
        Route::get('/niendo', [DungChungController::class, 'getNienDoFromSession'])->name('niendo');
        Route::get('/ds_kybaocao', [DungChungController::class, 'getDanhSachKyBaoCao'])->name('ds_kybaocao');
        Route::POST('/Update_OneColum', [DungChungController::class, 'Update_OneColum'])->name('Update_OneColum');

        //Nhóm danh mục (get combo,...)
        Route::prefix('danhmuc')->name('danhmuc.')->group(function () {
            Route::get('getlist-phongban', [DanhMucController::class, 'getPhongBanList'])->name('comboPhongBan');
            Route::get('getlist-donvi', [DanhMucController::class, 'getDonViList'])->name('comboDonVi');
            Route::get('getlist-gioitinh', [DanhMucController::class, 'getGioiTinhList'])->name('comboGioiTinh');
            Route::get('getlist-chucvu', [DanhMucController::class, 'getChucVuList'])->name('comboChucVu');
            Route::get('getlist-nhanvien', [DanhMucController::class, 'getNhanVienList'])->name('comboNhanVien');
            Route::get('getlist-loaihinhdv', [DanhMucController::class, 'getLoaiHinhDonViList'])->name('comboLoaiHinhDonVi');
            Route::get('getlist-monhoc', [DanhMucController::class, 'getListMonHoc'])->name('comboMonHoc');
            Route::get('getlist-captochuc', [DanhMucController::class, 'getListCapToChuc'])->name('comboCapToChuc');
            Route::get('getlist-caphoc', [DanhMucController::class, 'getListCapHoc'])->name('comboCapHoc');
            Route::get('getlist-kythi', [DanhMucController::class, 'getListKyThi'])->name('comboKyThi');
            Route::get('getlist-khoathi', [DanhMucController::class, 'getListKhoaThi'])->name('comboKhoaThi');
            Route::get('getlist-quyetdinh', [DanhMucController::class, 'getListQuyetDinh'])->name('comboQuyetDinh');
            Route::get('getlist-user', [DanhMucController::class, 'getListUserByDonVi'])->name('comboUser');
            Route::get('getlist-donvi_tree', [DanhMucController::class, 'GetListDonVi_tree'])->name('comboDonVi_tree');
            Route::get('getlist-donvi2', [DanhMucController::class, 'GetListDonVi'])->name('comboDonVi2');
            Route::get('getlist-hinhthucdaotao', [DanhMucController::class, 'getListHinhThucDaoTao'])->name('comboHTDT');
            Route::get('getlist-hoidong', [DanhMucController::class, 'GetListHoiDong'])->name('comboHoiDong');
            Route::get('getlist-hocsinh', [DanhMucController::class, 'getDoiTuongList'])->name('comboHocSinh');
            Route::get('getlist-xeploai', [DanhMucController::class, 'getListXepLoai'])->name('comboXepLoai');
            Route::get('getlist-dvhs', [DanhMucController::class, 'GetListDonViCoHocSinh'])->name('GetListDonViCoHocSinh');
            Route::get('getlist-hsdv', [DanhMucController::class, 'GetListHocSinhByDonVi'])->name('GetListHocSinhByDonVi');
            Route::get('getlist-caphoc', [DanhMucController::class, 'getListCapHoc'])->name('comboCapHoc');
            Route::get('getlist-sogoc', [DanhMucController::class, 'getListSoGoc'])->name('comboSoGoc');
            Route::get('getlist-mauvbcc', [DanhMucController::class, 'getMauVanBangChungChi'])->name('comboMauVanBangChungChi');
            Route::get('getlist-dienuutien', [DanhMucController::class, 'getListDienUuTien'])->name('comboDienUuTien');
            Route::get('getlist-namhoc', [DanhMucController::class, 'getListNamHoc'])->name('comboNamHoc');
            Route::get('getlist-lophoc', [DanhMucController::class, 'getListLopHoc'])->name('comboLopHoc');
            Route::get('getlist-bangtotnghiepxemhs', [DanhMucController::class, 'getBangTotNghiepXemHS'])->name('getBangTotNghiepXemHS');
            Route::get('getlist-bangdiembyhocsinh', [DanhMucController::class, 'getBangDiemByHocSinh'])->name('getBangDiemByHocSinh');

            // Test route không cần auth
            Route::get('test-bangdiem/{id}', function ($id) {
                try {
                    $hocSinhObjectId = new \MongoDB\BSON\ObjectId($id);
                    $bangDiemChiTietList = \App\Models\QuanLy\BangDiem_ChiTiet::where(function ($query) use ($id, $hocSinhObjectId) {
                        $query->where('HocSinhID', $id)
                            ->orWhere('HocSinhID', $hocSinhObjectId)
                            ->orWhere('HocSinhID', (string) $hocSinhObjectId);
                    })->get();

                    return response()->json([
                        'Err' => false,
                        'count' => $bangDiemChiTietList->count(),
                        'data' => $bangDiemChiTietList->take(1)->toArray()
                    ]);
                } catch (\Exception $e) {
                    return response()->json([
                        'Err' => true,
                        'Msg' => $e->getMessage()
                    ]);
                }
            })->name('testBangDiem');
            Route::get('getlist-donvitinh', [DanhMucController::class, 'getDonViTinhList'])->name('comboDonViTinh');
            Route::get('getlist-hinhthucnhanphoi', [DanhMucController::class, 'getHinhThucNhanPhoiList'])->name('comboHinhThucNhanPhoi');
        });
    });


    // ====================
    // Xuất dữ liệu
    // ====================
    Route::prefix('export')->name('export.')->group(function () {
        Route::post('excel_v2', [GenericExportController::class, 'excel_v2'])->name('excel_v2');
        Route::post('excel', [GenericExportController::class, 'excel'])->name('excel');
        Route::post('pdf', [GenericExportController::class, 'pdf'])->name('pdf');
        Route::post('pdf_v2', [GenericExportController::class, 'pdf_v2'])->name('pdf_v2');
    });
    #endregion

    #region Hệ thống
    Route::prefix('hethong')->name('hethong.')->group(function () {

        Route::prefix('doimatkhau')->group(function () {
            Route::get('/', [DoiMatKhauController::class, 'index']);
            Route::post('/doimatkhau', [DoiMatKhauController::class, 'doimatkhau'])->name('doimatkhau');
        });

        Route::prefix('profile')->name('profile.')->group(function () {
            Route::get('/', [ProfileController::class, 'index']);
            Route::get('get-user-hientai', [ProfileController::class, 'getInfo'])->name('getUser');
            Route::get('get-nhanvien', [ProfileController::class, 'getNhanVienFromID'])->name('getNhanVien');
            Route::post('avatar-url', [ProfileController::class, 'updateAvatarUrl'])->name('updateAvatar');
            Route::post('luuthongtin', action: [ProfileController::class, 'luuThongTin'])->name('luuThongTin');
            Route::put('updateNienDo', [ProfileController::class, 'updateNienDo'])->name('updateNienDo');
        });

        //Nhật ký đăng nhập
        Route::prefix('nhatkydangnhap')->group(function () {
            Route::get('/', [NhatKyDangNhapController::class, 'index']);
            Route::get('/get-users', [NhatKyDangNhapController::class, 'getAllDanhSachNguoiDung'])->name('getAllDanhSachNguoiDung');
            Route::get('/get-logs', [NhatKyDangNhapController::class, 'getAllNhatKySuDung'])->name('getAllNhatKySuDung');
            Route::get('/load-units', [NhatKyDangNhapController::class, 'loadDonVi'])->name('loadDonVi');
            Route::get('/get-by-unit', [NhatKyDangNhapController::class, 'getDonVi'])->name('getDonVi');
            Route::get('/log-details', [NhatKyDangNhapController::class, 'getLogDetails'])->name('getChiTietLog');
        });

        //Nhóm người dùng
        Route::prefix('nhomnguoidung')
            ->name('nhomnguoidung.')
            ->group(function () {

                Route::get('/', [NhomNguoiDungController::class, 'index'])
                    ->name('index');

                Route::get('/get-all', [NhomNguoiDungController::class, 'getAll'])
                    ->name('getAll');

                Route::get('/get-permissions/{id}', [NhomNguoiDungController::class, 'getPermissions'])
                    ->name('getPermissions');

                Route::get('/get-all-functions/{id}', [NhomNguoiDungController::class, 'getAllFunctions'])
                    ->name('getAllFunctions');

                Route::post('/add-functions', [NhomNguoiDungController::class, 'addFunctions'])
                    ->name('addFunctions');

                Route::put('/update-permission', [NhomNguoiDungController::class, 'updatePermission'])
                    ->name('updatePermission');

                Route::post('/save-groups', [NhomNguoiDungController::class, 'saveUserGroups'])
                    ->name('saveUserGroups');

                Route::delete('/delete-group/{id}', [NhomNguoiDungController::class, 'deleteGroup'])
                    ->name('deleteGroup');

                Route::delete('/delete-function/{id}', [NhomNguoiDungController::class, 'deleteFunction'])
                    ->name('deleteFunction');

                Route::put('/update-name', [NhomNguoiDungController::class, 'updateGroupName'])
                    ->name('updateGroupName');

                Route::put('/update-code', [NhomNguoiDungController::class, 'updateGroupCode'])
                    ->name('updateGroupCode');

                Route::post('/refresh-users/{id}', [NhomNguoiDungController::class, 'refreshUsersByGroup'])
                    ->name('refreshUsersByGroup');
            });

        //Người dùng
        Route::prefix('nguoidung')->name('nguoidung.')->group(function () {
            Route::get('/', [NguoiDungController::class, 'index']);
            Route::get('/get-all', [NguoiDungController::class, 'getAll'])->name('getAll');
            Route::get('/get-permissions/{id}', [NguoiDungController::class, 'getPermissions'])->name('getPermiss');
            Route::get('/get-all-functions', [NguoiDungController::class, 'getAllChucNang'])->name('getChucNang');
            Route::post('/luuthongtin', [NguoiDungController::class, 'LuuThongTin'])->name('luuThongTin');
            Route::post('/add-functions', [NguoiDungController::class, 'themChucNang'])->name('themChucNang');
            Route::put('/update-permission', [NguoiDungController::class, 'updatePermission'])->name('luuThongTinQuyen');
            Route::put('/update-password', [NguoiDungController::class, 'doiMatKhau'])->name('doiMatKhau');
            Route::post('/save-groups', [NguoiDungController::class, 'saveUsers'])->name('luuThongTinUser');
            Route::delete('/delete-group/{id}', [NguoiDungController::class, 'xoaUser'])->name('xoaUser');
            Route::delete('/delete-function/{id}', [NguoiDungController::class, 'xoaChucNang'])->name('xoaChucNang');
            Route::get('loaddulieusua/{id}', [NguoiDungController::class, 'loadDuLieuSua'])->name('loadDuLieuSua');
            Route::get('GetAllDonVi', [NguoiDungController::class, 'getAllDonVi'])->name('getAllDonVi');
            Route::get('nhanvien/{donviId}', [NguoiDungController::class, 'getAllNhanVien'])->name('getAllNhanVien');
            Route::get('phongban', [NguoiDungController::class, 'getAllPhongBan'])->name('getAllPhongBan');
            Route::get('GetAllNhomNguoiDung', [NguoiDungController::class, 'getAllNhomNguoiDung'])->name('getAllNhomNguoiDung');
            Route::delete('bulk-delete-users', [NguoiDungController::class, 'bulkDeleteUser'])->name('bulkDeleteUser');
        });

        Route::prefix('logs')->name('logs.')->group(function () {
            Route::get('/', [LogsController::class, 'index']);
            Route::get('/get-all', [LogsController::class, 'getAll'])->name('getAll');
        });
    });

    Route::module('hethong', 'chucnang', ChucNangController::class);
    Route::prefix('hethong/chucnang')->name('chucnang.')->group(function () {
        Route::post('getallchucnang', [ChucNangController::class, 'getallchucnang'])->name('cn.getallchucnang');
        Route::post('getchucnangbaocao', [ChucNangController::class, 'getchucnangbaocao'])->name('cn.getchucnangbaocao');
        Route::get('kiemTraXoa', [ChucNangController::class, 'kiemTraXoa'])->name('cn.kiemTraXoa');
    });

    // ====================
    // Thiết lập hệ thống
    // ====================
    Route::prefix('hethong/thietlaphethong')->name('thietlaphethong.')->group(function () {
        Route::get('/', [ThietLapHeThongController::class, 'index'])->name('index');
        Route::get('/getdata', [ThietLapHeThongController::class, 'getdata'])->name('getdata');
        Route::post('/luuthongtin', [ThietLapHeThongController::class, 'luuthongtin'])->name('luuthongtin');
        Route::post('luuthongtinCauHinh', [ThietLapHeThongController::class, 'luuthongtinCauHinh'])->name('luuthongtinCauHinh');
        Route::get('/getdataCauHinh', [ThietLapHeThongController::class, 'getdataCauHinh'])->name('getdataCauHinh');
        Route::get('/moveFileToPermanent', [ThietLapHeThongController::class, 'moveFileToPermanent'])->name('moveFileToPermanent');
    });

    // ====================
    // cấu hình hệ thống hệ thống
    // ====================
    Route::prefix('hethong/cauhinhhethong')->name('cauhinhhethong.')->group(function () {
        Route::get('/', action: [CauHinhHeThongController::class, 'index'])->name('index');
        Route::post('luuthongtinCauHinh', [CauHinhHeThongController::class, 'luuthongtinCauHinh'])->name('luuthongtinCauHinh');
        Route::get('/getdataCauHinh', [CauHinhHeThongController::class, 'getdataCauHinh'])->name('getdataCauHinh');
        Route::get('/moveFileToPermanent', [CauHinhHeThongController::class, 'moveFileToPermanent'])->name('moveFileToPermanent');
    });
    // ====================
    // Kiểm tra xóa
    // ====================
    Route::module('hethong', 'kiemtraxoa', KiemTraXoaController::class);

    // ====================
    // Thiết lập màu hệ thống
    // ====================
    Route::module('hethong', 'thietlapmauhethong', ThietLapMauHeThongController::class);
    Route::prefix('hethong/thietlapmauhethong')->name('thietlapmauhethong.')->group(function () {
        Route::get('/', [ThietLapMauHeThongController::class, 'index'])->name('index');
        Route::get('/getdata', [ThietLapMauHeThongController::class, 'getdata'])->name('getdata');
        Route::post('/luuthongtin', [ThietLapMauHeThongController::class, 'luuthongtin'])->name('luuthongtin');
        Route::post('/xoa', [ThietLapMauHeThongController::class, 'xoa'])->name('xoa');
    });

    // ====================
    // Cấu hình gửi thông báo
    // ====================
    Route::module('hethong', 'cauhinhguithongbao', CauHinhGuiThongBaoController::class);
    Route::prefix('hethong/cauhinhguithongbao')->name('cauhinhguithongbao.')->group(function () {
        Route::post('luuthongtin', [CauHinhGuiThongBaoController::class, 'luuThongTin'])->name('luuThongTin');
        Route::delete('xoa', [CauHinhGuiThongBaoController::class, 'xoa'])->name('xoa');
        Route::get('/', [CauHinhGuiThongBaoController::class, 'index'])->name('index');
        Route::get('kiemTraXoa', [CauHinhGuiThongBaoController::class, 'kiemTraXoa'])->name('kiemTraXoa');
        Route::get('getallLoaiTB', [CauHinhGuiThongBaoController::class, 'getAllLoaiThongBao'])->name('getallLoaiTB');
        Route::get('loaddulieusua/{id}', [CauHinhGuiThongBaoController::class, 'loadDuLieuSua'])->name('loadDuLieuSua');
    });

    Route::module('hethong', 'cauhinhmail', CauHinhMailController::class);
    Route::prefix('hethong/cauhinhmail')->name('cauhinhmail.')->group(function () {
        Route::get('/get', [CauHinhMailController::class, 'getCauHinh'])->name('get');
        Route::put('/update', [CauHinhMailController::class, 'update'])->name('update');
        Route::post('/test', [CauHinhMailController::class, 'sendTestEmail'])->name('sendtestmail');
    });

    #endregion





    #region Danh mục
    // ====================
    // Danh mục nhân viên
    // ====================
    // Route::module('danhmuc','nhanvien', NhanVienController::class);
    Route::module('danhmuc', 'nhanvien', NhanVienController::class);
    Route::prefix('danhmuc/nhanvien')->name('nhanvien.')->group(function () {
        Route::get('xuatword', [DonViTinhController::class, 'xuatword'])->name('xuat.word');
        Route::get('xuatexcel', [DonViTinhController::class, 'xuatexcel'])->name('xuat.excel');
        Route::post('check-excel', [NhanVienController::class, 'checkExcel'])->name('checkExcel');
        Route::post('load-excel', [NhanVienController::class, 'loadExcel'])->name('loadExcel');
        Route::post('import-excel', [NhanVienController::class, 'importExcel'])->name('importExcel');
        Route::get('template', [NhanVienController::class, 'downloadTemplate'])->name('template');
        Route::post('load-sheet-names', [NhanVienController::class, 'loadSheetNames'])->name('loadSheetNames');
    });

    Route::module('danhmuc', 'loaichungtu', LoaiChungTuController::class);


    // ====================
    // Danh mục đơn vị tính
    // ====================
    Route::module('danhmuc', 'donvitinh', DonViTinhController::class);
    Route::prefix('danhmuc/donvitinh')->name('donvitinh.')->group(function () {
        Route::get('/xuatword', [DonViTinhController::class, 'xuatword'])->name('xuat.word');
        Route::get('/xuatexcel', [DonViTinhController::class, 'xuatexcel'])->name('xuat.excel');
        Route::post('getdonvitinh', [LoaiDiaBanHCController::class, 'getdonvitinh'])->name('dvt.getdonvitinh');
    });

    // ====================
    // Danh mục địa bàn hành chính
    // ====================
    Route::module('danhmuc', 'diabanhanhchinh', DiaBanHanhChinhController::class);
    Route::prefix('danhmuc/diabanhanhchinh')->name('diabanhanhchinh.')->group(function () {
        Route::get('kiemTraXoa', [DiaBanHanhChinhController::class, 'kiemTraXoa'])->name('db.kiemTraXoa');
        Route::get('loadDatachart', [DiaBanHanhChinhController::class, 'loadDatachart'])->name('db.loadDatachart');
        Route::post('getdiabanhc_byloai', [DiaBanHanhChinhController::class, 'getdiabanhc_byloai'])->name('db.getdiabanhc_byloai');
        Route::post('getdiabanhc_tinh', [DiaBanHanhChinhController::class, 'getdiabanhc_tinh'])->name('db.getdiabanhc_tinh');
        Route::post('getdiabanhc_noicap', [DiaBanHanhChinhController::class, 'getdiabanhc_noicap'])->name('db.getdiabanhc_noicap');
        Route::post('getdiabanhc_byidcha', [DiaBanHanhChinhController::class, 'getdiabanhc_byidcha'])->name('db.getdiabanhc_byidcha');

        Route::post('check-excel', [DiaBanHanhChinhController::class, 'checkExcel'])->name('checkExcel');
        Route::post('load-excel', [DiaBanHanhChinhController::class, 'loadExcel'])->name('loadExcel');
        Route::post('importExcel', [DiaBanHanhChinhController::class, 'importExcel'])->name('importExcel');
        Route::get('TaiFileMau', [DiaBanHanhChinhController::class, 'downloadTemplate'])->name('TaiFileMau');
        Route::post('load-sheet-names', [DiaBanHanhChinhController::class, 'loadSheetNames'])->name('loadSheetNames');
    });


    // ====================
    // Danh mục Lớp học
    // ====================
    Route::module('danhmuc', 'lophoc', LopHocController::class);
    Route::prefix('danhmuc/lophoc')->name('lophoc.')->group(function () {
        Route::post('check-excel', [LopHocController::class, 'checkExcel'])->name('checkExcel');
        Route::post('load-excel', [LopHocController::class, 'loadExcel'])->name('loadExcel');
        Route::post('import-excel', [LopHocController::class, 'importExcel'])->name('importExcel');
        Route::get('template', [LopHocController::class, 'downloadTemplate'])->name('template');
        Route::post('load-sheet-names', [LopHocController::class, 'loadSheetNames'])->name('loadSheetNames');
    });


    // ====================
    // Danh mục loại địa bàn hành chính
    // ====================
    Route::module('danhmuc', 'loaidiabanhc', LoaiDiaBanHCController::class);
    Route::prefix('danhmuc/loaidiabanhc')->name('loaidiabanhc.')->group(function () {
        Route::post('getloaidiabanhc', [LoaiDiaBanHCController::class, 'getloaidiabanhc'])->name('ldb.getloaidiabanhc');
    });

    // ====================
    // Danh mục đơn vị
    // ====================
    Route::module('danhmuc', 'donvi', DonViController::class);
    Route::prefix('danhmuc/donvi')->name('donvi.')->group(function () {
        Route::post('getalldonvi', [DonViController::class, 'getalldonvi'])->name('dv.getalldonvi');
        Route::get('kiemTraXoa', [DonViController::class, 'kiemTraXoa'])->name('dv.kiemTraXoa');
        Route::get('getDonViByID', [DonViController::class, 'getDonViByID'])->name('getDonViByID');

        Route::post('check-excel', [DonViController::class, 'checkExcel'])->name('checkExcel');
        Route::post('load-excel', [DonViController::class, 'loadExcel'])->name('loadExcel');
        Route::post('importExcel', [DonViController::class, 'importExcel'])->name('importExcel');
        Route::get('TaiFileMau', [DonViController::class, 'downloadTemplate'])->name('TaiFileMau');
        Route::post('load-sheet-names', [DonViController::class, 'loadSheetNames'])->name('loadSheetNames');
    });

    // ====================
    // Danh mục chức vụ
    // ====================
    Route::module('danhmuc', 'chucvu', ChucVuController::class);

    // ====================
    // Danh mục loại hình đơn vị
    // ====================
    Route::module('danhmuc', 'loaihinhdonvi', LoaiHinhDonViController::class);

    // ====================
    // Danh mục môn học
    // ====================
    Route::module('danhmuc', 'monhoc', MonHocController::class);
    Route::prefix('danhmuc/monhoc')->name('monhoc.')->group(function () {
        Route::post('check-excel', [MonHocController::class, 'checkExcel'])->name('checkExcel');
        Route::post('load-excel', [MonHocController::class, 'loadExcel'])->name('loadExcel');
        Route::post('importExcel', [MonHocController::class, 'importExcel'])->name('importExcel');
        Route::get('TaiFileMau', [MonHocController::class, 'downloadTemplate'])->name('TaiFileMau');
        Route::post('load-sheet-names', [MonHocController::class, 'loadSheetNames'])->name('loadSheetNames');
    });
    // ====================
    // Danh mục cấp học
    // ====================
    Route::module('danhmuc', 'caphoc', CapHocController::class);
    Route::prefix('danhmuc/caphoc')->name('caphoc.')->group(function () {
        Route::post('getcaphoc', [CapHocController::class, 'getcaphoc'])->name('ldb.getcaphoc');
    });

    // ====================
    // Danh mục cấp học
    // ====================
    Route::module('danhmuc', 'captochuc', CapToChucController::class);
    Route::prefix('danhmuc/captochuc')->name('captochuc.')->group(function () {
        Route::post('getcaptochuc', [CapToChucController::class, 'getcaptochuc'])->name('ldb.getcaptochuc');
    });

    // ====================
    // Danh mục xếp loại
    // ====================
    Route::module('danhmuc', 'xeploai', XepLoaiController::class);

    // ====================
    // Danh mục kỳ thi
    // ====================
    Route::module('danhmuc', 'kythi', KyThiController::class);
    Route::prefix('danhmuc/kythi')->name('kythi.')->group(function () {
        Route::delete('bulkDelete', [KyThiController::class, 'bulkDelete'])->name('bulkDelete');
    });
    Route::module('danhmuc', 'khoathi', KhoaThiController::class);
    Route::prefix('danhmuc/khoathi')->name('khoathi.')->group(function () {
        Route::delete('bulkDelete', [KhoaThiController::class, 'bulkDelete'])->name('bulkDelete');
    });
    // ====================

    // ====================
    // Danh mục kho lưu trữ
    // ====================
    Route::module('danhmuc', 'kholuutru', KhoLuuTruController::class);
    Route::prefix('danhmuc/kholuutru')->name('kholuutru.')->group(function () {
        Route::post('getkholuutru', [KhoLuuTruController::class, 'getkholuutru'])->name('ldb.getkholuutru');
    });

    // ====================
    // Danh mục kệ lưu trữ
    // ====================
    Route::module('danhmuc', 'keluutru', KeLuuTruController::class);
    Route::prefix('danhmuc/keluutru')->name('keluutru.')->group(function () {
        Route::post('getkeluutru', [KeLuuTruController::class, 'getkeluutru'])->name('ldb.getkeluutru');
        Route::post('getkeluutru_theoKho', [KeLuuTruController::class, 'getkeluutru_theoKho'])->name('ldb.getkeluutru_theoKho');
    });

    // ====================
    // Danh mục loại văn bản
    // ====================
    Route::module('danhmuc', 'loaivanban', LoaiVanBanController::class);
    Route::prefix('danhmuc/loaivanban')->name('loaivanban.')->group(function () {
        Route::post('getloaivanban', [LoaiVanBanController::class, 'getloaivanban'])->name('ldb.getloaivanban');
    });

    // ====================
    // Danh mục mức độ tin cậy
    // ====================
    Route::module('danhmuc', 'mucdotincay', MucDoTinCayController::class);
    Route::prefix('danhmuc/mucdotincay')->name('mucdotincay.')->group(function () {
        Route::post('getmucdotincay', [MucDoTinCayController::class, 'getmucdotincay'])->name('ldb.getmucdotincay');
    });

    // ====================
    // Danh mục nhóm tài liệu
    // ====================
    Route::module('danhmuc', 'nhomtailieu', NhomTaiLieuController::class);
    Route::prefix('danhmuc/nhomtailieu')->name('nhomtailieu.')->group(function () {
        Route::post('getnhomtailieu', [NhomTaiLieuController::class, 'getnhomtailieu'])->name('ldb.getnhomtailieu');
        Route::get('getMaNhomTaiLieu', [NhomTaiLieuController::class, 'getMaNhomTaiLieu'])->name('ldb.getMaNhomTaiLieu');
    });

    // ====================
    // Danh mục chế độ sử dụng
    // ====================
    Route::module('danhmuc', 'chedosudung', CheDoSuDungController::class);
    Route::prefix('danhmuc/chedosudung')->name('chedosudung.')->group(function () {
        Route::post('getchedosudung', [CheDoSuDungController::class, 'getchedosudung'])->name('ldb.getchedosudung');
    });

    // ====================
    // Danh mục tình trạng vật lý
    // ====================
    Route::module('danhmuc', 'tinhtrangvatly', TinhTrangVatLyController::class);
    Route::prefix('danhmuc/tinhtrangvatly')->name('tinhtrangvatly.')->group(function () {
        Route::post('gettinhtrangvatly', [TinhTrangVatLyController::class, 'gettinhtrangvatly'])->name('ldb.gettinhtrangvatly');
    });

    // ====================
    // Danh mục ngôn ngữ
    // ====================
    Route::module('danhmuc', 'ngonngu', NgonNguController::class);
    Route::prefix('danhmuc/ngonngu')->name('ngonngu.')->group(function () {
        Route::post('getngonngu', [NgonNguController::class, 'getngonngu'])->name('ldb.getngonngu');
    });

    // ====================
    // Danh mục Phông lưu trữ
    // ====================
    Route::module('danhmuc', 'phongluutru', PhongLuuTruController::class);
    Route::prefix('danhmuc/phongluutru')->name('phongluutru.')->group(function () {
        Route::post('getphongluutru', [PhongLuuTruController::class, 'getphongluutru'])->name('ldb.getphongluutru');
    });

    // ====================
    // Danh mục loại luu trữ
    // ====================
    Route::module('danhmuc', 'loailuutru', LoaiLuuTruController::class);
    Route::prefix('danhmuc/loailuutru')->name('loailuutru.')->group(function () {
        Route::post('getloailuutru', [LoaiLuuTruController::class, 'getloailuutru'])->name('ldb.getloailuutru');
    });

    // ====================
    // Danh mục hình thức đào tạo
    // ====================
    Route::module('danhmuc', 'hinhthucdaotao', HinhThucDaoTaoController::class);
    Route::prefix('danhmuc/hinhthucdaotao')->name('hinhthucdaotao.')->group(function () {
        Route::post('gethinhthucdaotao', [HinhThucDaoTaoController::class, 'getHinhThucDaoTao'])->name('ldb.gethinhthucdaotao');
    });

    // ====================
    // Danh mục quốc tịch
    // ====================
    Route::module('danhmuc', 'quoctich', QuocTichController::class);
    Route::prefix('danhmuc/quoctich')->name('quoctich.')->group(function () {
        Route::post('getquoctich', [QuocTichController::class, 'getQuocTich'])->name('ldb.getquoctich');
    });

    // ====================
    // Danh mục tôn giáo
    // ====================
    Route::module('danhmuc', 'tongiao', TonGiaoController::class);
    Route::prefix('danhmuc/tongiao')->name('tongiao.')->group(function () {
        Route::post('gettongiao', [TonGiaoController::class, 'getTonGiao'])->name('ldb.gettongiao');
    });

    // ====================
    // Danh mục dân tộc
    // ====================
    Route::module('danhmuc', 'dantoc', DanTocController::class);
    Route::prefix('danhmuc/dantoc')->name('dantoc.')->group(function () {
        Route::post('getdantoc', [DanTocController::class, 'getdantoc'])->name('ldb.getdantoc');
    });



    // ====================
    // Danh mục trạng thái
    // ====================
    Route::module('danhmuc', 'trangthai', TrangThaiController::class);
    Route::prefix('danhuc/trangthai')->name('trangthai.')->group(function () {
        Route::post('gettrangthai', [TrangThaiController::class, 'gettrangthai'])->name('lbl.gettrangthai');
        Route::post('gettrangthai_khongthuocno', [TrangThaiController::class, 'gettrangthai_khongthuocno'])->name('lbl.gettrangthai_khongthuocno');
    });

    // ====================
    // Danh mục phòng ban
    // ====================
    Route::module('danhmuc', 'phongban', PhongBanController::class);
    Route::prefix('danhuc/phongban')->name('phongban.')->group(function () {
        Route::post('getphongban', [PhongBanController::class, 'getphongban'])->name('lbl.getphongban');
        Route::post('getphongban_khongthuocno', [PhongBanController::class, 'getphongban_khongthuocno'])->name('lbl.getphongban_khongthuocno');
    });

    // ====================
    // Danh mục lý do
    // ====================
    Route::module('danhmuc', 'lydo', LyDoController::class);
    Route::prefix('danhuc/lydo')->name('lydo.')->group(function () {
        Route::post('getlydo', [LyDoController::class, 'getlydo'])->name('lbl.getlydo');
        Route::post('getlydo_khongthuocno', [LyDoController::class, 'getlydo_khongthuocno'])->name('lbl.getlydo_khongthuocno');
    });
    Route::module('danhmuc', 'hoidong', HoiDongController::class);
    Route::prefix('danhmuc/hoidong')
        ->name('hoidong.')
        ->middleware('checkchucnang')
        ->controller(HoiDongController::class)
        ->group(function () {
            Route::get('all-ct', 'getAllCT')
                ->name('getAllCT');
            Route::get('nam-xet', 'getAllNamXet')
                ->name('getAllNamXet');

            Route::post('load-ct', 'loadDuLieuSuaCT')
                ->name('loadCT');

            Route::get('exists', 'kiemTraTonTai')
                ->name('exists');

            Route::post('save-ct', 'luuThongTinCT')
                ->name('saveCT');

            Route::put('save-ct', 'luuThongTinCT')
                ->name('saveCT');


            Route::delete('delete-ct', 'xoaHoiDong_ThanhPhan')
                ->name('deleteCT');

            Route::get('autocomplete-chuc-vu', 'loadDuLieuAutoComplete')
                ->name('autocompleteChucVu');
        });


    // ====================
    // Danh mục loại văn bằng chứng chỉ
    // ====================
    Route::module('danhmuc', 'mauvanbangchungchi', MauVanBangChungChiController::class);
    Route::prefix('danhmuc/mauvanbangchungchi')->name('mauvanbangchungchi.')->group(function () {
        Route::post('getmauvanbangchungchi', [MauVanBangChungChiController::class, 'getmauvanbangchungchi'])->name('lbl.getmauvanbangchungchi');
        Route::post('getfontsize', [MauVanBangChungChiController::class, 'GetFontSize_Combo'])->name('getfontsize');
        Route::post('getfontfamily', [MauVanBangChungChiController::class, 'GetFontFamily_Combo'])->name('getfontfamily');
        Route::get('KiemTraMauCT', [MauVanBangChungChiController::class, 'KiemTraMauCT'])->name('KiemTraMauCT');
        Route::post('InsertMauGCNCT', [MauVanBangChungChiController::class, 'InsertMauGCNCT'])->name('InsertMauGCNCT');
        Route::get('GetAllCT', [MauVanBangChungChiController::class, 'GetAllCT'])->name('GetAllCT');
        Route::delete('xoact', [MauVanBangChungChiController::class, 'xoact'])->name('xoact');
        Route::put('LuuThongTinCT', [MauVanBangChungChiController::class, 'LuuThongTinCT'])->name('LuuThongTinCT');
        Route::post('getFileWord', [MauVanBangChungChiController::class, 'getFileWord'])->name('getFileWord');
        Route::post('getFileWordV2', [MauVanBangChungChiController::class, 'getFileWordV2'])->name('getFileWordV2');
    });
    // ====================
    // Danh mục tiêu chí
    // ====================
    Route::module('danhmuc', 'tieuchi', TieuChiController::class);
    Route::prefix('danhmuc/tieuchi')->name('tieuchi.')->group(function () {
        Route::post('NhanBanDuLieuCT', [TieuChiController::class, 'NhanBanDuLieuCT'])->name('NhanBanDuLieuCT');
    });
    // ====================
    // Danh mục hệ đào tạo
    // ====================
    Route::module('danhmuc', 'hedaotao', HeDaoTaoController::class);
    Route::prefix('danhmuc/hedaotao')->name('hedaotao.')->group(function () {
        Route::post('gethedaotao', [HeDaoTaoController::class, 'gethedaotao'])->name('gethedaotao');
    });
    // ====================
    // Danh mục loại phôi văn bằng chứng chỉ
    // ====================
    Route::module('danhmuc', 'loaiphoivanbangchungchi', LoaiPhoiVanBangChungChiController::class);
    Route::prefix('danhmuc/loaiphoivanbangchungchi')->name('loaiphoivanbangchungchi.')->group(function () {
        Route::post('getloaiphoivanbangchungchi', [LoaiPhoiVanBangChungChiController::class, 'getloaiphoivanbangchungchi'])->name('getloaiphoivanbangchungchi');
    });
    #endregion

    // ====================
    // Danh mục loại văn bằng chứng chỉ
    // ====================
    Route::module('danhmuc', 'loaivanbangchungchi', LoaiVanBangChungChiController::class);
    Route::prefix('danhmuc/loaivanbangchungchi')->name('loaivanbangchungchi.')->group(function () {
        Route::post('getloaivanbangchungchi', [LoaiVanBangChungChiController::class, 'getloaivanbangchungchi'])->name('getloaivanbangchungchi');
        Route::get('getAll', [LoaiVanBangChungChiController::class, 'getAll'])->name('getAllLoaiVBCC');
    });
    #endregion

    #region Số hoá
    // ====================
    // Số hóa hop hồ sơ
    // ====================
    Route::module('sohoa', 'hophoso', HopHoSoController::class);
    Route::prefix('sohoa/hophoso')->name('hophoso.')->group(function () {
        Route::post('gethophoso', [HopHoSoController::class, 'gethophoso'])->name('ldb.gethophoso');
    });

    // ====================
    // Số hóa hồ sơ
    // ====================
    Route::module('sohoa', 'hoso', HoSoController::class);
    Route::prefix('sohoa/hoso')->name('hoso.')->group(function () {
        Route::post('gethoso', [HoSoController::class, 'gethoso'])->name('ldb.gethoso');
        Route::post('getNam', [HoSoController::class, 'getNam'])->name('ldb.getNam');
    });

    // ====================
    // Số hóa tra cứu hồ sơ
    // ====================
    Route::module('sohoa', 'tracuuhoso', TraCuuHoSoController::class);

    // ====================
    // Số hóa tra cứu hồ sơ dạng thư mục
    // ====================
    Route::module('sohoa', 'tracuuhosodangthumuc', TraCuuHoSoDangThuMucController::class);
    #endregion

    #region Quản lý
    Route::prefix('quanly/dungchung')->name('quanly.dungchung.')->group(function () {
        Route::get('get-doituong-by-truonghoc/{donviId_TruongHoc}', [QuanLyController::class, 'getDoiTuongByDonViTruongHoc'])->name('getHSByTruongHoc');
        Route::get('get-truonghoc', [QuanLyController::class, 'getDanhSachTruongHoc'])
            ->name('getDanhSachTruongHoc');

        Route::post('get-doituong-by-truonghoc-demsoluong/{donviId_TruongHoc}', [QuanLyController::class, 'getDoiTuongByDonViTruongHoc_DemSoLuong'])->name('getHSByTruongHocDemSoLuong');
        Route::post('get-truonghoc-demsoluong', [QuanLyController::class, 'getDanhSachTruongHoc_DemSoLuong'])
            ->name('getDanhSachTruongHocDemSoLuong');
        Route::get('get-bangdiemgrid-svg', [QuanLyController::class, 'bangDiemMainGridSvg'])
            ->name('bangDiemMainGridSvg');
        Route::get('get-nhatkythaotac', [QuanLyController::class, 'getNhatKyThaoTac'])
            ->name('nhatKyThaoTac');
        Route::get('get-truongdulieu', [QuanLyController::class, 'getTruongDuLieuList'])
            ->name('getTruongDuLieuList');
        Route::get('get-truongdulieu-theo-model', [QuanLyController::class, 'getTruongDuLieuListTheoModel'])
            ->name('getTruongDuLieuListTheoModel');
        Route::post('scan-ocr', [QuanLyController::class, 'ocrInternal'])
            ->name('ocrInternal');
        Route::post('save‑training', [QuanLyController::class, 'store'])
            ->name('save‑training');
        Route::get(
            '/ocr-training/{file}',
            [QuanLyController::class, 'getOCRFile']
        )
            ->where('file', '^[A-Za-z0-9_-]+\.(?:png|txt)$')
            ->middleware(['auth', 'can:download-ocr-training'])
            ->name('get-ocr-file');
    });


    // ====================
    // Quản lý học sinh
    // ====================

    Route::module('quanly', 'doituong', DoiTuongController::class);
    Route::prefix('quanly/doituong')->name('doituong.')->group(function (): void {
        Route::post('getdoituong', [DoiTuongController::class, 'getdoituong'])->name('hs.getdoituong');
        Route::get('getthongkedoituong', [DoiTuongController::class, 'getthongkedoituong'])->name('hs.getthongkedoituong');
        Route::get('getListTrangThai', [DoiTuongController::class, 'getListTrangThai'])->name('hs.getListTrangThai');
    });

    Route::module('quanly', 'quyetdinhtotnghiep', HoSoTotNghiepTHPTController::class);
    Route::prefix('quanly/quyetdinhtotnghiep')->name('quyetdinhtotnghiep.')->group(function () {
        Route::put('{quyetdinhid}/banhanh', [HoSoTotNghiepTHPTController::class, 'luuthongtinBanHanh'])->name('luuthongtinBanHanh');
        Route::put('{quyetdinhid}/thuhoi', [HoSoTotNghiepTHPTController::class, 'luuthongtinThuHoi'])->name('luuthongtinThuHoi');
        Route::post('{quyetdinhid}/themmoihocsinh', [HoSoTotNghiepTHPTController::class, 'ThemMoiHocSinhTN'])->name('ThemMoiHocSinhTN');
        Route::post('luuhocsinh', [HoSoTotNghiepTHPTController::class, 'luuthongtinHocSinhTN'])->name('luuthongtinHocSinhTN');
        Route::put('luuhocsinh', [HoSoTotNghiepTHPTController::class, 'luuthongtinHocSinhTN'])->name('luuthongtinHocSinhTN');
        Route::get('{quyetdinhid}/listHocSinh', [HoSoTotNghiepTHPTController::class, 'getHocSinhTNWithDoiTuongByQuyetDinhId'])->name('listHocSinhByQuyetDinh');
        Route::get('hocsinhtn/{id}', [HoSoTotNghiepTHPTController::class, 'getHocSinhTNById'])->name('getHocSinhTNById');
        Route::get('countHocSinh', [HoSoTotNghiepTHPTController::class, 'countHocSinhTNByKetQua'])->name('countHocSinhTNByKetQua');
        Route::delete('hocsinhtn', [HoSoTotNghiepTHPTController::class, 'xoaHocSinhTN'])->name('xoaHocSinhTN');
        Route::get('mau-excel', [HoSoTotNghiepTHPTController::class, 'downloadTemplate'])->name('getMauExcel');
        Route::post('check-excel', [HoSoTotNghiepTHPTController::class, 'checkExcel'])->name('checkExcel');
        Route::post('load-excel', [HoSoTotNghiepTHPTController::class, 'loadExcel'])->name('loadExcel');
        Route::post('import-excel', [HoSoTotNghiepTHPTController::class, 'importExcel'])->name('importExcel');
        Route::post('load-sheet-names', [HoSoTotNghiepTHPTController::class, 'loadSheetNames'])->name('loadSheetNames');
    });

    Route::module('quanly', 'bangdiemhocsinh', BangDiemHocSinhController::class);

    // 2) All routes under /quanly/bangdiemhocsinh, named bangdiemhocsinh.*
    Route::prefix('quanly/bangdiemhocsinh')
        ->name('bangdiemhocsinh.')
        ->group(function () {
            // publish a grade-sheet
            Route::put(
                '{bangdiemid}/banhanh',
                [BangDiemHocSinhController::class, 'luuthongtinBanHanh']
            )
                ->name('luuthongtinBanHanh');

            // revoke a grade-sheet
            Route::put(
                '{bangdiemid}/thuhoi',
                [BangDiemHocSinhController::class, 'luuthongtinThuHoi']
            )
                ->name('luuthongtinThuHoi');

            // add a new student to the sheet
            Route::post(
                '{bangdiemid}/themmoihocsinh',
                [BangDiemHocSinhController::class, 'ThemMoiHocSinhTN']
            )
                ->name('ThemMoiHocSinhTN');

            // save student scores (create or update)
            Route::match(
                ['post', 'put'],
                'luuhocsinh',
                [BangDiemHocSinhController::class, 'luuthongtinHocSinhTN']
            )
                ->name('luuthongtinHocSinhTN');
            Route::match(
                ['post', 'put'],
                'luuthongtin-ocr',
                [BangDiemHocSinhController::class, 'luuThongTinOCR']
            )
                ->name('luuThongTinOCR');

            // list students for a given sheet
            Route::get(
                '{bangdiemid}/listHocSinh',
                [BangDiemHocSinhController::class, 'getHocSinhTNWithDoiTuongByQuyetDinhId']
            )
                ->name('listHocSinhByBangDiem');

            Route::get(
                'danh-sach-mon-thi',
                [BangDiemHocSinhController::class, 'getMonThiTrongBangDiem']
            )
                ->name('listMonThiByBangDiem');
            Route::get(
                'danh-sach-mon-thi-trong-kythi',
                [BangDiemHocSinhController::class, 'getMonThiTrongKyThi']
            )
                ->name('listMonThiByKyThi');

            // get one student's record
            Route::get(
                'hocsinhtn/{id}',
                [BangDiemHocSinhController::class, 'getHocSinhTNById']
            )
                ->name('getHocSinhTNById');

            // delete a student's record
            Route::delete(
                'hocsinhtn',
                [BangDiemHocSinhController::class, 'xoaHocSinhTN']
            )
                ->name('xoaHocSinhTN');

            // Excel template download
            Route::get(
                'mau-excel',
                [BangDiemHocSinhController::class, 'downloadTemplate']
            )
                ->name('getMauExcel');



            // Excel validations & import
            Route::post(
                'check-excel',
                [BangDiemHocSinhController::class, 'checkExcel']
            )
                ->name('checkExcel');
            Route::post(
                'load-excel',
                [BangDiemHocSinhController::class, 'loadExcel']
            )
                ->name('loadExcel');
            Route::post(
                'import-excel',
                [BangDiemHocSinhController::class, 'importExcel']
            )
                ->name('importExcel');
            Route::post(
                'load-sheet-names',
                [BangDiemHocSinhController::class, 'loadSheetNames']
            )
                ->name('loadSheetNames');
        });
    // ====================
    // Quản lý Tiếp nhận, nhập kho phôi VBCC
    // ====================
    Route::module('quanly', 'tiepnhanphoivbcc', TiepNhanPhoiVBCCController::class);
    Route::prefix('quanly/tiepnhanphoivbcc')->name('tiepnhanphoivbcc.')->group(function () {
        Route::post('getTiepNhanPhoiVBCC', [TiepNhanPhoiVBCCController::class, 'getTiepNhanPhoiVBCC'])->name('getTiepNhanPhoiVBCC');
        Route::get('getMauVanBangChungChiByLoai', [TiepNhanPhoiVBCCController::class, 'getMauVanBangChungChiByLoai'])->name('getMauVanBangChungChiByLoai');
        Route::get('getAllTiepNhanPhoiVBCC', [TiepNhanPhoiVBCCController::class, 'getAllTiepNhanPhoiVBCC'])->name('getAllTiepNhanPhoiVBCC');
        Route::post('PheDuyet', [TiepNhanPhoiVBCCController::class, 'PheDuyet'])->name('PheDuyet');
        Route::post('getAllThongKe', [TiepNhanPhoiVBCCController::class, 'getAllThongKe'])->name('getAllThongKe');
        Route::get('getAlldonxincapphoibang', [TiepNhanPhoiVBCCController::class, 'getAlldonxincapphoibang'])->name('getAlldonxincapphoibang');
        Route::get('getAllthongkevanbangdasudung', [TiepNhanPhoiVBCCController::class, 'getAllthongkevanbangdasudung'])->name('getAllthongkevanbangdasudung');
        Route::get('LayMaTuTang', [TiepNhanPhoiVBCCController::class, 'LayMaTuTang'])->name('LayMaTuTang');
    });
    // ====================
    // Quản lý cấp phát, xuất kho phôi VBCC
    // ====================
    Route::module('quanly', 'capphatphoivbcc', CapPhatPhoiVBCCController::class);
    Route::module('quanly', 'capphatphoivbcc', CapPhatPhoiVBCCController::class); //getDanhSachTiepNhanCoCapPhat
    Route::prefix('quanly/capphatphoivbcc')->name('capphatphoivbcc.')->group(function () {
        Route::get('getDanhSachTiepNhanCoCapPhat', [CapPhatPhoiVBCCController::class, 'getDanhSachTiepNhanCoCapPhat'])->name('getDanhSachTiepNhanCoCapPhat');
    });
    // ====================
    // Quản lý hủy phôi VBCC
    // ====================
    Route::module('quanly', 'huyphoivbcc', HuyPhoiVBCCController::class);

    Route::module('quanly', 'huyphoivbcc', HuyPhoiVBCCController::class); //getDanhSachTiepNhanCoCapPhat
    Route::prefix('quanly/huyphoivbcc')->name('huyphoivbcc.')->group(function () {
        Route::post('PheDuyet', [HuyPhoiVBCCController::class, 'PheDuyet'])->name('PheDuyet');
        Route::get('getListLyDo', [HuyPhoiVBCCController::class, 'getListLyDo'])->name('getListLyDo');
        Route::get('getThongKeHuyPhoiTheoTenLyDo', [HuyPhoiVBCCController::class, 'getThongKeHuyPhoiTheoTenLyDo'])->name('getThongKeHuyPhoiTheoTenLyDo');
    });
    // ====================
    // Quản lý sổ gốc
    // ====================
    Route::module('quanly', 'sogoc', SoGocController::class);
    Route::prefix('quanly/sogoc')->name('sogoc.')->group(function () {
        Route::post('luuthongtinct', [SoGocController::class, 'luuthongtinct'])->name('luuthongtinct');
        Route::put('luuthongtinct', [SoGocController::class, 'luuthongtinct'])->name('luuthongtinct');
        Route::Get('getallct', [SoGocController::class, 'getallct'])->name('getallct');
        Route::delete('xoact', [SoGocController::class, 'xoact'])->name('xoact');
        Route::get('loaddulieusuact', [SoGocController::class, 'loaddulieusuact'])->name('loaddulieusuact');
        Route::get('LayThongTinThongKeXepLoai', [SoGocController::class, 'LayThongTinThongKeXepLoai'])->name('LayThongTinThongKeXepLoai');
        Route::get('LayMaTuTang', [SoGocController::class, 'LayMaTuTang'])->name('LayMaTuTang');
        Route::post('Update_SoVaoSoGoc', [SoGocController::class, 'Update_SoVaoSoGoc'])->name('Update_SoVaoSoGoc');

        Route::POST('Update_GiaoThuHoi', [SoGocController::class, 'Update_GiaoThuHoi'])->name('Update_GiaoThuHoi');
        Route::GET('XuatExcelSoGoc', [SoGocController::class, 'XuatExcelSoGoc'])->name('XuatExcelSoGoc');

        Route::GET('GetListDonViCoHocSinh', [SoGocController::class, 'GetListDonViCoHocSinh'])->name('GetListDonViCoHocSinh');
        Route::GET('GetListHocSinhByDonVi', [SoGocController::class, 'GetListHocSinhByDonVi'])->name('GetListHocSinhByDonVi');

        Route::post('check-excel', [SoGocController::class, 'checkExcel'])->name('checkExcel');
        Route::post('load-excel', [SoGocController::class, 'loadExcel'])->name('loadExcel');
        Route::post('importExcel', [SoGocController::class, 'importExcel'])->name('importExcel');
        Route::get('TaiFileMau', [SoGocController::class, 'downloadTemplate'])->name('TaiFileMau');
        Route::post('load-sheet-names', [SoGocController::class, 'loadSheetNames'])->name('loadSheetNames');
    });
    // ====================
    // Quản lý sổ gốc bản sao
    // ====================
    Route::module('quanly', 'sogocbansao', SoGocBanSaoController::class);
    Route::prefix('quanly/sogocbansao')->name('sogocbansao.')->group(function () {
        Route::post('luuthongtinct', [SoGocBanSaoController::class, 'luuthongtinct'])->name('luuthongtinct');
        Route::put('luuthongtinct', [SoGocBanSaoController::class, 'luuthongtinct'])->name('luuthongtinct');
        Route::Get('getallct', [SoGocBanSaoController::class, 'getallct'])->name('getallct');
        Route::delete('xoact', [SoGocBanSaoController::class, 'xoact'])->name('xoact');
        Route::get('loaddulieusuact', [SoGocBanSaoController::class, 'loaddulieusuact'])->name('loaddulieusuact');
        Route::get('LayThongTinThongKeXepLoai', [SoGocBanSaoController::class, 'LayThongTinThongKeXepLoai'])->name('LayThongTinThongKeXepLoai');
        Route::get('LayMaTuTang', [SoGocBanSaoController::class, 'LayMaTuTang'])->name('LayMaTuTang');
        Route::post('Update_SoVaoSoGocBanSao', [SoGocBanSaoController::class, 'Update_SoVaoSoGocBanSao'])->name('Update_SoVaoSoGocBanSao');

        Route::POST('Update_GiaoThuHoi', [SoGocBanSaoController::class, 'Update_GiaoThuHoi'])->name('Update_GiaoThuHoi');
        Route::GET('XuatExcelSoGoc', [SoGocBanSaoController::class, 'XuatExcelSoGoc'])->name('XuatExcelSoGoc');
        Route::GET('GetListDonViCoHocSinh', [SoGocBanSaoController::class, 'GetListDonViCoHocSinh'])->name('GetListDonViCoHocSinh');
        Route::GET('GetListHocSinhByDonVi', [SoGocBanSaoController::class, 'GetListHocSinhByDonVi'])->name('GetListHocSinhByDonVi');
    });

    Route::module('quanly', 'capbangtotnghiep', CapBangTotNghiepController::class);
    Route::prefix('quanly/capbangtotnghiep')->name('capbangtotnghiep.')->group(function () {
        Route::post('GetListDonViCoHocSinh', [CapBangTotNghiepController::class, 'GetListDonViCoHocSinh'])->name('GetListDonViCoHocSinh');
        Route::post('GetListHocSinhByDonVi', [CapBangTotNghiepController::class, 'GetListHocSinhByDonVi'])->name('GetListHocSinhByDonVi');
        Route::post('luuthongtinct', [CapBangTotNghiepController::class, 'luuthongtinct'])->name('luuthongtinct');
        Route::put('luuthongtinct', [CapBangTotNghiepController::class, 'luuthongtinct'])->name('luuthongtinct');
        Route::Get('getallct', [CapBangTotNghiepController::class, 'getallct'])->name('getallct');
        Route::delete('xoact', [CapBangTotNghiepController::class, 'xoact'])->name('xoact');
        Route::get('getMauVanBang', [CapBangTotNghiepController::class, 'getMauVanBang'])->name('getMauVanBang');
        Route::post('getFileWordV2', [CapBangTotNghiepController::class, 'getFileWordV2'])->name('getFileWordV2');
        Route::get('LayThongTinThongKeXepLoai', [CapBangTotNghiepController::class, 'LayThongTinThongKeXepLoai'])->name('LayThongTinThongKeXepLoai');
        Route::get('LayMaTuTang', [CapBangTotNghiepController::class, 'LayMaTuTang'])->name('LayMaTuTang');
        Route::get('KiemTraDaTa', [CapBangTotNghiepController::class, 'KiemTraDaTa'])->name('KiemTraDaTa');

        Route::post('check-excel', [CapBangTotNghiepController::class, 'checkExcel'])->name('checkExcel');
        Route::post('load-excel', [CapBangTotNghiepController::class, 'loadExcel'])->name('loadExcel');
        Route::post('importExcel', [CapBangTotNghiepController::class, 'importExcel'])->name('importExcel');
        Route::get('TaiFileMau', [CapBangTotNghiepController::class, 'downloadTemplate'])->name('TaiFileMau');
        Route::post('load-sheet-names', [CapBangTotNghiepController::class, 'loadSheetNames'])->name('loadSheetNames');
    });
    Route::module('quanly', 'huyphoivbcc', HuyPhoiVBCCController::class);
    Route::prefix('quanly/huyphoivbcc')->name('huyphoivbcc.')->group(function () {
        // Add specific routes for huyphoivbcc here if needed
    });


    // ====================
    // Tổng quan
    // ====================
    Route::module('quanly', 'tongquan', TongQuanController::class);
    Route::prefix('quanly/tongquan')->name('tongquan.')->group(function () {
        Route::Get('BieuDoChart21_CapBanSao', [TongQuanController::class, 'BieuDoChart21_CapBanSao'])->name('BieuDoChart21_CapBanSao');
        Route::Get('BieuDoChart3_HocSinhTotNghiep', [TongQuanController::class, 'BieuDoChart3_HocSinhTotNghiep'])->name('BieuDoChart3_HocSinhTotNghiep');
        Route::Get('BieuDoChart1_ChinhSuaVanBang', [TongQuanController::class, 'BieuDoChart1_ChinhSuaVanBang'])->name('BieuDoChart1_ChinhSuaVanBang');
        Route::Get('BieuDoChart41_ThuHoiVanBang', [TongQuanController::class, 'BieuDoChart41_ThuHoiVanBang'])->name('BieuDoChart41_ThuHoiVanBang');
        Route::Get('ThongKeVBCC', [TongQuanController::class, 'ThongKeVBCC'])->name('ThongKeVBCC');
        Route::Get('GetDonVi', [TongQuanController::class, 'GetDonVi'])->name('GetDonVi');
        Route::Get('DanhSach_VanBangChungChiCapMoi', [TongQuanController::class, 'DanhSach_VanBangChungChiCapMoi'])->name('DanhSach_VanBangChungChiCapMoi');
        Route::Get('TongSoPhoiDaSuDung', [TongQuanController::class, 'TongSoPhoiDaSuDung'])->name('TongSoPhoiDaSuDung');
        Route::Get('TongSoPhoiHuyBo', [TongQuanController::class, 'TongSoPhoiHuyBo'])->name('TongSoPhoiHuyBo');
        Route::Get('TongSoPhoi', [TongQuanController::class, 'TongSoPhoi'])->name('TongSoPhoi');


    });

    #endregion

    #region Biên tập công thông tin
    // ====================
    // Biên tập loại tin tức
    // ====================
    Route::module('bientapcongthongtin', 'loaitintuc', BienTapLoaiTinTucController::class);
    Route::prefix('bientapcongthongtin/loaitintuc')->name('bientapcongthongtin.loaitintuc.')->group(function () {
        Route::get('getloaitintuc_khongthuocno', [BienTapLoaiTinTucController::class, 'getloaitintuc_khongthuocno'])->name('ltt.getloaitintuc_khongthuocno');
    });

    // ====================
    // Biên tập tin tức
    // ====================
    Route::module('bientapcongthongtin', 'tintuc', BienTapTinTucController::class);
    Route::prefix('bientapcongthongtin/tintuc')->name('bientapcongthongtin.tintuc.')->group(function () {
        Route::delete('bulkDelete', [BienTapTinTucController::class, 'bulkDelete'])->name('bulkDelete');
        Route::post('{id}/toggleStatus', [BienTapTinTucController::class, 'toggleStatus'])->name('toggleStatus');
        Route::get('getComboData', [BienTapTinTucController::class, 'getComboData'])->name('getComboData');
        Route::post('deleteOldImage', [BienTapTinTucController::class, 'deleteOldImage'])->name('deleteOldImage');
        Route::post('getloaitintuc', [BienTapTinTucController::class, 'getloaitintuc'])->name('getloaitintuc');
    });

    // ====================
    // Biên tập trang
    // ====================
    Route::module('bientapcongthongtin', 'trang', BienTapTrangController::class);
    Route::prefix('bientapcongthongtin/trang')->name('bientapcongthongtin.trang.')->group(function () {
        Route::post('{id}/toggleStatus', [BienTapTrangController::class, 'toggleStatus'])->name('toggleStatus');
        Route::get('generateAutoCode', [BienTapTrangController::class, 'generateAutoCode'])->name('generateAutoCode');
    });

    // ====================
    // Thiết lập website
    // ====================
    Route::prefix('bientapcongthongtin/thietlapwebsite')->name('bientapcongthongtin.thietlapwebsite.')->group(function () {
        Route::get('/', [BienTapThietLapWebsiteController::class, 'index'])->name('index');
        Route::get('/getall', [BienTapThietLapWebsiteController::class, 'getAll'])->name('getall');
        Route::get('/loaddulieusua', [BienTapThietLapWebsiteController::class, 'loadDuLieuSua'])->name('loaddulieusua');
        Route::put('/capnhat', [BienTapThietLapWebsiteController::class, 'capNhat'])->name('capnhat');
    });

    // ====================
    // Phê duyệt bình luận
    // ====================
    Route::module('bientapcongthongtin', 'pheduyetbinhluan', PheDuyetBinhLuanController::class);
    Route::prefix('bientapcongthongtin/pheduyetbinhluan')->name('bientapcongthongtin.pheduyetbinhluan.')->group(function () {
        Route::get('/', [PheDuyetBinhLuanController::class, 'index'])->name('index');
        Route::get('/getall', [PheDuyetBinhLuanController::class, 'getAll'])->name('getall');
        Route::get('/loaddulieusua', [PheDuyetBinhLuanController::class, 'loadDuLieuSua'])->name('loaddulieusua');
        Route::put('/capnhat', [PheDuyetBinhLuanController::class, 'capNhat'])->name('capnhat');
        Route::delete('/xoaBinhLuan', [PheDuyetBinhLuanController::class, 'xoaBinhLuan'])->name('xoaBinhLuan');
        Route::get('/getComboData', [PheDuyetBinhLuanController::class, 'getComboData'])->name('getComboData');
    });

    #endregion

    #region Báo cáo
    // ====================
    // Dùng chung báo cáo
    // ====================
    Route::prefix('baocao/dungchung')->group(function () {
        Route::get('GetTuyChonIn', [DungChungBaoCao::class, 'GetTuyChonIn'])->name('dungchungbaocao.GetTuyChonIn');
        Route::get('getUserGroupCode', [DungChungBaoCao::class, 'getUserGroupCode'])->name('dungchungbaocao.getUserGroupCode');
        Route::post('LuuThamSoBaoCao', [DungChungBaoCao::class, 'LuuThamSoBaoCao'])->name('dungchungbaocao.LuuThamSoBaoCao');
        Route::post('XuatBaoCao', [DungChungBaoCao::class, 'XuatBaoCao'])->name('dungchungbaocao.XuatBaoCao');
        Route::post('taoMauMacDinhUser', [DungChungBaoCao::class, 'taoMauMacDinhUser'])->name('dungchungbaocao.taoMauMacDinhUser');
        Route::get('getThietLapTheoUser', [DungChungBaoCao::class, 'getThietLapTheoUser'])->name('dungchungbaocao.getThietLapTheoUser');
        Route::get('getAllMauBaoCaoTheoUser', [DungChungBaoCao::class, 'getAllMauBaoCaoTheoUser'])->name('dungchungbaocao.getAllMauBaoCaoTheoUser');
    });
    // ====================
    // Danh sách báo cáo
    // ====================
    Route::module('baocao', 'danhsachbaocao', DanhSachBaoCaoController::class);
    Route::prefix('baocao/danhsachbaocao')->name('danhsachbaocao.')->group(function () {
        Route::get('GetBaoCaoDaLuu', [DanhSachBaoCaoController::class, 'getAllBaoCaoDaLuu'])->name('GetBaoCaoDaLuu');
        Route::put('LuuThongTin_BaoCaoDaLuu', [DanhSachBaoCaoController::class, 'LuuThongTin_BaoCaoDaLuu'])->name('LuuThongTin_BaoCaoDaLuu');
        Route::post('LuuThongTin_BaoCaoDaLuu', [DanhSachBaoCaoController::class, 'LuuThongTin_BaoCaoDaLuu'])->name('LuuThongTin_BaoCaoDaLuu');
    });
    // ====================
    // Thiết lập mẫu báo cáo
    // ====================
    Route::module('baocao', 'thietlapmaubaocao', ThietLapMauBaoCaoController::class);
    Route::prefix('baocao/thietlapmaubaocao')->name('thietlapmaubaocao.')->group(function () {
        Route::post('getMauBaoCaoThietLapCha', [ThietLapMauBaoCaoController::class, 'getMauBaoCaoThietLapCha'])->name('getMauBaoCaoThietLapCha');
        Route::get('getThietLapBaoCaoTheoMau', [ThietLapMauBaoCaoController::class, 'getThietLapBaoCaoTheoMau'])->name('getThietLapBaoCaoTheoMau');
        Route::post('CapNhatViTriDuLieuThietLap', [ThietLapMauBaoCaoController::class, 'updateViTriThietLap'])->name('CapNhatViTriDuLieuThietLap');
        Route::get('GetSoThuTuLonNhat', [ThietLapMauBaoCaoController::class, 'getNextSTTAndTenCot'])->name('GetSoThuTuLonNhat');
        Route::POST('luuthongtinthietlap', [ThietLapMauBaoCaoController::class, 'luuthongtinthietlap'])->name('luuthongtinthietlap');
        Route::PUT('luuthongtinthietlap', [ThietLapMauBaoCaoController::class, 'luuthongtinthietlap'])->name('luuthongtinthietlap');
        Route::get('getThietLapTheoID', [ThietLapMauBaoCaoController::class, 'getThietLapTheoID'])->name('getThietLapTheoID');
        Route::delete('xoathietlap', [ThietLapMauBaoCaoController::class, 'xoathietlap'])->name('xoathietlap');
        Route::PUT('luuthongtintheouser', [ThietLapMauBaoCaoController::class, 'luuthongtintheouser'])->name('luuthongtintheouser');
        Route::post('luuthongtintheouser', [ThietLapMauBaoCaoController::class, 'luuthongtintheouser'])->name('luuthongtintheouser');
    });
    ////BÁO CÁO MẪU
    Route::prefix('baocao/baocaomau')->name('baocaomau.')->group(function () {
        Route::get('/', [BaoCaoMauController::class, 'index'])->name('index');
        Route::get('ViewBaoCao', [BaoCaoMauController::class, 'ViewBaoCao'])->name('ViewBaoCao');
    });
    // ====================
    // Báo cáo thống kê tình trạng sử dụng phôi VBCC
    // ====================
    Route::prefix('baocao/thongkesdphoi')->name('thongkesdphoi.')->group(function () {
        Route::get('/', [ThongKeSDPhoiController::class, 'index'])->name('index');
        Route::get('ViewBaoCao', [ThongKeSDPhoiController::class, 'ViewBaoCao'])->name('ViewBaoCao');
        Route::post('getAllPhoiVBCC', [ThongKeSDPhoiController::class, 'getLoaiPhoiVanBangChungChi'])->name('getAllPhoiVBCC');
        Route::get('getDonViSDPhoi', [ThongKeSDPhoiController::class, 'getDonViSDPhoi'])->name('getDonViSDPhoi');
        Route::get('getTinhTrangPhoi', [ThongKeSDPhoiController::class, 'getTinhTrangPhoi'])->name('getTinhTrangPhoi');
        Route::get('getCapHoc', [ThongKeSDPhoiController::class, 'getCapHoc'])->name('getCapHoc');
    });
    // ====================
    // Báo cáo thống kê học sinh tốt nghiệp
    // ====================
    Route::prefix('baocao/thongkehstn')->name('thongkehstn.')->group(function () {
        Route::get('/', [ThongKeHSTNController::class, 'index'])->name('index');
        Route::get('ViewBaoCao', [ThongKeHSTNController::class, 'ViewBaoCao'])->name('ViewBaoCao');
    });

    // ====================
    // Báo cáo thống kê số lượng cấp bản sao
    // ====================
    Route::prefix('baocao/thongkeslcapbansao')->name('thongkeslcapbansao.')->group(function () {
        Route::get('/', [ThongKeSLCapBanSaoController::class, 'index'])->name('index');
        Route::get('ViewBaoCao', [ThongKeSLCapBanSaoController::class, 'ViewBaoCao'])->name('ViewBaoCao');
    });
    // ====================
    // Báo cáo cấp phát văn bằng chứng chỉ chi tiết
    // ====================
    Route::prefix('baocao/baocaocapphatvb')->name('baocaocapphatvb.')->group(function () {
        Route::get('/', [BaoCaoCapPhatVBController::class, 'index'])->name('index');
        Route::get('ViewBaoCao', [BaoCaoCapPhatVBController::class, 'ViewBaoCao'])->name('ViewBaoCao');
    });
    #endregion

    // ====================
    // Tra cúu thông tin VBCC
    // ====================
    Route::module('tracuu', 'tracuuvbcc', TraCuuVBCCController::class);
    Route::prefix('tracuu/tracuuvbcc')->name('tracuuvbcc.')->group(function () {
        Route::post('getdiabanhc_tinh', [DiaBanHanhChinhController::class, 'getdiabanhc_tinh'])->name('db.getdiabanhc_tinh');
        Route::post('getdiabanhc_byidcha', [DiaBanHanhChinhController::class, 'getdiabanhc_byidcha'])->name('db.getdiabanhc_byidcha');
    });

    // ====================
    // Tra cúu quyet dinh chinh sua VBCC
    // ====================
    Route::module('tracuu', 'tracuuquyetdinhcsndvbcc', TraCuuQuyetDinhCSNDVBCCController::class);
    Route::prefix('tracuu/tracuuquyetdinhcsndvbcc')->name('tracuuquyetdinhcsndvbcc.')->group(function () {
        Route::post('getCapHoc', [CapHocController::class, 'getCapHoc'])->name('getCapHoc');
        Route::post('getLoaiPhoiVanBangChungChi', [LoaiPhoiVanBangChungChiController::class, 'getLoaiPhoiVanBangChungChi'])->name('getLoaiPhoiVanBangChungChi');
        Route::post('gettrangthai', [QuyetDinhChinhSuaVanBangController::class, 'gettrangthai'])->name('gettrangthai');
        Route::get('getdoituong_ThuocTruongHoc', [TraCuuQuyetDinhCSNDVBCCController::class, 'getdoituong_ThuocTruongHoc'])->name('getdoituong_ThuocTruongHoc');
        Route::post('getKhoaThi', [TraCuuQuyetDinhCSNDVBCCController::class, 'getKhoaThi'])->name('getKhoaThi');
        Route::put('BanHanhQD', [QuyetDinhChinhSuaVanBangController::class, 'BanHanhQD'])->name('BanHanhQD');
        Route::put('ThuHoiQD', [QuyetDinhChinhSuaVanBangController::class, 'ThuHoiQD'])->name('ThuHoiQD');
        Route::post('getchucvu', [ChucVuController::class, 'getchucvu'])->name('getchucvu');
        Route::get('getYeuCauCSNDVBCC', [QuyetDinhChinhSuaVanBangController::class, 'getYeuCauCSNDVBCC'])->name('getYeuCauCSNDVBCC');
        Route::get('getDonYeuCauByID', [QuyetDinhChinhSuaVanBangController::class, 'getDonYeuCauByID'])->name('getDonYeuCauByID');
        Route::post('getDanhSachTruongHoc_DemSoLuong', [QuyetDinhChinhSuaVanBangController::class, 'getDanhSachTruongHoc_DemSoLuong'])->name('getDanhSachTruongHoc_DemSoLuong');
    });

    #region Đơn xin cấp phôi bằng
    // ====================
    // Quản lý đơn xin cấp phôi bằng
    // ====================
    Route::module('quanly', 'donxincapphoibang', DonXinCapPhoiBangController::class);
    Route::prefix('quanly/donxincapphoibang')->name('donxincapphoibang.')->group(function () {
        Route::post('luuThongTin', action: [DonXinCapPhoiBangController::class, 'luuThongTin'])->name('luuThongTin');
        Route::put('luuThongTin', action: [DonXinCapPhoiBangController::class, 'luuThongTin'])->name('luuThongTin');
        Route::get('getListTrangThaiCapBang', action: [DonXinCapPhoiBangController::class, 'getListTrangThaiCapBang'])->name('getListTrangThaiCapBang');
        Route::get('getAll', [DonXinCapPhoiBangController::class, 'getAll'])->name('getAll');
        Route::get('loadDuLieuSua', [DonXinCapPhoiBangController::class, 'loadDuLieuSua'])->name('loadDuLieuSua');
        Route::get('getThongKe', [DonXinCapPhoiBangController::class, 'getThongKe'])->name('getThongKe');
        Route::post('GuiDon', [DonXinCapPhoiBangController::class, 'GuiDon'])->name('GuiDon');
        Route::delete('xoa', [DonXinCapPhoiBangController::class, 'xoa'])->name('xoa');
        Route::get('getCurrentUserInfo', [DonXinCapPhoiBangController::class, 'getCurrentUserInfo'])->name('getCurrentUserInfo');
    });

    #endregion

    #region duyệt đơn xin cấp phôi bằng
    // ====================
    // Quản lý duyệt đơn xin cấp phôi bằng
    // ====================
    Route::module('quanly', 'duyetdonxincapphoibang', DuyetDonXinCapPhoiBangController::class);
    Route::prefix('quanly/duyetdonxincapphoibang')->name('duyetdonxincapphoibang.')->group(function () {
        Route::get('getAll', [DuyetDonXinCapPhoiBangController::class, 'getAll'])->name('getAll');
        Route::get('loadDuLieuSua', [DuyetDonXinCapPhoiBangController::class, 'loadDuLieuSua'])->name('loadDuLieuSua');
        Route::post('GuiDon', [DuyetDonXinCapPhoiBangController::class, 'GuiDon'])->name('GuiDon');
        Route::get('getThongKe', [DuyetDonXinCapPhoiBangController::class, 'getThongKe'])->name('getThongKe');
        Route::get('getListTrangThaiCapBang', action: [DuyetDonXinCapPhoiBangController::class, 'getListTrangThaiCapBang'])->name('getListTrangThaiCapBang');
        Route::get('getAlldonxincapphoibang', [TiepNhanPhoiVBCCController::class, 'getAlldonxincapphoibang'])->name('getAlldonxincapphoibang');
        Route::post('CapPhatPhoiBang', action: [DuyetDonXinCapPhoiBangController::class, 'CapPhatPhoiBang'])->name('CapPhatPhoiBang');
        Route::get('getCurrentUserInfo', [DonXinCapPhoiBangController::class, 'getCurrentUserInfo'])->name('getCurrentUserInfo');
        Route::get('LayMaTuTang', [TiepNhanPhoiVBCCController::class, 'LayMaTuTang'])->name('LayMaTuTang');
    });

    #endregion

    #region Yêu cầu CSNDVBCC
    // ====================
    // Quản lý đơn xin cấp phôi bằng
    // ====================
    Route::module('quanly', 'yeucaucsndvbcc', YeuCauCSNDVBCCController::class);
    Route::prefix('quanly/yeucaucsndvbcc')->name('yeucaucsndvbcc.')->group(function () {
        Route::post('luuThongTin', action: [YeuCauCSNDVBCCController::class, 'luuThongTin'])->name('luuThongTin');
        Route::put('luuThongTin', action: [YeuCauCSNDVBCCController::class, 'luuThongTin'])->name('luuThongTin');
        Route::get('getListTrangThaiCapBang', action: [YeuCauCSNDVBCCController::class, 'getListTrangThaiCapBang'])->name('getListTrangThaiCapBang');
        Route::get('getAll', [YeuCauCSNDVBCCController::class, 'getAll'])->name('getAll');
        Route::get('loadDuLieuSua', [YeuCauCSNDVBCCController::class, 'loadDuLieuSua'])->name('loadDuLieuSua');
        Route::get('getThongKe', [YeuCauCSNDVBCCController::class, 'getThongKe'])->name('getThongKe');
        Route::post('GuiDon', [YeuCauCSNDVBCCController::class, 'GuiDon'])->name('GuiDon');
        Route::delete('xoa', [YeuCauCSNDVBCCController::class, 'xoa'])->name('xoa');
    });

    #endregion

    #region Duyệt yêu cầu CSNDVBCC
    // ====================
    // Quản lý Duyệt yêu cầu CSNDVBCC
    // ====================
    Route::module('quanly', 'duyetyeucaucsndvbcc', DuyetYeuCauCSNDVBCCController::class);
    Route::prefix('quanly/duyetyeucaucsndvbcc')->name('duyetyeucaucsndvbcc.')->group(function () {
        Route::get('getListTrangThaiCapBang', action: [DuyetYeuCauCSNDVBCCController::class, 'getListTrangThaiCapBang'])->name('getListTrangThaiCapBang');
        Route::get('getAll', [DuyetYeuCauCSNDVBCCController::class, 'getAll'])->name('getAll');
        Route::get('getThongKe', [DuyetYeuCauCSNDVBCCController::class, 'getThongKe'])->name('getThongKe');
        Route::get('loadDuLieuSua', [DuyetYeuCauCSNDVBCCController::class, 'loadDuLieuSua'])->name('loadDuLieuSua');
        Route::post('GuiDon', [DuyetYeuCauCSNDVBCCController::class, 'GuiDon'])->name('GuiDon');
    });
    #endregion
#region Yêu cầu CSNDVBCC
    // ====================
    // Quản lý đơn xin cấp phôi bằng
    // ====================
    Route::module('quanly', 'yeucaucsndvbcc', YeuCauCSNDVBCCController::class);
    Route::prefix('quanly/yeucaucsndvbcc')->name('yeucaucsndvbcc.')->group(function () {
        Route::post('luuThongTin', action: [YeuCauCSNDVBCCController::class, 'luuThongTin'])->name('luuThongTin');
        Route::put('luuThongTin', action: [YeuCauCSNDVBCCController::class, 'luuThongTin'])->name('luuThongTin');
        Route::get('getListTrangThaiCapBang', action: [YeuCauCSNDVBCCController::class, 'getListTrangThaiCapBang'])->name('getListTrangThaiCapBang');
        Route::get('getAll', [YeuCauCSNDVBCCController::class, 'getAll'])->name('getAll');
        Route::get('loadDuLieuSua', [YeuCauCSNDVBCCController::class, 'loadDuLieuSua'])->name('loadDuLieuSua');
        Route::get('getThongKe', [YeuCauCSNDVBCCController::class, 'getThongKe'])->name('getThongKe');
        Route::post('GuiDon', [YeuCauCSNDVBCCController::class, 'GuiDon'])->name('GuiDon');
        Route::delete('xoa', [YeuCauCSNDVBCCController::class, 'xoa'])->name('xoa');
    });

    #endregion

    #region Đơn xin cấp bản sao
    // ====================
    // Quản lý đơn xin cấp bản sao
    // ====================
    Route::module('quanly', 'donxincapbansao', DonXinCapBanSaoController::class);
    Route::prefix('quanly/donxincapbansao')->name('donxincapbansao.')->group(function () {
        Route::post('PheDuyet', action: [DonXinCapBanSaoController::class, 'PheDuyet'])->name('PheDuyet');
        Route::get('getAllthongkevanbangdasudung', action: [DonXinCapBanSaoController::class, 'getAllthongkevanbangdasudung'])->name('getAllthongkevanbangdasudung');
        Route::get('getAllLoaiVanBangCC', action: [DonXinCapBanSaoController::class, 'getAllLoaiVanBangCC'])->name('getAllLoaiVanBangCC');
    });
    #endregion

    #endregion
    #region Duyệt Đơn xin cấp bản sao
    // ====================
    // Quản lý Duyệt đơn xin cấp bản sao
    // ====================
    Route::module('quanly', 'duyetdonxincapbansao', DuyetDonXinCapBanSaoController::class);
    Route::prefix('quanly/duyetdonxincapbansao')->name('duyetdonxincapbansao.')->group(function () {
        Route::post('PheDuyet', action: [DuyetDonXinCapBanSaoController::class, 'PheDuyet'])->name('PheDuyet');
        Route::get('getAllthongkevanbangdasudung', action: [DuyetDonXinCapBanSaoController::class, 'getAllthongkevanbangdasudung'])->name('getAllthongkevanbangdasudung');
        Route::get('getAllLoaiVanBangCC', action: [DuyetDonXinCapBanSaoController::class, 'getAllLoaiVanBangCC'])->name('getAllLoaiVanBangCC');
    });

    #endregion
    // ====================
    // Tra cúu thông tin học sinh
    // ====================
    Route::module('tracuu', 'tracuuhocsinh', TraCuuHocSinhController::class);
    Route::prefix('tracuu/tracuuhocsinh')->name('tracuuhocsinh.')->group(function () {
        Route::post('getdiabanhc_tinh', [DiaBanHanhChinhController::class, 'getdiabanhc_tinh'])->name('db.getdiabanhc_tinh');
        Route::post('getdiabanhc_byidcha', [DiaBanHanhChinhController::class, 'getdiabanhc_byidcha'])->name('db.getdiabanhc_byidcha');
    });

    #endregion
    // ====================
    // Quyet dinh chinh sua van bang
    // ====================
    Route::module('quanly', 'quyetdinhchinhsuavanbang', QuyetDinhChinhSuaVanBangController::class);
    Route::prefix('quanly/quyetdinhchinhsuavanbang')->name('quyetdinhchinhsuavanbang.')->group(function () {
        Route::post('getCapHoc', [CapHocController::class, 'getCapHoc'])->name('getCapHoc');
        Route::post('getLoaiPhoiVanBangChungChi', [LoaiPhoiVanBangChungChiController::class, 'getLoaiPhoiVanBangChungChi'])->name('getLoaiPhoiVanBangChungChi');
        Route::post('gettrangthai', [QuyetDinhChinhSuaVanBangController::class, 'gettrangthai'])->name('gettrangthai');
        Route::put('BanHanhQD', [QuyetDinhChinhSuaVanBangController::class, 'BanHanhQD'])->name('BanHanhQD');
        Route::put('ThuHoiQD', [QuyetDinhChinhSuaVanBangController::class, 'ThuHoiQD'])->name('ThuHoiQD');
        Route::post('getchucvu', [ChucVuController::class, 'getchucvu'])->name('getchucvu');
        Route::get('getYeuCauCSNDVBCC', [QuyetDinhChinhSuaVanBangController::class, 'getYeuCauCSNDVBCC'])->name('getYeuCauCSNDVBCC');
        Route::get('getDonYeuCauByID', [QuyetDinhChinhSuaVanBangController::class, 'getDonYeuCauByID'])->name('getDonYeuCauByID');
        Route::post('getDanhSachTruongHoc_DemSoLuong', [QuyetDinhChinhSuaVanBangController::class, 'getDanhSachTruongHoc_DemSoLuong'])->name('getDanhSachTruongHoc_DemSoLuong');
    });

    Route::module('quanly', 'phieuyeucauhuyvbcc', PhieuYeuCauHuyVBCCController::class);
    Route::prefix('quanly/phieuyeucauhuyvbcc')->name('phieuyeucauhuyvbcc.')->group(function () {
        Route::post('PheDuyet', action: [PhieuYeuCauHuyVBCCController::class, 'PheDuyet'])->name('PheDuyet');
        Route::get('getAllthongkevanbangdasudung', action: [PhieuYeuCauHuyVBCCController::class, 'getAllthongkevanbangdasudung'])->name('getAllthongkevanbangdasudung');
    });
    #endregion
    #region phê duyệt phiếu yêu cầu hủy/thu hồi phôi bằng
    // ====================
    // Quản lý phê duyệt phiếu yêu cầu hủy/thu hồi phôi bằng
    // ====================
    Route::module('quanly', 'pheduyetphieuyeucauhuyvbcc', PheDuyetPhieuYeuCauHuyVBCCController::class);
    Route::prefix('quanly/pheduyetphieuyeucauhuyvbcc')->name('pheduyetphieuyeucauhuyvbcc.')->group(function () {
        Route::post('PheDuyet', action: [PheDuyetPhieuYeuCauHuyVBCCController::class, 'PheDuyet'])->name('PheDuyet');
    });

    // ====================
    // Quyet dinh huy van bang
    // ====================
    Route::module('quanly', 'quyetdinhhuyvanbang', QuyetDinhHuyVanBangController::class);
    Route::prefix('quanly/quyetdinhhuyvanbang')->name('quyetdinhhuyvanbang.')->group(function () {
        Route::post('getCapHoc', [CapHocController::class, 'getCapHoc'])->name('getCapHoc');
        Route::post('getLoaiPhoiVanBangChungChi', [LoaiPhoiVanBangChungChiController::class, 'getLoaiPhoiVanBangChungChi'])->name('getLoaiPhoiVanBangChungChi');
        Route::get('loadhinhthucxuly', [QuyetDinhHuyVanBangController::class, 'getHinhThucXuLy'])->name('loadHinhThucXuLy');
        Route::put('BanHanhQD', [QuyetDinhHuyVanBangController::class, 'BanHanhQD'])->name('BanHanhQD');
        Route::put('ThuHoiQD', [QuyetDinhHuyVanBangController::class, 'ThuHoiQD'])->name('ThuHoiQD');
        Route::post('gettrangthai', [QuyetDinhHuyVanBangController::class, 'gettrangthai'])->name('gettrangthai');
        Route::post('getchucvu', [ChucVuController::class, 'getchucvu'])->name('getchucvu');
        Route::post('getDanhSachTruongHoc_DemSoLuong', [QuyetDinhHuyVanBangController::class, 'getDanhSachTruongHoc_DemSoLuong'])->name('getDanhSachTruongHoc_DemSoLuong');
        Route::get('getYeuCauHuyVBCC', [QuyetDinhHuyVanBangController::class, 'getYeuCauHuyVBCC'])->name('getYeuCauHuyVBCC');
        Route::get('getDonYeuCauByID', [QuyetDinhHuyVanBangController::class, 'getDonYeuCauByID'])->name('getDonYeuCauByID');
    });
});
// ====================
// Test route for debugging
// ====================

// ====================
// Dynamic page routes (must be at the end)
// ====================
Route::get('/{slug}', [BienTapTrangController::class, 'showBySlug'])
    ->where('slug', '[a-z0-9\-]+')
    ->name('page.show');
